{"name": "pixel2024-worker", "version": "0.0.0", "private": true, "scripts": {"dev": "wrangler dev", "test": "echo \"Error: no test specified\" && exit 1", "db:seed": "npx ts-node seeders", "deploy": "wrangler deploy"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@cloudflare/vitest-pool-workers": "^0.8.14", "cors": "^2.8.5", "dotenv": "^16.5.0", "firebase": "^10.12.1", "googleapis": "^148.0.0", "hono": "^4.3.9", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0"}, "devDependencies": {"@types/node-fetch": "^2.6.11", "node-fetch": "^3.3.2", "ts-node": "^10.9.2", "wrangler": "^4.9.1"}}