{"name": "pixel2024", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "rimraf dist && astro check && astro build", "preview": "astro preview", "astro": "astro", "test": "vitest"}, "dependencies": {"@astrojs/check": "^0.5.10", "@astrojs/cloudflare": "^10.1.0", "@astrojs/react": "^3.3.0", "@astrojs/tailwind": "^5.1.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@types/react": "^18.2.79", "@types/react-dom": "^18.2.25", "aos": "^2.3.4", "astro": "^4.6.2", "astro-compressor": "^0.4.1", "blurhash": "^2.0.5", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "firebase": "^10.11.0", "firebase-admin": "^12.1.1", "flowbite": "^2.5.2", "framer-motion": "^11.2.4", "gsap": "^3.13.0", "intro.js": "^7.2.0", "node-fetch": "^3.3.2", "nodemailer": "^6.9.13", "react": "^18.3.1", "react-dom": "^18.3.1", "react-google-recaptcha": "^3.1.0", "react-lazy-load-image-component": "^1.6.0", "react-markdown": "^10.1.0", "rimraf": "^5.0.5", "save": "^2.9.0", "shiki": "^1.3.0", "sweetalert2": "^11.10.7", "swiper": "^11.1.1", "tailwindcss": "^3.4.3", "typescript": "^5.4.5", "yup": "^1.4.0", "zustand": "^4.5.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/react-lazy-load-image-component": "^1.6.4", "daisyui": "^4.10.2", "vite-test-utils": "^0.6.0", "vitest": "^1.5.0"}}