---
const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const desc1 =
  `Project Innovation & eXploration in CS Education and Learning (PIXEL) ${currentYear} is a dynamic platform that is held annually, specially curated for final year Computer Science students to showcase their creative and innovative final year projects. PIXEL ${currentYear} is organised by the Computer Science Society in collaboration with the School of Computer Sciences, Universiti Sains Malaysia.`;

const desc2 =
  `PIXEL ${currentYear} ensures that students get valuable feedback from industry professionals, enhance their skills and be well-equipped for future challenges in the tech world. PIXEL ${currentYear} serves as a podium for students to explore and delve into varieties of ideas and innovations and discover beyond the horizons.`;

const categories = [
  {
    name: "Software Engineering (SE)",
    icon: "fa-solid fa-circle-nodes text-[16px] md:text-[24px]",
  },
  {
    name: "Intelligent Computing (IC)",
    icon: "fa-solid fa-robot text-[16px] md:text-[24px]",
  },
  {
    name: "Computing Infrastructure (CI)",
    icon: "fa-solid fa-building-lock text-[16px] md:text-[24px]",
  },
];

---

<div
  class="w-full flex max-lg:flex-col justify-center items-stretch relative gap-10 xl:gap-8"
>
  <!-- about pixel card -->
  <div class="bg-white w-full rounded-md shadow-lg">
    <!-- waves background -->
    <div
      class="relative bg-img w-full py-8 px-6 md:py-12 md:px-8 before:z-[-1]"
    >
      <div class="w-full flex flex-col z-[1] relative">
        <p
          class="text-primary-linear font-semibold text-mobile-18 md:text-tablet-20 xl:text-desktop-24"
        >
          WHAT IS PIXEL?
        </p>
        <h2 class="text-mobile-34 md:text-tablet-48 xl:text-desktop-60 mt-1">
          FINAL YEAR PROJECT SHOWCASE
        </h2>
        <div class="w-full mt-6 md:mt-8 text-justify">
          <p>{desc1}</p>
          <br />
          <p>{desc2}</p>

          <p class="mt-6 md:mt-8">Project Categories Available:</p>
          <div
            class="mx-auto w-full flex max-md:flex-col justify-center items-center gap-2 xl:gap-1 mt-4"
          >
            {
              categories.map((category) => (
                <>
                  <a
                    href={
                      !isPostEvent
                        ? `/awards/${isPostEvent ? currentYear : parseInt(currentYear) - 1}/${category.name.split(" (")[0]}`
                        : `/projects/${category.name.split(" (")[0]}`
                    }
                    class="w-full md:w-fit p-4 flex xl:flex-col grow gap-2 items-center md:justify-center xl:items-start bg-grey-4 text-white rounded-tr-md transition hover:bg-[#000000bb]"
                  >
                    <i class={`flex size-5 md:size-7 ${category.icon}`} />
                    <div class="vl xl:hidden" />
                    <p class="text-mobile-14 text-white md:w-fit md:max-w-[14ch]">
                      {category.name}
                    </p>
                  </a>
                  <p />
                </>
              ))
            }
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- venue card -->
  <div
    class="bg-white w-full rounded-md shadow-lg flex min-h-[440px] overflow-hidden"
  >
    <!-- venue background -->
    <div
      class="relative flex bg-venue max-md:before:bottom-[-20px] before:bg-center before:bg-cover before:w-full before:h-full md:max-lg:before:bg-right lg:before:bg-bottom lg:before:bottom-0 md:before:bg-contain w-full py-8 px-6 md:py-12 md:px-8 min-h-[440px] h-full"
    >
      <div
        class="relative w-full flex flex-col justify-between h-full min-h-[392px]"
      >
        <div>
          <p
            class="text-primary-linear font-semibold text-mobile-18 md:text-tablet-20 xl:text-desktop-24"
          >
            AWARDS & CLOSING CEREMONY
          </p>
          <h2
            class="text-mobile-24 md:text-tablet-34 lg:text-desktop-34 md:max-w-[500px] mt-1"
          >
            DEWAN PEMBANGUNAN SISWA 1<span
              class="text-grey-4 text-mobile-24 md:text-tablet-34 block"
              >UNIVERSITI SAINS MALAYSIA</span
            >
          </h2>
        </div>
        <div
          class="flex max-xl:flex-col *:bg-primary-6 *:text-primary-1 *:p-2 md:*:px-4 *:rounded-md gap-2 *:!font-semibold *:text-mobile-14 md:*:text-tablet-20 lg:*:text-desktop-20 *:w-fit max-xl:justify-self-end h-fit xl:mt-4"
        >
          <div>1ST JULY 2025</div>
          <div>9:00 AM - 1:00 PM (GMT+8)</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-venue::before {
    content: "";
    position: absolute;
    z-index: 0;
    left: 0;
    /* bottom: -20px; */
    /* width: 100%; */
    height: 100%;
    background-image: url("/assets/images/venue.webp");
    background-repeat: no-repeat;
    /* background-size: cover;
    background-position: center; */
  }

  .vl {
    border-left: 1px solid white;
    height: 25px;
    position: relative;
  }
</style>
