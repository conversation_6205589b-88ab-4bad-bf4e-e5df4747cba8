// Used by Google Sheet action buttons

// Fetch Airtable Participants (Dev)
function updateFYP_Dev() {
  updateFYPStudentsSheet(false);
}

// Fetch Airtable Participants (Prod)
function updateFYP_Prod() {
  updateFYPStudentsSheet(true);
}

// Fetch Airtable Evaluations (Prod)
function updateEvaluations_Prod() {
  updateEvaluationsSheet(true);
}

// Fetch Airtable Evaluations (Dev)
function updateEvaluations_Dev() {
  updateEvaluationsSheet(false);
}

// Update Normalization Sheet
updateEvaluationNormalization;

// Update Final Marks Sheet
updateFinalMarks;

// Compute Normalized Marks
function computeNormalizedMarks() {
  updateEvaluationNormalization();
  updateFinalMarks();
}

// Clear fyp_students Sheet
function clearFYPStudentsSheet() {
  clearSheetDataOnly("fyp_students");
}

// Clear evaluations Sheet
function clearEvaluationsSheet() {
  clearSheetDataOnly("evaluations");
}

// Clear normalization Sheet
function clearNormalizationSheet() {
  clearSheetDataOnly("normalization");
}

// Clear final_marks Sheet
function clearFinalMarksSheet() {
  clearSheetDataOnly("final_marks");
}

// Convert JSON Data
convertJobsJSONtoSheet;

// Generate Job Applications
generateJobApplicationsSheet