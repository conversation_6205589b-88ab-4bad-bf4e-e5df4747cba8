// Merge the exports from other files in this folder
// and the Session Storage file in this file

export * from "./bloc";
export * from "./types";
// This file is mainly used to setup and set up the session storage
// Also include the functions of email verification

import Swal from "sweetalert2";
import { widget } from "@/utils/widget";
import { judgeFactory } from "@modules/judge";

// this brings in the self-declared data types
import type { Credential, ParticipantSession, JudgeSession } from "./types";
import { getSessionStorage, setSessionStorage } from "@/utils/common";

// import { logout } from "./db";
import { actionSessionStorage } from "@utils/utils";
import { sendEmailVerification, User } from "firebase/auth";
import { participantFactory } from "@modules/participant";

import pixelAdmin from "@data/admins.json";
import pixelJudges from "@data/judges.json";
import { proxyApi } from "@utils/proxyApi";

const cloudFunction = "/api/auth/verifyRecaptcha";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
let isJudging = import.meta.env.PUBLIC_PIXEL_IS_JUDGING === "true";

export const getUserSession = (): ParticipantSession | null => {
  return actionSessionStorage().get("userData");
};

export const updateUserSession = (userInfo: ParticipantSession) => {
  actionSessionStorage().create("userData", userInfo);
};

export const getJudgeSession = (): JudgeSession | null => {
  return actionSessionStorage().get("judgeData");
};

export const updateJudgeSession = (judgeInfo: JudgeSession) => {
  actionSessionStorage().create("judgeData", judgeInfo);
};

export const isUserJudgeAndStoreToSessionStorage = async (
  user: User,
  email: string,
  uid: string,
  page: string
): Promise<void> => {
  try {
    let isAdmin = pixelAdmin.includes(email.toLowerCase());
    let isJudge = pixelJudges.includes(email.toLowerCase());

    if (isAdmin) {
      // setTimeout(() => {
      //   window.location.href = "/judge";
      // }, 1000);

      // Redirect to admin dashboard
      setTimeout(() => {
        window.location.href = "/admin";
      }, 1000);
      widget.alertSuccess(`Hello Admin`, "Login successful!");
      return;
    } else if (isJudge) {
      if (page === "login") {
        // The judges is allowed to login during the judging phase
        if (isJudging) {
          const judgeData = await judgeFactory().getJudge(uid, currentYear);

          const judgeInfo: JudgeSession = {
            id: judgeData.id ?? "",
            firebase_uid: uid ?? "",
            name: judgeData.name ?? "",
            salutation: judgeData.salutation ?? "",
            organization: judgeData.organization ?? "",
            position: judgeData.position ?? "",
            email: judgeData.email ?? "",
            linkedin: judgeData.linkedin ?? "",
            country: judgeData.country ?? "",
            major: judgeData.major ?? "",
            assigned_projects_id: judgeData.assigned_projects_id ?? [],
            assigned_projects_year: judgeData.assigned_projects_year ?? [],
            project_name: judgeData.project_name ?? [],
            project_major: judgeData.project_major ?? [],
            sdg: judgeData.sdg ?? [],
            evaluation_id: judgeData.evaluation_id ?? [],
            evaluated_projects_name: judgeData.evaluated_projects_name ?? [],
            evaluated_projects_year: judgeData.evaluated_projects_year ?? [],
            evaluated_projects_id: judgeData.evaluated_projects_id ?? [],
          };

          //  console.log("Show the judge info before stored in session storage: ", judgeInfo);
          actionSessionStorage().create("judgeData", judgeInfo);
          //  console.log("Judge info stored in session storage successfully");

          setTimeout(() => {
            window.location.href = "/judge";
          }, 1000);
          widget.success("Login successful!");
          return;
        } else {
          widget.error(
            "Error: You cannot access to Judge Dashboard page during non-judging phase."
          );
          return;
        }
        // }
      } else {
        widget.error(
          "Error: Judge is not allowed to register themselves. <NAME_EMAIL> for further assistance"
        );
        return;
      }
    } else {
      // Participant
      // console.log("hello");
      // put the firebase uid as the parameter and send it to the fetchParticipantInfoInDb function
      const userData = await participantFactory().getParticipant(uid);
      // cases where the user did not complete registration after email verification
      if (userData === null && user.emailVerified === true) {
        window.location.href = "/register";
        return;
      }

      // ParticipantSession is a self-declared structure
      const userInfo: ParticipantSession = {
        id: userData.id ?? "",
        firebase_uid: userData.firebase_uid ?? "",

        student_name: userData.student_name ?? "",
        display_name: userData.display_name ?? "",
        matric_no: userData.matric_no ?? "",
        ic: userData.ic ?? "",
        student_email: userData.student_email ?? "",
        student_major: userData.student_major ?? "",
        desc: userData.desc ?? "",

        personal_email: userData.personal_email ?? "",
        tel: userData.tel ?? "",
        discord_id: userData.discord_id ?? "",
        github: userData.github ?? "",
        linkedin: userData.linkedin ?? "",
        portfolio: userData.portfolio ?? "",

        agreement: userData.agreement ?? false,
        created_at: userData.created_at ?? "",
        updated_at: userData.updated_at ?? "",

        photo_agreement: userData.photo_agreement ?? false,
        photo: userData.photo ?? "",

        resume_agreement: userData.resume_agreement ?? false,
        resume: userData.resume ?? "",

        is_team: userData.is_team ?? false,

        year: userData.year ?? "",

        is_special_access_granted: userData.is_special_access_granted ?? false,

        project_id: userData.project_id?.[0] ?? "",
        project_name: userData.project_name?.[0] ?? "",
        project_major: userData.project_major?.[0] ?? "",
        sdg: userData.sdg?.[0] ?? "",
        job_application_id: userData.job_application_id ?? "",
      };
      // console.log("User Info: ", userInfo);

      if (userData.is_verified === false) {
        const result = await widget.confirm(
          "You haven't verified your email. Please check your inbox or spam folder. Do you want to resend the verification email?",
          "Yes, resend it"
        );

        if (result.isConfirmed) {
          const actionCodeSettings = {
            url: window.location.origin + "/register",
            handleCodeInApp: true,
          };

          await sendEmailVerification(user, actionCodeSettings);
        }

        return;
      }

      actionSessionStorage().create("userData", userInfo);

      // Redirect to participant dashboard
      setTimeout(() => {
        window.location.href = "/participant";
      }, 1000);
      widget.success("Login successful!");
      return;
    }
  } catch (error) {
    widget.error("User does not exist<br>" + error);
    throw new Error("Error in isUserJudgeAndStoreToSessionStorage: " + error);
  }
};

export const unauthorizedRedirect = async () => {
  if (
    (!getUserSession() && window.location.pathname.startsWith("/dashboard")) ||
    (!getJudgeSession() && window.location.pathname.startsWith("/judge/"))
  ) {
    widget.toast(
      "Unauthorized access to " + window.location.pathname + " page.",
      "error"
    );

    // wait for 3 seconds
    await new Promise((resolve) => setTimeout(resolve, 3100));
    window.location.href = "/login";
  }
};

export const checkRecaptcha = async (token: string | null) => {
  widget.loading("Validating user's info...");
  try {
    const csrfToken = await proxyApi.getCSRFToken();

    const response = await fetch(`${cloudFunction}?response=${token}`, {
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": csrfToken,
      },
    });

    if (response.ok) {
      Swal.close();
      return true;
    } else {
      const message = await response.json();
      widget.error("Captcha verification failed. " + message.message);
      return false;
    }
  } catch (error) {
    widget.error("Error during captcha verification" + error);
    return false;
  }
};

// export const convertRegisterDataToParticipantInfo: (registerData: SignUp, is_verified: boolean) => ParticipantSession = (registerData, is_verified) => {
//     return {
//         airtable_id: registerData.airtable_id,
//         firebase_uid: registerData.firebase_uid,

//         name: registerData.name,
//         matric_no: registerData.matric_no,
//         major: registerData.major,
//         ic: registerData.ic,
//         desc: registerData.desc,
//         photo: registerData.photo,
//         agreement: registerData.agreement,
//         is_verified: is_verified,

//         email: registerData.email,
//         github: registerData.github,
//         linkedin: registerData.linkedin,
//         portfolio: registerData.portfolio,
//         tel: registerData.tel,
//         discord: registerData.discord,

//         created_at: new Date().toISOString(),
//         updated_at: new Date().toISOString(),

//         project_id: "",
//         project_name: "",
//         project_major: "",
//         sdg: "",
//     };
// };
