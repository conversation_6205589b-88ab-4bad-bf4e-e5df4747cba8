import { useEffect, useState } from "react";
import PastWinnerCard from "@components/landing/PastWinnerCard";
import { Project } from "@modules/project/types";
import { proxyApi, mapProject } from "@/utils";

const PastWinners = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [bestProjects, setBestProjects] = useState<any[]>([]);
  const [bestPresenter, setBestPresenter] = useState<any | null>(null);

  const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
  const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

  useEffect(() => {
    (async () => {
      const result = await proxyApi.get("projects", {
        filterByFormula: `AND(year="${isPostEvent ? currentYear : parseInt(currentYear) - 1}",
          OR(
            FIND("Best Project", {awards}),
            FIND("Best Presenter", {awards})
          )
        )`,
      });

      let data = result.map((record: any) => {
        return mapProject(record);
      }) as Project[];

      setBestProjects(
        data.filter(
          (project: any) =>
            project.awards && project.awards.includes("Best Project")
        )
      );

      setBestPresenter(
        data.filter(
          (project: any) =>
            project.awards && project.awards.includes("Best Presenter")
        )
      );

      setIsLoading(false);
    })();
  }, []);

  return (
    <>
      <h2 className="text-white text-mobile-34 md:text-tablet-48 lg:text-desktop-60">
        PIXEL
        {isPostEvent ? ` ${currentYear} ` : ` ${parseInt(currentYear) - 1} `}
        WINNERS
      </h2>
      <p className="text-primary-3 mt-6 md:mt-8 text-justify">
        Each year, hundreds of Final Year Students from USM present projects
        that showcase their creativity, collaboration, and potential to address
        global challenges. With a consistent focus on tackling the United
        Nations Sustainable Development Goals (SDGs), these projects aim to
        create solutions for pressing issues on Earth and beyond. They represent
        the most innovative and impactful contributions from the USM Computer
        Science community.
      </p>
      <h3 className="text-white !font-semibold text-mobile-20 md:text-tablet-24 lg:text-desktop-34 mt-10 md:mt-14 lg:mt-12">
        BEST PROJECT AWARD
      </h3>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-8 gap-8 *:min-w-[240px]">
        {!isLoading
          ? bestProjects.map((project) => (
              <PastWinnerCard project={project} key={project.id} />
            ))
          : Array.from({ length: 3 }).map((_, index) => (
              <div className="w-full h-[240px] bg-white rounded-md" key={index}>
                <div className="animate-pulse flex flex-col h-full">
                  <div className="bg-grey-2 w-full h-[200px]" />
                  <div className="py-3 px-4 h-[40px] flex justify-center items-center">
                    <div className="rounded h-2 w-full bg-grey-2 " />
                  </div>
                </div>
              </div>
            ))}
      </div>
      <h3 className="text-white !font-semibold text-mobile-20 md:text-tablet-24 lg:text-desktop-34 mt-10 md:mt-14 lg:mt-12">
        BEST PRESENTER AWARD
      </h3>
      <div className="grid md:grid-cols-2 lg:grid-cols-3 mt-6 lg:mt-8 gap-8 *:min-w-[240px]">
        {!isLoading ? (
          bestPresenter.map((project) => (
            <PastWinnerCard project={project} key={project.id} />
          ))
        ) : (
          <div className="w-full h-fit bg-white rounded-md">
            <div className="animate-pulse flex flex-col h-full">
              <div className="bg-grey-2 w-full min-h-[200px]" />
              <div className="py-3 px-4 h-[40px] flex justify-center items-center">
                <div className="rounded h-2 w-full bg-grey-2 " />
              </div>
            </div>
          </div>
        )}
        <a
          href="/awards"
          className="flex md:flex-col gap-2 justify-center items-center bg-white transition hover:bg-primary-6 group ease-in-out rounded-md px-2 max-md:py-4 md:px-4"
        >
          <p className="!font-medium group-hover:text-white max-md:leading-tight text-desktop-16 md:text-tablet-20 lg:text-desktop-20 inline-block text-center max-md:max-w-[20ch]">
            Discover more award winning projects here
          </p>
          <i className="fa-solid fa-arrow-right group-hover:text-white lg:text-2xl"></i>
        </a>
      </div>
    </>
  );
};

export default PastWinners;
