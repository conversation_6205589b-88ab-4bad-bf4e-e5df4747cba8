function updateEvaluationNormalization() {
  /**
   *
   * @desc
   * This function calculates normalization statistics for each project:
   * mean, standard deviation, z-scores and project_z_sum from the evaluations sheet.
   * 
   * The normalized data is written to the normalization sheet.
   * 
   */

  Logger.log("Updating normalization sheet ...");

  const evalSheetName = 'evaluations';
  const normSheetName = 'normalization';

  const evalSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName(evalSheetName);
  const normSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName(normSheetName);

  // List of evaluation criteria used to calculate z-scores
  const criteriaFields = [
    'problem_statement',
    'solution',
    'innovativeness',
    'impact_benefit',
    'practicality',
    'content_clarity',
    'presentation',
    'innovation_creativity',
    'entrepreneurship',
  ];

  // Base metadata fields to include before scores
  const baseFields = [
    'judge_id',
    'judge_salutation',
    'judge_name',
    'project_id',
    'project_name',
    'project_major',
    'sdg'
  ];

  // Fetch headers and data
  const headers = evalSheet.getRange(1, 1, 1, evalSheet.getLastColumn()).getValues()[0];
  const data = evalSheet.getRange(2, 1, evalSheet.getLastRow() - 1, headers.length).getValues();
  const colIndex = name => headers.indexOf(name);

  const resultRows = [];

  // Build raw result rows (no stats yet)
  data.forEach(row => {
    const result = [
      ...baseFields.map(f => row[colIndex(f)]),
      ...criteriaFields.map(f => row[colIndex(f)])
    ];
    resultRows.push(result);
  });

  // Combine results per judge
  const mergedByJudge = {};

  // Loop through each row and group by judge_id
  resultRows.forEach(row => {
    const judgeId = row[0]; // judge_id is at index 0

    // If this is the first project for the judge:
    if (!mergedByJudge[judgeId]) {
      mergedByJudge[judgeId] = [...row]; // make a copy
      // convert all mergeable fields to arrays (starting from project_id onwards)
      // i = 3 because 'project_id' is located at index 3
      // row.length = baseFields.length (7) + criteriaFields.length (9) = 16
      for (let i = 3; i < row.length; i++) {
        mergedByJudge[judgeId][i] = [row[i]];
      }
    } else {
      // If this judge has been seen before
      for (let i = 3; i < row.length; i++) {
        mergedByJudge[judgeId][i].push(row[i]);
      }
    }
  });

  const finalMergedRows = [];

  // Compute mean, std, z-score, and z-sum for each judge's evaluations
  for (let judgeId in mergedByJudge) {
    const row = mergedByJudge[judgeId];
    // baseFields.length = 7
    // start from index after baseFields means index 0 at criteriaFields of row
    // Final output:
    // [
    //   [4, 1],              // problem_statement scores
    //   [10, 1],             // solution scores
    //   [10, 1],             // innovativeness scores
    //   ...
    // ]
    const scores = criteriaFields.map((_, idx) => row[baseFields.length + idx].map(Number)); // numeric scores only

    const projectCount = scores[0].length;
    const meanList = [], stdList = [], zList = [];

    for (let i = 0; i < criteriaFields.length; i++) {
      const values = scores[i];
      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const std = Math.sqrt(values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length);

      meanList.push(mean.toFixed(2));
      stdList.push(std.toFixed(2));

      const zScores = values.map(v => std === 0 ? 0 : ((v - mean) / std).toFixed(2));
      zList.push(zScores);
    }

    // Transpose zList back to per-project and calculate z-sum
    const zPerProject = Array(projectCount).fill().map(() => []);
    for (let i = 0; i < criteriaFields.length; i++) {
      for (let j = 0; j < projectCount; j++) {
        zPerProject[j].push(parseFloat(zList[i][j]));
      }
    }

    const zScoreStrs = zList.map(zs => zs.join(';'));
    const zSumStrs = zPerProject.map(zs => zs.reduce((a, b) => a + b, 0).toFixed(2)).join(';');

    // Push final row with proper semicolon joins for array fields
    finalMergedRows.push([
      row[0], // judge_id
      row[1], // judge_salutation
      row[2], // judge_name
      ...row.slice(3, 7).map(arr => arr.join(';')), // project_id, name, major, sdg
      ...row.slice(7, 7 + criteriaFields.length).map(arr => arr.join(';')), // scores
      ...meanList,
      ...stdList,
      ...zScoreStrs,
      zSumStrs
    ]);
  }

  // Define output headers
  const outputHeaders = [
    ...baseFields,
    ...criteriaFields,
    ...criteriaFields.map(f => `${f}_mean`),
    ...criteriaFields.map(f => `${f}_std`),
    ...criteriaFields.map(f => `${f}_z`),
    'project_z_sum'
  ];

  // Clear sheet content but keep structure
  clearSheetDataOnly(normSheetName);
  clearSheetDataOnly("final_marks"); // Require re-computation of final marks

  // Write headers and merged + normalized data
  normSheet.getRange(1, 1, 1, outputHeaders.length).setValues([outputHeaders]);
  normSheet.getRange(2, 1, finalMergedRows.length, outputHeaders.length).setValues(finalMergedRows);

  Logger.log("✅ Normalized data written to sheet.");
}
