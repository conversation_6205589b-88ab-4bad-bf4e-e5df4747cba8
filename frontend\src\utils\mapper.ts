import { Participant } from "@modules/participant";
import { Project } from "@modules/project";

function mapProject(project: any) {
  const {
    thumbnail,
    highlights,
    slide,

    leader_id,
    leader_name,

    supervisor_id,
    supervisor_name,
    supervisor_profile_link,

    members_id,
    members_email,
    members_name,
    members_firebase_uid,
    members_photo,
    ...otherFields
  } = project;

  return {
    thumbnail: thumbnail ? thumbnail : "",
    highlights: highlights ? (Array.isArray(highlights) ? highlights : []) : [],
    slide: slide ? slide : "",
    leader: {
      id: leader_id ? leader_id[0] : "",
      name: leader_name ? leader_name[0] : "",
    },
    supervisor: {
      name: supervisor_name ? supervisor_name[0] : "",
      profile_link: supervisor_profile_link ? supervisor_profile_link[0] : "",
    },
    team: {
      members_id: members_id,
      members_firebase_uid: members_firebase_uid,
      members_email: members_email,
      members_name: members_name,
      members_photo: members_photo,
    },
    ...otherFields,
  } as Project;
}

// function mapParticipant(participant: any) {
//   const { photo, ...otherFields } = participant;

//   //  console.log(photo)

// 	return {
// 		photo: photo,
// 		...otherFields,
// 	} as Participant;
// }

// function mapTeam

function mapSdg(sdg: any) {
  if (!sdg) return "Unknown";

  sdg = parseInt(sdg);

  if (sdg >= 1 && sdg <= 17) {
    const sdgNames = [
      "1 No Poverty",
      "2 Zero Hunger",
      "3 Good Health and Well-Being",
      "4 Quality Education",
      "5 Gender Equality",
      "6 Clean Water and Sanitation",
      "7 Affordable and Clean Energy",
      "8 Decent Work and Economic Growth",
      "9 Industry, Innovation and Infrastructure",
      "10 Reduced Inequalities",
      "11 Sustainable Cities and Communities",
      "12 Responsible Consumption and Production",
      "13 Climate Action",
      "14 Life Below Water",
      "15 Life On Land",
      "16 Peace, Justice and Strong Institutions",
      "17 Partnerships for the Goals",
    ];

    return sdgNames[sdg - 1] || "Unknown";
  } else {
    return sdg;
  }  
};

export { mapProject, mapSdg };
