import { createContext } from "react";
import { Job } from "@/modules";

type CareerContextType = {
  sort: string;
  updateSort: (value: string) => void;
  openModal: (id: string) => void;
  jobs: Job[];
  updateFilters: (filters: { mode: string; type: string; location: string }) => void;
};

export const careerContext = createContext<CareerContextType>({
  sort: "default",
  updateSort: () => {},
  openModal: () => {},
  jobs: [],
  updateFilters: () => {},
});