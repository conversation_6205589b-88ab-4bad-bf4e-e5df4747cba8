import { useState } from "react";
import ProjectModal from "@components/projects/ProjectModal";
import ProjectSubmissionView from "./ProjectSubmissionView";
import { Project } from "@modules/index";
import { map } from "astro/zod";
import { mapSdg } from "@utils/mapper";

const labels = [
  "Selected Sustainable Development Goals",
  "Supervisor Name",
  "Project Name",
  "Project Description",
  "Presentation Link",
  "Presentation Slide",
  "Project Thumbnail",
  "Project Higlights",
  "Project Tags",
];

const rubric = "https://drive.google.com/uc?id=1PvpRROSnoPZ5ZsVmnG44WDTUD1XYOa31";

type props = {
  project: Project;
  loading: boolean;
  pageMode: string;
};

export default function ProjectNavbar({ project, loading, pageMode }: props) {
  const [isOpen, setIsOpen] = useState(false);

  const showProject = () => {
    document.body.style.overflow = "hidden";
    setIsOpen(true);
  };

  const closeProject = () => {
    document.body.style.overflow = "auto";
    setIsOpen(false);
  };

  const [activeIndex, setActiveIndex] = useState(null);

  if (loading) return null;

  project.sdg = mapSdg(project.sdg);

  return (
    <>
      {isOpen && (
        <ProjectModal onClose={closeProject} id={project.id}>
          <ProjectSubmissionView project={project} onClose={closeProject} />
        </ProjectModal>
      )}
      <div className="w-full top-0 sticky z-20 bg-white px-5 md:px-10 shadow-md md:rounded-t-md">
        {/* breadcrumb */}
        <div className="flex gap-2 py-3 items-center">
          <div className="flex text-ellipsis whitespace-nowrap max-md:gap-2 items-center">
            <a
              href={pageMode === "evaluation" ? "/judge" : "/participant"}
            >
              <i
                title="Back to Dashboard"
                className="relative fa-solid fa-arrow-left flex justify-center items-center max-md:size-8 size-6 md:-ml-8  text-xs rounded-full border border-grey-4 transition duration-100 cursor-pointer hover:bg-primary-2 hover:border-primary-6 hover:text-primary-6"
              ></i>
            </a>
            <a
              href={pageMode === "evaluation" ? "/judge" : "/participant"}
              title="Back to Dashboard"
              className="hover:underline"
            >
              Dashboard
            </a>
          </div>
          <i className="text-primary-6  fa-solid fa-caret-right fa-sm"></i>
          <p className="text-primary-6 font-medium">
          {pageMode === "evaluation" ? "Project Evaluation" : "Project Submission"}
          </p>
        </div>

        <hr></hr>
        <div id="navbar" className="py-2">
          <p className="text-xs opacity-50">Click number for quick navigate</p>
          <div className="flex justify-between items-center mt-1">
            <div className="flex gap-2 md:gap-3 text-sm flex-wrap">
              {[...Array(9)].map((_, i) => {
                const isActive = i === activeIndex;
                const bgColor = isActive ? "bg-black" : "bg-grey-2";
                const hoverColor = isActive
                  ? "hover:bg-[#464343]"
                  : "hover:bg-gray-400";
                const textColor = isActive ? "text-white" : "text-black";

                return (
                  <a
                    key={i + 1}
                    href={`#${i + 1}`}
                    className={`size-7 md:w-7 md:h-7  md:text-base flex justify-center items-center rounded-full ${bgColor} ${hoverColor} ${textColor} transition duration-100 cursor-pointer`}
                    title={labels[i]}
                    onClick={() => setActiveIndex(i)}
                  >
                    {i + 1}
                  </a>
                );
              })}
              {/* {pageMode === "evaluation" && (
              <a
                className="w-fit h-5 md:h-7 px-4 text-xs md:text-base flex justify-center items-center rounded-full bg-gray-300 hover:bg-gray-400 transition duration-100 cursor-pointer"
                href="#evaluate"
                title="evaluate"
              >
                Evaluate
              </a>
            )} */}
            </div>
            {pageMode === "edit" && (
              <button
                className="px-4 py-2 border-primary-6 border-[1px] bg-white hover:bg-primary-1 hover:border-primary-7 text-primary-6 hover:text-primary-7 group rounded cursor-pointer shadow duration-100 transitionr"
                onClick={showProject}
              >
                Preview
              </button>
            )}
            {pageMode === "evaluation" && (
              <a
                className="px-4 py-2 border-primary-6 border-[1px] bg-white hover:bg-primary-1 hover:border-primary-7 text-primary-6 hover:text-primary-7 group rounded cursor-pointer shadow duration-100 transitionr"
                href={rubric}
                target="_blank"
                rel="noreferrer"
              >
                <p >Judging Rubric</p>
              </a>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
