---
import "../layouts/layout.css";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
		<meta name="description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society">
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

		<meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025">
    <meta property="og:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects.">
    <meta property="og:type" content="website" /> 
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp">
    <meta property="og:url" content="https://pixelusm.com/">

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta name="twitter:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp"/>
    
		<meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
		<link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/assets/favicon/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/assets/favicon/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/assets/favicon/favicon-16x16.png">
		<link rel="manifest" href="/assets/favicon/site.webmanifest">

    <!-- FontAwesome cdn css -->
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css">

    <meta name="generator" content={Astro.generator} />
    <title>Page Not Found | PIXEL {currentYear}</title>
  </head>
  <body>
    <main class="h-screen">
      <div class="absolute w-full top-1/2 -translate-y-1/2 text-center z-50">
        <p class="text-mobile-48 md:text-tablet-72 lg:text-desktop-96 mb-2">404</p>
        <h1 class="text-mobile-18 md:text-tablet-20 lg:text-desktop-24 text-black/80 font-bold">
          Page Not Found
        </h1>
        <p class="text-grey-5 italic mb-8">You seem lost.</p>
        <a href="/" class="text-blue-700 underline px-5 py-4">Back to Homepage</a>
      </div>

    </main>
  </body>
  <script src="../../node_modules/flowbite/dist/flowbite.min.js"></script>
</html>
