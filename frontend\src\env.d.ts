/// <reference types="astro/client" />
/// <reference path="../.astro/types.d.ts" />

interface ImportMetaEnv {
  readonly PUBLIC_ENCRYPTION_KEY: string;
  readonly PUBLIC_PROJECT_KEY: string;
  readonly PUBLIC_BLOCKPATH: string;
  readonly PUBLIC_TEAM_LIMIT: string;
  readonly PUBLIC_RECAPTCHA_SITEKEY: string;
  readonly PUBLIC_FIREBASE_APIKEY: string;
  readonly PUBLIC_FIREBASE_AUTHDOMAIN: string;
  readonly PUBLIC_FIREBASE_PROJECTID: string;
  readonly PUBLIC_FIREBASE_STORAGEBUCKET: string;
  readonly PUBLIC_FIREBASE_MESSAGINGSENDERID: string;
  readonly PUBLIC_FIREBASE_APPID: string;
  readonly PUBLIC_FIREBASE_MEASUREMENTID: string;
  readonly PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH: string;
  readonly PUBLIC_PIXEL_IS_REGISTRATION: string;
  readonly PUBLIC_PIXEL_IS_JUDGING: string;
  readonly PUBLIC_PIXEL_IS_POST_SUBMISSION: string;
  readonly PUBLIC_PIXEL_IS_POST_EVENT: string;
  readonly PUBLIC_PIXEL_IS_PRODUCTION: string;
  readonly PUBLIC_PIXEL_YEAR: string;
  readonly PUBLIC_SUBMISSION_DEADLINE: string;
  readonly PUBLIC_JUDGING_DEADLINE: string

  // private env
  readonly PUBLIC_PIXEL_PASSWORD: string;
  readonly PUBLIC_RECAPTCHA_KEY: string;
  readonly PUBLIC_GDRIVE_RESUME_FOLDER: string;
  readonly PUBLIC_BLOCKMODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
