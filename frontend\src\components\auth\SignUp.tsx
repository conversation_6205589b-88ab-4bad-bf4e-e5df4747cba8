import { widget, auth, actionSessionStorage } from "@/utils";
import * as Yup from "yup";
import type { Credential } from "@/modules";
import { authFactory } from "@/modules";
import emails from "@data/fyp-students.json";
import admins from "@data/admins.json";
import { Timestamp } from "firebase/firestore";
import { useState, useEffect } from "react";
import { participantFactory } from "@/modules";
import { onAuthStateChanged } from "firebase/auth";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";
import judges from "@data/judges.json";

const SignUp = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    let unsub;
    // validation when entering register page
    (async function () {
      // user here is the return object from the firebase authentication method, onAuthStateChanged
      // if the user is logged in, then the user object will be returned
      // if the user is not logged in, then the user object will be null
      // used as flag
      unsub = await onAuthStateChanged(auth, async (user) => {
        // Make sure the user uid is initialized or not null all the time

        var judgesList = judges.map((judge) => judge.toLowerCase());

        const isJudge = judgesList.includes(user?.email.toLowerCase());

        const isAdmin = admins.includes(user?.email.toLowerCase());

        if (isJudge || isAdmin)
          return;

        if (unsub) {
          unsub();
        }
        if (user) {
          //uncomment this to skip email verification
          if (!user.emailVerified) {
            widget
              .alertError(
                "Email not verified",
                "Please verify your email before proceeding."
              )
              .then(() => {
                window.location.href = "/sign-in";
              });
          } else {
            // uid refers to firebase_uid
            // get the particiapnt data from the airtable(Modified). if there are data from the airtable, then consider the user has registered before
            const participant = actionSessionStorage().get("userData");
            // if there is a participant data in the airtable, meaning that the user has registered before
            if (participant) {
              widget
                .alert({
                  title: "You have logged in",
                  text: "Sign out to switch account. Redirecting to participant dashboard...",
                  icon: "info",
                })
                .then(() => {
                  window.location.href = "/participant";
                });
            }

            // but if the user hasnt registered yet (information filled out), then do nothing
            // as the userEffect is to constantly checking the state and redirect those, who have already
            // logged in, to other page.
          }
        }
      });
    })();
  }, []);

  // once the form is submitted, prevent it from deleting the existing value.
  const handleSubmit = async (e: any) => {
    e.preventDefault();
    // if the local authorized email storage does not have the the submitted email account
    // then return the alert messsage that the email there is not authorized and ends the next handleSubmit process
    if (!new Set([...emails, ...admins, ...judges]).has((e.target.email.value).toLowerCase())) {
      // e is the object in the form, retrieves it email, and get the email value
      return await widget.alertError(
        "Not Authorized",
        "You are not authorized to register an account. Only Final Year Students are allowed to register an account."
      );
    }

    // However, if the email submmitted is authorized by the local email storage, proceed to the next process

    // Help setting up the account with credentials such as email and passwords
    const signUpCredential: Credential = {
      email: e.target.email.value,
      password: e.target.password.value,
      confirm_password: e.target.cpassword.value,
    };

    // Validation Schema
    let accountSchema = Yup.object().shape<Record<keyof Credential, Yup.AnySchema>>(
      {
        email: Yup.string()
          .trim()
          .email("Please enter a valid email address")
          .matches(/^.*@student\.usm\.my$/, {
            message: "Please enter a valid USM student email address",
          })
          .required("Please enter your email address"),
        password: Yup.string().trim().required("Please enter your password"),
        confirm_password: Yup.string()
          .trim()
          .required("Please confirm your password")
          .oneOf([Yup.ref("password")], "Passwords do not match"),
      }
    );

    // validate using Yup and if error, alert error
    try {
      await accountSchema.validate(signUpCredential);

      // if valid and proceed with the sign up confirmation, register new account
      if (await signUpConfirmation()) {
        await authFactory().signUp(signUpCredential);
      } else return;
    } catch (error: any) {
      return await widget.alertError("Error", error.message);
    }
  };

  const toggleShowPassword = (e: any) => {
    if (e.target.id == "password-toggle") {
      setShowPassword(!showPassword);
      const password = document.getElementById("password") as HTMLInputElement;
      password.type = showPassword ? "password" : "text";
    } else if (e.target.id == "confirmPassword-toggle") {
      setShowConfirmPassword(!showConfirmPassword);
      const cpassword = document.getElementById(
        "cpassword"
      ) as HTMLInputElement;
      cpassword.type = showConfirmPassword ? "password" : "text";
    }
  };

  // show confirmation before proceeding
  const signUpConfirmation = async (): Promise<boolean> => {
    const confirmation = await widget.confirm(
      "Sign Up Confirmation",
      "You are not allowed to change your email address once you have signed up."
    );
    return confirmation.isConfirmed;
  };

  return (
    <div className="w-full flex h-full max-lg:h-[100dvh]">
      {/* pixie image */}
      <div className="max-lg:hidden w-[50%] border-r-2 flex border-grey-2 *:text-[100px] *:leading-none *:font-bold overflow-hidden">
        <div className="h-full self-end relative -bottom-28 hover:-bottom-16 transition-all">
          <LazyLoadImage
            src="/assets/images/logo/pixie-half.webp"
            alt="PIXEL Pixie"
            className=""
            effect="blur"
          />
        </div>
      </div>

      {/* form */}
      <div className="w-full flex flex-col justify-center lg:w-[50%] px-5 sm:px-10 sm:py-16 md:px-16 md:py-12 ">
        {/* return to home */}
        <a
          href="/"
          className="text-mobile-14 mb-10 sm:mb-5 cursor-pointer hover:underline  "
        >
          <i className="fa-solid fa-chevron-left text-sm mr-1"></i>
          <span className="text-mobile-14">Return to Home</span>
        </a>

        <div className="mb-5">
          <div className="w-[3rem] h-[3rem] my-3 mb-5 max-sm:mx-auto">
            <LazyLoadImage
              src="assets/images/logo/PIXEL-icon.webp"
              alt="pixel"
              width="100%"
              height="100%"
              effect="blur"
            />
          </div>
          <h1 className="text-2xl font-bold max-sm:text-center mb-1">
            Account Sign Up
          </h1>
          <p className="text-mobile-14 max-sm:text-center">
            Sign up with your account to get started.
          </p>
        </div>

        <form className="my-5" onSubmit={handleSubmit}>
          <div className="flex items-center input-container mb-5">
            <input
              type="text"
              id="email"
              name="email"
              className="w-full border-none px-0 pr-8"
              required
            />
            <span>Email Address</span>
            <i className="fa-solid fa-at absolute right-2 opacity-50"></i>
          </div>
          <div className="flex items-center input-container mb-5">
            <input
              type="password"
              id="password"
              name="password"
              className="w-full border-none px-0"
              required
            />
            <span>Password</span>
            <i
              className={` ${
                showPassword ? "fa-regular fa-eye" : "fa-regular fa-eye-slash"
              }  absolute right-2 z-50 text-grey-4 hover:cursor-pointer`}
              id="password-toggle"
              onClick={(e) => toggleShowPassword(e)}
            ></i>
          </div>
          <div className="flex items-center input-container mb-5">
            <input
              type="password"
              id="cpassword"
              name="cpassword"
              className="w-full border-none px-0"
              required
            />
            <span>Confirm Password</span>
            <i
              id="confirmPassword-toggle"
              className={` ${
                showConfirmPassword
                  ? "fa-regular fa-eye"
                  : "fa-regular fa-eye-slash"
              }  absolute right-2 z-50 text-grey-4 hover:cursor-pointer`}
              onClick={(e) => toggleShowPassword(e)}
            ></i>
          </div>
          <button
            type="submit"
            className="bg-primary-6 font-semibold px-5 py-3 rounded text-white hover:bg-primary-7 transition-all w-full mt-6"
          >
            SIGN UP
          </button>
        </form>

        {/* navigate to sign in */}
        <p className="text-center sm:text-left *:text-mobile-14">
          <span>Already have an account? </span>
          <a href="/sign-in" className="text-primary-6 hover:underline">
            Sign In
          </a>
        </p>
      </div>
    </div>
  );
};

export default SignUp;
