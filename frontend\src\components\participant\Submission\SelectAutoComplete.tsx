import { useState, useEffect, useRef } from "react";

export function SelectAutoComplete({ options, name, value, onChange, className }: any) {
  const suggestionsRef = useRef(null);
  const wrapperRef = useRef(null);
  const [optionInput, setOptionInput] = useState("");
  const [filteredOptions, setFilteredOptions] = useState([] as any);

  useEffect(() => {
    if (optionInput.length > 0 && !options.includes(optionInput)) {
      // filter the options based on the input
      const filteredData = options.filter((option: any) => {
        const regex = new RegExp(`${optionInput}`, "gi");
        return option.match(regex);
      });
      setFilteredOptions(filteredData);
    } else {
      setFilteredOptions([]);
    }
  }, [optionInput]);

  // Handle click outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !(wrapperRef.current as any).contains(event.target)) {
        setFilteredOptions([]);
      }
    }

    // Add event listener when component mounts
    document.addEventListener("mousedown", handleClickOutside);
    
    // Clean up event listener when component unmounts
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Close dropdown when window loses focus
  useEffect(() => {
    function handleBlur() {
      setFilteredOptions([]);
    }

    window.addEventListener("blur", handleBlur);
    return () => {
      window.removeEventListener("blur", handleBlur);
    };
  }, []);

  async function clickOption(e: any) {
    // set the input value to the selected option
    const input = document.getElementById("input") as HTMLInputElement;
    input.value = e.target.innerText;
    setOptionInput(e.target.innerText);
    // Trigger onChange event
    if (onChange) {
      onChange({ target: { value: e.target.innerText } });
    }
    // close the suggestions
    setFilteredOptions([]);
  }

  return (
    <div className="wrapper" ref={wrapperRef}>
      <section className="wrapper-input">
        <div className="relative">
          <input
            type="text"
            name={name}
            defaultValue={value ? value : ""}
            className="daisy-input w-full"
            id="input"
            placeholder={options.length === 0 ? "Loading..." : "Enter Supervisor Name"}
            onChange={(e) => {
              setOptionInput(e.target.value);
              if (onChange) {
                onChange(e);
              }
            }}
            onFocus={() => {
              if (optionInput.length > 0) {
                const filteredData = options.filter((option: any) => {
                  const regex = new RegExp(`${optionInput}`, "gi");
                  return option.match(regex);
                });
                setFilteredOptions(filteredData);
              }
            }}
          />

          <div 
            ref={suggestionsRef} 
            className={`absolute w-full bg-white border border-gray-200 rounded-md shadow-lg z-50 ${className || 'max-h-[200px] overflow-y-auto'}`}
          >
            {filteredOptions.map((option: any, key: any) => (
              <p
                className="border-b border-gray-200 bg-white hover:bg-gray-100 w-full p-2 cursor-pointer transition-colors duration-100"
                onClick={(e: any) => clickOption(e)}
                key={key}
              >
                {option}
              </p>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
