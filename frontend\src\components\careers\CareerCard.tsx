import React, { useEffect, useRef, useState } from "react";
import { Job } from "@/modules";
import { careerContext } from "./Context";
import { useContext } from "react";

type Props = {
  job: Job;
  handleJobApply: (jobId: any) => Promise<void>;
  removeJob: (jobId: any) => Promise<void>;
  isApplied: boolean;
};

const CareerCard: React.FC<Props> = ({
  job,
  handleJobApply,
  removeJob,
  isApplied,
}) => {
  const { openModal } = useContext(careerContext);

  const [isLoading, setIsLoading] = useState(false);

  // To solve aria-hidden warning
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const jobDescription = `
                  <h1 class="font-semibold text-mobile-18 md:text-xl">Job Descriptions</h1>

                  <h2 class="font-semibold text-mobile-16 md:text-lg mt-5">A. Responsibilities</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-2">
                    ${
                      job.responsibilities
                        ? job.responsibilities
                            .map(
                              (responsibility) =>
                                `<li class="text-sm md:text-base">${responsibility}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>

                  <h2 class="font-semibold text-mobile-16 md:text-lg mt-5">B. Requirements</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-1">
                    ${
                      job.requirements
                        ? job.requirements
                            .map(
                              (req) =>
                                `<li class="text-sm md:text-base">${req}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>
                  
                  <h2 class="font-semibold text-lg mt-5">C. Benefits Offered</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-1">
                    ${
                      job.benefits
                        ? job.benefits
                            .map(
                              (benefit) =>
                                `<li class="text-sm md:text-base">${benefit}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>

                  `;

  const dialogRef = useRef<HTMLDialogElement>(null);

  const handleApplyClick = async (jobId) => {
    setIsLoading(true);
    try {
      await handleJobApply(jobId); // Wait for apply to complete
    } catch (error) {}
    setIsLoading(false);
  };

  const handleUnapplyClick = async (jobId) => {
    setIsLoading(true);
    try {
      await removeJob(jobId); // Wait for unapply to complete
    } catch (error) {}
    setIsLoading(false);
  };

  return (
    <div
      // onClick={() => openModal(job.id)}
      onClick={() => {
        const modal = document.getElementById(
          `job_modal_${job.id}`
        ) as HTMLDialogElement;
        modal?.showModal();
      }}
      className="bg-white rounded-xl shadow-lg hover:shadow-xl overflow-hidden border border-grey-2 cursor-pointer transition-all duration-200 hover:scale-105"
    >
      {/* Company Logo */}
      <div className="relative h-48 bg-primary-1 flex justify-center p-6">
        <img
          src={job.company?.logo}
          alt={job.company?.name}
          className="max-h-full max-w-full object-contain"
        />
        {/* {true && ( */}
        {isApplied && (
          <div className="absolute top-3 right-3 z-10">
            <i
              className="aria-hidden fa-solid fa-circle-check text-primary-4 fa-2x"
              title="Applied!"
            ></i>
          </div>
        )}
      </div>

      {/* Job Details */}
      <div className="p-6">
        <h3 className="text-xl font-semibold text-primary-6 mb-2 line-clamp-1">
          {job.name}
        </h3>
        <p className="text-grey-4 mb-4">{job.company?.name}</p>

        {/* Job Info */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center gap-2">
            <i className="fa-solid fa-location-dot text-primary-6"></i>
            <span className="text-grey-5">{job.location}</span>
          </div>
          <div className="flex items-center gap-2">
            <i className="fa-solid fa-briefcase text-primary-6"></i>
            <span className="text-grey-5">{job.type}</span>
          </div>
          <div className="flex items-center gap-2">
            <i className="fa-solid fa-clock text-primary-6"></i>
            <span className="text-grey-5">{job.mode}</span>
          </div>
        </div>

        {/* View Details Button */}
        <button
          // onClick={() => openModal(job.id)}
          onClick={() => {
            const modal = document.getElementById(
              `job_modal_${job.id}`
            ) as HTMLDialogElement;
            modal?.showModal();
          }}
          className="w-full py-2 px-4 bg-primary-1 text-primary-6 rounded-full hover:bg-primary-2 transition-all duration-200 font-medium"
        >
          View Details
        </button>
        <dialog
          id={`job_modal_${job.id}`}
          ref={dialogRef}
          className="daisy-modal daisy-modal-bottom sm:daisy-modal-middle"
        >
          <div className="daisy-modal-box min-w-[70%]">
            <div className="daisy-modal-action sticky top-0 right-0 md:top-2 md:right-2 float-right">
              <form method="dialog">
                <button className="daisy-btn daisy-btn-sm daisy-btn-circle">
                  ✕
                </button>
              </form>
            </div>
            <div
              id="modal-content"
              className="markdown prose max-w-none"
              dangerouslySetInnerHTML={{
                __html: jobDescription || "",
              }}
            ></div>
            <div className="mx-auto w-max mt-4 flex flex-col gap-4 sm:flex-row">
              <a href={`/`}>
                <button className="daisy-btn daisy-btn-primary border-none rounded-full shadow-lg normal-case mx-auto bg-primary-4 hover:bg-primary-3 text-white text-base font-normal">
                  See Company Details
                </button>
              </a>

              <button
                onClick={
                  isApplied
                    ? () => handleUnapplyClick(job.id)
                    : () => handleApplyClick(job.id)
                }
                disabled={isLoading}
                className="daisy-btn daisy-btn-primary border-none rounded-full shadow-lg  normal-case mx-auto bg-primary-4 hover:bg-primary-3 text-white text-base font-normal px-12"
              >
                {isLoading ? (
                  <i
                    className={
                      !isClient
                        ? ""
                        : "fa-solid fa-circle-notch animate-spin ml-2 origin-center fa-lg"
                    }
                  ></i>
                ) : isApplied ? (
                  "Unapply"
                ) : (
                  "Apply"
                )}
              </button>
            </div>
          </div>
          <form method="dialog" className="daisy-modal-backdrop">
            <button>close</button>
          </form>
        </dialog>
      </div>
    </div>
  );
};

export default CareerCard;
