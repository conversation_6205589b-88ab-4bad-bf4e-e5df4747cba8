import { proxy<PERSON>pi } from "@utils/proxyApi";
import { sanitizeData } from "@utils/utils";
import { widget } from "@utils/widget";
import { Judge, Project, ParticipantSession } from "..";
import { Children } from "react";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

export function adminFactory() {
  /**
   * Get all judges data
   * @param {string} year - year to filter the data (optional)
   * @returns {Promise<Judge[]>} - all judges data
   */
  async function getJudges(year?: String): Promise<Judge[]> {
    let offset = null;
    let allRecords: Judge[] = [];

    do {
      try {
        const result = await proxyApi.get("judges", {
          filterByFormula: year
            ? `FIND("${year}", ARRAYJOIN(year))`
            : undefined,
          offset,
        });

        if (result.offset) {
          // if result.offset has value, meaning to say there are more records to fetch
          allRecords = allRecords.concat(result.data);
        } else {
          // no more records to fetch after this
          allRecords = allRecords.concat(result);
          break;
        }

        offset = result.offset;
      } catch (error) {
        throw new Error("Error in getJudges: " + error);
      }
    } while (offset);

    return allRecords.map(sanitizeData);
  }

  /**
   * Get all participants' airtable_id, names, student emails, project_names, and special access status of current year
   * @returns {Promise<any[]>} - all participants' names, student emails, project_names, and special access status of current year
   */
  async function getParticipants(): Promise<Partial<ParticipantSession>[]> {
    let offset = null;
    let allRecords: Project[] = [];

    do {
      try {
        const result = await proxyApi.get("participants", {
          filterByFormula: `year = ${currentYear}`,
          fields:
            "student_name, student_email, project_name, is_special_access_granted",
          offset,
        });

        if (result.offset) {
          // if result.offset has value, meaning to say there are more records to fetch
          allRecords = allRecords.concat(result.data);
        } else {
          // no more records to fetch after this
          allRecords = allRecords.concat(result);
          break;
        }

        offset = result.offset;
      } catch (error) {
        throw new Error("Error in getJudges: " + error);
      }
    } while (offset);

    return allRecords;
  }

  async function grantSpecialAccess(participantId: string) {
    try {
      const response = await proxyApi.patch(
        "participants",
        { is_special_access_granted: true },
        participantId
      );

      widget.alertSuccess(
        "Success",
        "Special access granted successfully",
        1500
      );
    } catch (error: any) {
      throw new Error("Error in grantSpecialAccess: " + error);
    }
  }

  async function revokeSpecialAccess(participantId: string) {
    try {
      await proxyApi.patch(
        "participants",
        { is_special_access_granted: false },
        participantId
      );

      widget.alertSuccess(
        "Success",
        "Special access revoked successfully",
        1500
      );
    } catch (error: any) {
      throw new Error("Error in revokeSpecialAccess: " + error);
    }
  }
  /**
   * Assign a specific project to a judge
   * @param {Judge} selectedJudge - selected judge to assign the project
   * @param {string} selectedProjectId - id of the selected project to assign to the judge
   */
  async function assignProject(
    selectedJudge: Judge,
    selectedProjectId: string
  ) {
    //assign selected project to selectedJudge
    if (
      selectedJudge?.assigned_projects_id &&
      selectedJudge?.assigned_projects_id.length > 0
    )
      // if judge has already been assigned projects
      selectedJudge?.assigned_projects_id.push(selectedProjectId);
    // push selectedProjectId to assigned_projects_id
    // if judge has no assigned projects
    else selectedJudge.assigned_projects_id = [selectedProjectId]; // create new array with selectedProjectId

    try {
      await proxyApi.patch(
        "judges",
        {
          assigned_projects_id: selectedJudge.assigned_projects_id,
        },
        selectedJudge.id
      );

      widget.alertSuccess("Success", "Project assigned successfully", 1500);
    } catch (error) {
      throw new Error("Error in assignProject: " + error);
    }
  }

  /**
   * Assign all projects to all judges
   * @param {Judge} selectedJudge - selected judge to assign the project
   * @param {string} selectedProjectId - id of the selected project to assign to the judge
   */
  async function assignAllProjects(judges: Judge[], projects: Project[]) {
    // Group judges by major
    const judgesByMajor = judges.reduce((acc, judge) => {
      acc[judge.major] = acc[judge.major] || [];
      acc[judge.major].push(judge);
      return acc;
    }, {});
    // Group projects by major
    const projectsByMajor = projects.reduce((acc, project) => {
      acc[project.project_major] = acc[project.project_major] || [];
      acc[project.project_major].push(project);
      return acc;
    }, {});
    // Assignments map to keep track of which projects are assigned to which judges
    const assignments = new Map();

    // Process each major separately
    for (const major in projectsByMajor) {
      const majorJudges: Judge[] = judgesByMajor[major] || [];
      const majorProjects: Project[] = projectsByMajor[major] || [];
      // Calculate judgeCount and projectCount for the current major
      const judgeCount = majorJudges.length;
      const projectCount = majorProjects.length;
      // each project assigned to 3 judges
      // max projects for each major
      const maxProjectsPerJudge = Math.ceil((projectCount * 3) / judgeCount);

      // const minProjectsPerJudge = Math.floor((projectCount * 3) / judgeCount);

      const maxJudgesPerProject = 3;

      // Criteria for a project to be assigned judges:
      // 1. The project is not assigned to any judge
      // 2. Video qc is passed

      const readyProjects = majorProjects.filter(
        (project) =>
          project.assigned_judges_id === undefined && project.video_qc
      );
      // Shuffle the arrays for random assignment

      // const shuffledProjects = readyProjects.sort(() => 0.5 - Math.random());
      const shuffledProjects = readyProjects;
      // const shuffledJudges = majorJudges.sort(() => 0.5 - Math.random());
      const shuffledJudges = majorJudges;
      // Criteria and method for a judge to be assigned projects:
      // 1. The project assigned count is at least minProjectsPerJudge, at most maxProjectsPerJudge
      // 2. Judge will only be assigned to projects with the same major
      // 3. At least one international judge will be assigned to a project with the same major

      // const internationalJudges = shuffledJudges.filter(
      //   (judge) => judge.country != "Malaysia"
      // );

      // const localJudges = shuffledJudges.filter(
      //   (judge) => judge.country == "Malaysia"
      // );

      // assign 1 international judge to each project first
      // let i = 0;
      // while (i < shuffledProjects.length) {
      //   // const judge = internationalJudges[i % internationalJudges.length];
      //   // if (!assignments.has(judge.id)) {
      //   //   assignments.set(judge.id, []);
      //   // }
      //   // if (!shuffledProjects[i].assigned_judges_id) {
      //   //   shuffledProjects[i].assigned_judges_id = [];
      //   // }
      //   // if (
      //   //   assignments.get(judge.id).length == 0
      //   // ) {
      //   //   assignments.get(judge.id).push(shuffledProjects[i].id);
      //   //   shuffledProjects[i].assigned_judges_id.push(judge.id);
      //   //   i++;
      //   // }
      //   for (let j = 0; j < internationalJudges.length; j++) {
      //     if (!assignments.has(internationalJudges[j].id)) {
      //       assignments.set(internationalJudges[j].id, []);
      //     }

      //     // check if project has no property assigned_judges_id
      //     if (
      //       !shuffledProjects[i % shuffledProjects.length].assigned_judges_id
      //     ) {
      //       shuffledProjects[i % shuffledProjects.length].assigned_judges_id =
      //         [];
      //     }

      //     if (
      //       assignments.get(internationalJudges[j].id).length
      //     ) {
      //       assignments
      //         .get(internationalJudges[j].id)
      //         .push(shuffledProjects[i % shuffledProjects.length].id);
      //       shuffledProjects[
      //         i % shuffledProjects.length
      //       ].assigned_judges_id.push(internationalJudges[j].id);
      //       i++;
      //     }
      //   }
      // }

      let i = 0;
      while (i < shuffledProjects.length * 3) {
        for (let j = 0; j < shuffledJudges.length; j++) {
          if (!assignments.has(shuffledJudges[j].id)) {
            assignments.set(shuffledJudges[j].id, []);
          }

          // check if project has no property assigned_judges_id
          if (
            !shuffledProjects[i % shuffledProjects.length].assigned_judges_id
          ) {
            shuffledProjects[i % shuffledProjects.length].assigned_judges_id =
              [];
          }

          if (
            assignments.get(shuffledJudges[j].id).length <
              maxProjectsPerJudge &&
            shuffledProjects[i % shuffledProjects.length].assigned_judges_id
              ?.length < maxJudgesPerProject &&
            !shuffledProjects[
              i % shuffledProjects.length
            ].assigned_judges_id.includes(shuffledJudges[j].id)
          ) {
            assignments
              .get(shuffledJudges[j].id)
              .push(shuffledProjects[i % shuffledProjects.length].id);
            shuffledProjects[
              i % shuffledProjects.length
            ].assigned_judges_id.push(shuffledJudges[j].id);
            i++;
          }
        }
      }
    }

    // for debugging
    // // Create a map to store the project assignment count
    // const projectAssignmentCount = new Map();

    // // Iterate through the assignments
    // assignments.forEach((projectIds, judgeId) => {
    //   projectIds.forEach((projectId) => {
    //     if (!projectAssignmentCount.has(projectId)) {
    //       projectAssignmentCount.set(projectId, 0);
    //     }
    //     projectAssignmentCount.set(
    //       projectId,
    //       projectAssignmentCount.get(projectId) + 1
    //     );
    //   });
    // });

    // // Now projectAssignmentCount contains the count of assignments for each project
    // console.log("Project assignment count:", projectAssignmentCount);

    // // get the number of projects that have been assigned judges
    // const assignedProjects = Array.from(projectAssignmentCount).filter(
    //   ([_, count]) => count > 0
    // ).length;

    // console.log(assignedProjects);

    // // show each judge and number of projects assigned in a map
    // const judgeProjects = new Map();

    // assignments.forEach((projectIds, judgeId) => {
    //   judgeProjects.set(judgeId, projectIds.length);
    // });

    // console.log(judgeProjects);

    // Convert the map into the format expected by the API
    const records = Array.from(assignments).map(([judgeId, projectIds]) => ({
      id: judgeId,
      fields: {
        assigned_projects_id: projectIds,
      },
    }));

    try {
      // patch by batches of 10 because of the Airtable API limit
      const batchSize = 10;
      const batches = Math.ceil(records.length / batchSize);

      for (let i = 0; i < batches; i++) {
        const start = i * batchSize;
        const end = start + batchSize;
        const batch = records.slice(start, end);

        await proxyApi.patch("judges", batch);
      }

      widget.alertSuccess(
        "Success",
        "All projects assigned successfully",
        1500
      );
    } catch (error) {
      throw new Error("Error in assignAllProjects: " + error);
    }
  }

  /**
   * Trasnfer a project from one judge to another
   * @param {Judge[]} judgesWithProject - all judges with their assigned projects
   * @param {string} judgeIdFrom - id of the judge to transfer the project from
   * @param {string} judgeIdTo - id of the judge to transfer the project to
   * @param {string} projectId - id of the project to transfer
   */
  async function transferProject(
    judges: Judge[],
    judgeIdFrom: string,
    judgeIdTo: string,
    projectId: string
  ) {
    // Find the judge to transfer the project from
    const judgeFrom = judges.find((judge) => judge.id === judgeIdFrom);

    // Find the judge to transfer the project to
    const judgeTo = judges.find((judge) => judge.id === judgeIdTo);

    // Find the project to transfer
    const projectIndex = judgeFrom.assigned_projects_id.indexOf(projectId);

    // Transfer the project
    judgeFrom.assigned_projects_id.splice(projectIndex, 1);
    // check if judgeTo has the field assigned_projects_id
    // if not, create a new array and push the projectId
    if (
      judgeTo?.assigned_projects_id &&
      judgeTo?.assigned_projects_id.length > 0
    )
      judgeTo.assigned_projects_id.push(projectId);
    else judgeTo.assigned_projects_id = [projectId];

    const records = [
      {
        id: judgeIdFrom,
        fields: {
          assigned_projects_id: judgeFrom.assigned_projects_id,
        },
      },
      {
        id: judgeIdTo,
        fields: {
          assigned_projects_id: judgeTo.assigned_projects_id,
        },
      },
    ];

    try {
      await proxyApi.patch("judges", records);

      widget.alertSuccess("Success", "Project transferred successfully", 1500);
    } catch (error) {
      throw new Error("Error in transferProject: " + error);
    }
  }

  return {
    getJudges,
    getParticipants,
    grantSpecialAccess,
    revokeSpecialAccess,
    assignProject,
    assignAllProjects,
    transferProject,
  };
}
