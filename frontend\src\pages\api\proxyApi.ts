// Why do we call /api/proxy<PERSON>pi instead of using the api object directly?
// Directly using the api object would expose our backend URL, which could be accessed by anyone who visits that URL, for example (https://pixel2024-worker.cssocietytech.workers.dev/).
// By calling /api/proxyApi, we ensure that users only see our Astro Server being called, thus hiding the backend url.

// It's important to note that cookies can only be set if both the server (or backend) and the client are on the same domain. In our case, our Astro Server and Astro client are on the same domain.

// To prevent unauthorized access to our Astro Server, which calls our backend, we use /api/csrf. This endpoint sets a cookie and sends it back to the client.
// The client can then call /api/proxyApi with the cookie included in the header. The server checks if the cookie on the same domain has the same value.
// This means that anyone attempting to call /api/proxyApi would need to set the cookie in our domain (which is impossible due to domain restrictions) and then call /api/proxyApi with the cookie in the header.

// head over to /testapi to try out the proxy api operations

import type { APIRoute } from "astro";
import { Api } from "@/utils";

export const POST: APIRoute = async ({ request, cookies }) => {
  const { operation, table, data, recordId, typecast, params } =
    await request.json();
  const csrfToken = cookies.get("X-CSRF-Token")?.value;
  const requestCsrfToken = request.headers.get("X-CSRF-Token");

  const api = new Api("https://pixelpandaapi.pixelusm.com");

  if (!csrfToken || csrfToken !== requestCsrfToken) {
    cookies.delete("X-CSRF-Token", { path: "/" }); // Ensure the token is deleted on mismatch as well
    return new Response(JSON.stringify({ error: "CSRF token mismatch" }), {
      status: 403,
    });
  }

  try {
    let result;
    switch (operation) {
      case "first":
        result = await api.first(table, recordId);
        break;
      case "get":
        result = await api.get(table, params);
        break;
      case "post":
        result = await api.post(table, data, typecast);
        break;
      case "put":
        result = await api.put(table, recordId, data);
        break;
      case "patch":
        result = await api.patch(table, recordId, data, typecast);
        break;
      case "delete":
        result = await api.delete(table, recordId);
        break;
      default:
        throw new Error("Invalid operation in /api/proxyApi");
    }

    // Delete the CSRF token cookie after successful operation
    cookies.delete("X-CSRF-Token", { path: "/" });

    // Assuming result is an object with 'data' and 'offset' properties
    const responsePayload: { data: any; offset?: any } = {
      data: result.data,
    };
    
    if (result.offset) {
      responsePayload.offset = result.offset;
    }

    // return new Response(JSON.stringify({ data: result.data }), {
    return new Response(
      JSON.stringify(responsePayload),
      {
        status: 200,
      }
    );
  } catch (error) {
    // Delete the CSRF token cookie even if an error occurs
    cookies.delete("X-CSRF-Token", { path: "/" });

    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
    });
  }
};
