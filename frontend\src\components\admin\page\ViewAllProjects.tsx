import { useState, useContext, useEffect } from "react";
import { AdminContext } from "../Context";
import { ProjectContext } from "@components/projects/Context";
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";
import ProjectModal from "@components/projects/ProjectModal";
import { encryptProject } from "@utils/utils";
import { projectFactory } from "@modules/project";
import Loading from "@components/general/Loading";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const ViewAllProjects = () => {
  const { judges } = useContext(AdminContext);
  const { prevProjectId, setPrevProjectId } = useContext(ProjectContext);

  const [isLoading, setIsLoading] = useState(true);
  const [allProjects, setAllProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);

  // Filter states
  const [filterDraft, setFilterDraft] = useState("all"); // "all", "draft", "notDraft"
  const [filterVideoQC, setFilterVideoQC] = useState("all"); // "all", "qc", "noQc"
  const [filterAssigned, setFilterAssigned] = useState("all"); // "all", "assigned", "notAssigned"
  const [searchQuery, setSearchQuery] = useState("");

  const judgesForCurrentYear =
    judges?.filter((judge) => judge.year.includes(currentYear)) || [];

  useEffect(() => {
    fetchAllProjects();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [allProjects, filterDraft, filterVideoQC, filterAssigned, searchQuery]);

  const fetchAllProjects = async () => {
    try {
      setIsLoading(true);
      const projects = await projectFactory().getProjects({
        year: currentYear,
        // Don't apply any filters here, we want all projects
      });
      setAllProjects(projects);
    } catch (error) {
      console.error("Error fetching projects:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...allProjects];

    // Filter by draft status
    if (filterDraft === "draft") {
      filtered = filtered.filter((project) => project.isDraft === true);
    } else if (filterDraft === "notDraft") {
      filtered = filtered.filter((project) => !project.isDraft);
    }

    // Filter by video QC status
    if (filterVideoQC === "qc") {
      filtered = filtered.filter(
        (project) => project.video_qc && project.video_qc !== ""
      );
    } else if (filterVideoQC === "noQc") {
      filtered = filtered.filter(
        (project) => !project.video_qc || project.video_qc === ""
      );
    }

    // Filter by judge assignment status
    if (filterAssigned === "assigned") {
      filtered = filtered.filter((project) =>
        judgesForCurrentYear.some((judge) =>
          judge.assigned_projects_id?.includes(project.id)
        )
      );
    } else if (filterAssigned === "notAssigned") {
      filtered = filtered.filter(
        (project) =>
          !judgesForCurrentYear.some((judge) =>
            judge.assigned_projects_id?.includes(project.id)
          )
      );
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (project) =>
          project.project_name.toLowerCase().includes(query) ||
          project.project_major.toLowerCase().includes(query) ||
          project.tech_stack.some((tech) => tech.toLowerCase().includes(query))
      );
    }

    setFilteredProjects(filtered);
  };

  const showProject = (project) => {
    setSelectedProject(project);
    setPrevProjectId(project.id);
    if (prevProjectId) {
      const prevProjectModal = document.getElementById(
        `modal-${encryptProject(prevProjectId)}`
      );
      if (prevProjectModal) {
        prevProjectModal.style.display = "none";
      }
    }
    setIsOpen(true);
    document.body.style.overflow = "hidden";
  };

  const closeProject = () => {
    setPrevProjectId("");
    document.body.style.overflow = "auto";
    setIsOpen(false);
    setSelectedProject(null);
  };

  const getAssignedJudgesCount = (projectId) => {
    return judgesForCurrentYear.filter((judge) =>
      judge.assigned_projects_id?.includes(projectId)
    ).length;
  };

  const getEvaluatedJudgesCount = (projectId) => {
    return judgesForCurrentYear.filter((judge) =>
      judge.evaluated_projects_id?.includes(projectId)
    ).length;
  };

  const getProjectStatusBadge = (project) => {
    const isDraft = project.isDraft;
    const hasVideoQC = project.video_qc && project.video_qc !== "";
    const isAssigned = getAssignedJudgesCount(project.id) > 0;

    if (isDraft) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
          Draft
        </span>
      );
    }
    if (!hasVideoQC) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
          No Video QC
        </span>
      );
    }
    if (!isAssigned) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
          Not Assigned
        </span>
      );
    }
    return (
      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
        Ready
      </span>
    );
  };

  return (
    <>
      {isOpen && selectedProject && (
        <ProjectModal onClose={closeProject} id={selectedProject.id}>
          <ProjectSubmissionView
            project={selectedProject}
            onClose={closeProject}
          />
        </ProjectModal>
      )}

      <div className="p-6">
        <div className="flex gap-3 mb-6">
          <div className="bg-blue-300 w-4 rounded-md" />
          <h1 className="font-semibold text-xl">
            View All Projects ({currentYear})
          </h1>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {/* Draft Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Draft Status
              </label>
              <select
                value={filterDraft}
                onChange={(e) => setFilterDraft(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Projects</option>
                <option value="draft">Draft Only</option>
                <option value="notDraft">Not Draft</option>
              </select>
            </div>

            {/* Video QC Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Video QC Status
              </label>
              <select
                value={filterVideoQC}
                onChange={(e) => setFilterVideoQC(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Projects</option>
                <option value="qc">Has Video QC</option>
                <option value="noQc">No Video QC</option>
              </select>
            </div>

            {/* Assignment Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Judge Assignment
              </label>
              <select
                value={filterAssigned}
                onChange={(e) => setFilterAssigned(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Projects</option>
                <option value="assigned">Assigned to Judges</option>
                <option value="notAssigned">Not Assigned</option>
              </select>
            </div>

            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search projects..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>

          <div className="text-sm text-gray-600">
            Showing {filteredProjects.length} of {allProjects.length} projects
          </div>
        </div>

        {/* Projects Table */}
        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loading />
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 border-b">
                  <tr>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Project Name
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Major
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Video QC
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Judges Assigned
                    </th>
                    <th className="px-4 py-3 text-left font-medium text-gray-700">
                      Evaluation Status
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {filteredProjects.map((project, index) => {
                    const assignedCount = getAssignedJudgesCount(project.id);
                    const evaluatedCount = getEvaluatedJudgesCount(project.id);

                    return (
                      <tr key={project.id} className="hover:bg-gray-50">
                        <td className="px-4 py-3">
                          <button
                            onClick={() => showProject(project)}
                            className="text-left hover:text-primary-6 hover:underline font-medium"
                          >
                            {project.project_name}
                          </button>
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${
                              project.project_major === "Software Engineering"
                                ? "bg-green-100 text-green-800"
                                : project.project_major ===
                                    "Intelligent Computing"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-blue-100 text-blue-800"
                            }`}
                          >
                            {project.project_major === "Software Engineering"
                              ? "SE"
                              : project.project_major ===
                                  "Intelligent Computing"
                                ? "IC"
                                : "CI"}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          {getProjectStatusBadge(project)}
                        </td>
                        <td className="px-4 py-3">
                          {project.video_qc && project.video_qc !== "" ? (
                            <span className="text-green-600">✓</span>
                          ) : (
                            <span className="text-red-600">✗</span>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={
                              assignedCount > 0
                                ? "text-green-600"
                                : "text-red-600"
                            }
                          >
                            {assignedCount}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`${evaluatedCount === assignedCount && assignedCount > 0 ? "text-green-600" : "text-orange-600"}`}
                          >
                            {evaluatedCount}/{assignedCount}
                          </span>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {filteredProjects.length === 0 && !isLoading && (
              <div className="text-center py-12 text-gray-500">
                No projects found matching the current filters.
              </div>
            )}
          </div>
        )}
      </div>
    </>
  );
};

export default ViewAllProjects;
