import lecturerCommittee from "@/data/lecturerCommittee.json";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";

const LecturerCommittee = () => {
  return (
    <div className="w-full flex justify-center items-center">
      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8 max-md:flex-col md:flex-wrap  w-full *:flex-grow md:max-lg:*:flex-grow-0 xl:*:max-w-[382px] *:basis-[3/7]">
        {lecturerCommittee.map((lecturer) => (
          <LecturerCard lecturer={lecturer} key={lecturer.id} />
        ))}
      </div>
    </div>
  );
};

const LecturerCard = ({ lecturer }) => {
  return (
    <div className="flex max-xs:flex-col border-2 bg-white border-black justify-center transition-all duration-300 ease-in-out hover:shadow-lg hover:scale-[1.02]">
      <div className="flex justify-center max-sm:border-b-black max-xs:border-b-2">
        <LazyLoadImage
          src={lecturer.photo}
          alt={`${lecturer.name} Photo`}
          className="object-contain object-bottom max-sm:w-[144px] max-sm:h-[200px] sm:w-[180px] sm:h-[240px] md:w-[196px] md:h-[256px]"
          effect="blur"
        />
      </div>
      <div className="md:max-md:w-[40%] xs:w-[50%] flex flex-col justify-center items-center max-xs:py-3 px-3 *:text-center">
        <p className="text-mobile-16 font-semibold leading-tight mb-1">
          {lecturer.name}
        </p>
        <p className="text-mobile-14 mb-4">{lecturer.role}</p>
        <a
          href={lecturer.url}
          target="_blank"
          className="text-mobile-14 underline hover:text-primary-6 transition-all cursor-pointer"
        >
          Link to bio
        </a>
      </div>
    </div>
  );
};

export default LecturerCommittee;
