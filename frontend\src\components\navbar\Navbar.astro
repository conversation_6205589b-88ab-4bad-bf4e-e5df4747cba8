---
import Menu from "./Menu.astro";

//get current url page
const url = Astro.url.pathname;
const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isOfficialLaunch = import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

let navcontent: string[] | null = [];
if (url) {
  switch (url) {
    // if landing page then show these nav items
    case "/":
      if (isOfficialLaunch) {
        if (isPostEvent)  // navcontent = ["Home", "About", "Tentative", "Sponsors", `${currentYear} Winners`, "Contact"];
          navcontent = ["Home", "About", "Tentative", `${currentYear} Winners`, "Contact"];
        else  // ["Home", "About", "Tentative", "Sponsors", `${parseInt(currentYear) - 1} Winners`, "Contact"]
          navcontent = ["Home", "About", "Tentative", `${parseInt(currentYear) - 1} Winners`, "Contact"];
      } else {
        navcontent = ["Home", "About", "Tentative", `${parseInt(currentYear) - 1} Winners`, "Contact"];
      }
      break;
    default:
      navcontent = ["Home"];
      break;
  }
}

let openNavbar = false;

const { pathname } = Astro.url;
---

<div class="w-full flex justify-center bg-bgColor fixed z-[20]">
  <div
    class="w-full flex justify-between items-center py-5 px-4 md:px-5  xl:px-0 container-max-w pointer-events-none h-20"
  >
    <a
      href="/"
      class="flex-shrink-0 relative z-50 drop-shadow-xl pointer-events-auto "
    >
      <img
        class="h-14"
        src="/assets/images/logo/PIXEL-icon.webp"
        alt="pixel"
        width="100%"
        height="100%"
      />
    </a>
    <div class="flex items-center gap-2 md:gap-4 pointer-events-auto">
      {
        navcontent && (
          <div class="hidden md:flex rounded-full px-4 md:px-6 py-2 md:py-3 gap-8">
            {navcontent.map((item, index) => (
              <a
                href={
                  url !== "/"
                    ? "/"
                    : item == "Home" ? "#top" : "#" + item.replace(" ", "-").toLowerCase()
                }
                class="font-medium hover:text-primary-6 transition duration-100 ease-in-out"
              >
                {item}
              </a>
            ))}
          </div>
        )
      }

      <div>
        <Menu openNavbar={openNavbar} />
      </div>
    </div>
  </div>
</div>
