---
import Layout from "@layouts/Layout.astro";
import Hero from "@components/landing/Hero.astro";
import About from "@components/landing/About.astro";
import Banner from "@components/landing/Banner.astro";
import Tentative from "@components/landing/Tentative";
import Sponsors from "@components/landing/Sponsors";
import Extra from "@components/landing/Extra.astro";
import Contact from "@components/landing/Contact.astro";
import Footer from "@components/landing/Footer.astro";
import Section from "@components/landing/Section.astro";
import PastWinners from "@components/landing/PastWinners";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isOfficialLaunch = import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH == "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT == "true";
---

<Layout title="Home">
  <main class="w-full h-full flex flex-col">
    <!-- <Banner /> -->
    <Hero />
    <Section sectionId="about">
      <About />
    </Section>
    <Section sectionId="tentative" hasTopPadding={false}>
      <Tentative client:only="react" />
    </Section>

    {
      isOfficialLaunch && (
        <Section sectionId="sponsors" hasTopPadding={false}>
          <Sponsors client:only="react" />
        </Section>
      )
    }

    <Section sectionId={ isPostEvent ? `${currentYear}-winners` : `${parseInt(currentYear) - 1}-winners`} backgroundColor="black">
      <PastWinners client:only="react" />
    </Section>
    <!-- <Extra /> -->
    <Section sectionId="contact">
      <Contact />
    </Section>
  </main>

  <Footer />
</Layout>
