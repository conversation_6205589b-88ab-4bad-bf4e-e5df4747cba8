import { useState, useEffect } from "react";
// import type { Participant } from "@/modules";
import Countdown from "@components/participant/Dashboard/Countdown";
// import "@components/general/loading.css";
import { JudgeContext } from "./Context"; // for passing the data across the components
import "@layouts/layout.css";
import Sidebar from "./Sidebar";
import { MainSection } from "./MainSection";
import type { Judge, JudgeData, JudgeSession } from "@/modules";
import { actionSessionStorage, db, auth, widget } from "@/utils";
import { judgeFactory } from "@/modules";
import { onAuthStateChanged } from "firebase/auth";
import judges from "@data/judges.json";

const isJudging = import.meta.env.PUBLIC_PIXEL_IS_JUDGING === "true";

const Dashboard = () => {
  const [judge, setJudge] = useState<JudgeSession | null>(null);

  if (!isJudging) {
    widget
      .alertError("PIXEL is not open for judging yet", undefined, 3000)
      .then(() => {
        window.location.href = "/";
      });
    return;
  }

  // judging deadline
  const targetDate = new Date(import.meta.env.PUBLIC_JUDGING_DEADLINE);

  useEffect(() => {
    let unsub;

    (async () => {
      unsub = await onAuthStateChanged(auth, async (user) => {
        var judgesList = judges.map((judge) => judge.toLowerCase());

        const isJudge = judgesList.includes(user?.email.toLowerCase());

        if (unsub) {
          unsub();
        }
        
        if (!user || !isJudge) {
          widget
            .alertError(
              "Unauthorized",
              "You are not authorized to access this page.",
              undefined,
              3000
            )
            .then(() => {
              auth.signOut();
              window.location.href = "/";
            });
        } else if (user && isJudge) {
          const judgeData: JudgeSession = actionSessionStorage().get("judgeData");

          if (!judgeData) {
            widget.alertError(
              "Your account is not found. <NAME_EMAIL> for further assistance",
              "Something went wrong while fetching data. Returning to login page ..."
            );
            window.location.href = "/sign-in";
          } else {
            setJudge(judgeData);
          }
        }
      });
    })();

    // const fetchJudgeData = async () => {
    //   const updatedJudgeData: Judge = await judgeFactory().getJudge(
    //     judgeData.firebase_uid,
    //     currentYear
    //   );
    //   actionSessionStorage().update("judgeData", updatedJudgeData);
    //   judgeData = actionSessionStorage().get("judgeData");
    // };

    // fetchJudgeData();
  }, []);

  if (judge) {
    return (
      <div className="w-full h-full max-lg:min-h-screen flex flex-col bg-white md:rounded-md overflow-hidden">
        {/* {teamLoad && <p className="hidden">Loading...</p>} */}
        {/* Component: Topbar */}
        {isJudging ? (
          <div
            className="w-full relative flex justify-end items-center my-0 pr-5 py-2 gap-5"
            id="countdowntopbar"
          >
            <div className="w-full h-full absolute bg-grey-1 top-0 left-0">
              <div className="bg-primary-3 h-full overflow-hidden  relative w-full clip-path-polygon"></div>

              <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1 bg-black">
                <img
                  className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
                  src="/assets/images/logo/pixie-full.webp"
                  alt="Avatar"
                />
                <div className="absolute top-4 ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip">
                  <p className="text-[8px] max-sm:max-w-[10ch] lg:text-base font-semibold">
                    Good luck evaluating the projects!
                  </p>
                </div>
              </div>
            </div>

            <div className="flex flex-col mb-auto z-10">
              <p className="w-full lg:mt-2 uppercase text-md text-black font-bold tracking-wider self-start hidden lg:block whitespace-nowrap">
                Time left for evaluation{" "}
              </p>
              <hr></hr>
            </div>

            <Countdown targetDate={targetDate} />
          </div>
        ) : (
          <div
            className="w-full h-full relative flex justify-end items-center my-0 pr-5 py-2 bg-white rounded gap-5"
            id="countdowntopbar"
          >
            <div className="w-full h-full absolute bg-grey-1 top-0 left-0 opacity-50">
              <div
                className="bg-primary-5 h-full overflow-hidden relative w-full"
                id="wavecountdown"
              ></div>
            </div>
            <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1">
              <img
                className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
                src="/assets/images/logo/pixie-full.webp"
                alt="Avatar"
              />
              <div
                role="tooltip"
                className="ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip"
              >
                <p className="text-[8px] lg:text-base font-semibold whitespace-nowrap">
                  Judging Closed! Thanks for your participation and effort in
                  the judging process!
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex flex-col lg:flex-row w-full h-full lg:h-[640px]">
          <JudgeContext.Provider value={judge}>
            <Sidebar />

            <MainSection />
          </JudgeContext.Provider>
        </div>
      </div>
    );
  }
};

export default Dashboard;
