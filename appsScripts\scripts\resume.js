function participantInfo(e) {
  try {
    let obj = JSON.parse(e.postData.contents);
    
    // Decode file and create blob
    let dcode = Utilities.base64Decode(obj.file);
    let blob = Utilities.newBlob(dcode, obj.mimeType, obj.filename);

    let newFile;

    // Check if folderName is provided from frontend
    if (obj.folderName) {
      let targetFolder = getOrCreateFolderByPath(obj.folderName); // e.g. "2024/2025/PIXEL2025/resume"
      newFile = targetFolder.createFile(blob);
    } else {
      newFile = DriveApp.createFile(blob);
    }

    // Make file shareable and get URL
    let link = newFile.setSharing(DriveApp.Access.ANYONE_WITH_LINK, DriveApp.Permission.VIEW).getUrl();

    // Return JSON response with CORS
    return ContentService
      .createTextOutput(JSON.stringify({
        result: {
          status: "success",
          fileUrl: link
        }
      }))
      .setMimeType(ContentService.MimeType.JSON);

  } catch (err) {
    return ContentService
      .createTextOutput(JSON.stringify({
        result: {
          status: "error",
          error: err.toString()
        }
      }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

function doPost(e) {
  return participantInfo(e);
}

// Utility to find or create nested folder path like "2024/2025/PIXEL2025/resume"
function getOrCreateFolderByPath(path) {
  const folders = path.split("/");
  let parent = DriveApp.getRootFolder();

  for (const name of folders) {
    let found = null;
    const subfolders = parent.getFoldersByName(name);
    while (subfolders.hasNext()) {
      found = subfolders.next();
      break;
    }
    parent = found || parent.createFolder(name);
  }

  return parent;
}

// Optional error handler
function handleError(message) {
  return ContentService
    .createTextOutput(JSON.stringify({
      result: {
        status: "error",
        error: message
      }
    }))
    .setMimeType(ContentService.MimeType.JSON);
}



