import { useEffect } from "react";
import tentative from "@data/tentative.json";

function Tentative() {
  useEffect(() => {
    let mm = gsap.matchMedia();
    // @ts-ignore
    mm.add("(min-width: 992px)", () => {
        // @ts-ignore
        gsap.to("#left-sec", {
          scrollTrigger: {
            trigger: "#right-sec",
            start: "top +=80px",
            end: "bottom bottom-=10vh",
            pin: "#left-sec",
            pinSpacing: true,
            scrub: 4,
            toggleActions: "play pause resume reset",
            // markers: {
            //   startColor: "purple",
            //   endColor: "fuchsia",
            //   fontSize: "1rem",
            // }
          }
        })
      }
    );
  }, []);

  return (
    <div className="flex max-lg:flex-col w-full gap-10 xl:gap-8 lg:*:basis-1/2 h-full">
      <div
        id="left-sec"
        className="h-[440px] lg:h-[90vh] w-full flex flex-col rounded-lg shadow-lg py-8 px-6 md:py-12 md:px-8  relative bg-[url('/assets/images/logo/pixie-full.webp')] bg-no-repeat bg-[50%_135%] sm:bg-[50%_150%] md:bg-[80%_80px] lg:bg-[50%_122%] xl:bg-[50%_150%] [@media(1249px<width<1367px)]:bg-[50%_120%] [@media(1249px<width<1367px)]:bg-[length:320px]  bg-[length:240px] sm:bg-[length:264px] md:bg-[length:320px] lg:bg-[length:60%] xl:bg-[length:70%]"
      >
      {/* <div
        id="left-sec"
        className="h-[440px] lg:h-[90vh] w-full flex flex-col rounded-lg shadow-lg py-8 px-6 md:py-12 md:px-8 relative"
      > */}
        <div className="h-full w-full flex flex-col">
          <div >
            <p className="text-primary-linear font-semibold text-mobile-18 md:text-tablet-20 xl:text-desktop-24">
              EVENT TENTATIVE
            </p>
            <h2 className="text-mobile-34 md:text-tablet-48 xl:text-desktop-60 mt-1 max-w-[360px]" style={{ textShadow: '2px 2px 4px #FFFBF8'}}>
              REVIEW THE SCHEDULED AGENDA
            </h2>
          </div>
          {/* <div className="absolute w-full h-[85%] md:h-[70%] md:scale-125 lg:h-[60%] lg:scale-[1.75] xl:h-[63%] flex items-end justify-center">
            <img src="/assets/images/logo/pixie-full.webp" alt="Pixie" className="max-h-[240px] object-scale-down"/>
          </div> */}
        </div>
      </div>


      <div
        id="right-sec"
        className="h-full flex flex-col p-6 rounded-lg overflow-hidden gap-6 shadow-lg"
      >
        {tentative.map((data, index) => (
          // <div className="flex flex-col hover:border-l-4 hover:border-primary-4 hover:margin-l-5" key={index}>
          <div className="flex flex-col" key={index}>
            <div className="bg-grey-1 rounded w-fit px-3 py-2">
              <p className="text-mobile-18 text-grey-4 !font-medium">
                {data.time}
              </p>
            </div>
            <p className="whitespace-pre-wrap mt-6 font-medium text-mobile-20 md:text-tablet-20 lg:text-desktop-24">
              {data.event}
            </p>
            <p className="whitespace-pre-wrap mt-2 text-mobile-16 md:text-tablet-20 lg:text-desktop-20">
              {data.desc}
            </p>
            <hr className="w-full mt-6 border border-grey-1 drop-shadow-[0_5px_5px_rgba(0,0,0,0.5)]"></hr>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Tentative;
