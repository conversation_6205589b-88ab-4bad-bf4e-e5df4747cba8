---
import "./layout.css";

export interface Props {
  title: string;
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title } = Astro.props;
---

<!-- TODO: LON banner need to be hidden for the judges page --><!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society" />
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

    <meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025" />
    <meta property="og:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp" />
    <meta property="og:url" content="https://pixelusm.com/" />

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta name="twitter:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp" />

    <meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />

    <!-- FontAwesome cdn css -->
    <link
      rel="stylesheet"
      href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css"
    />

    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css"
    />

    <!-- Shepherd JS -->
    <script
      src="https://cdn.jsdelivr.net/npm/shepherd.js@10.0.1/dist/js/shepherd.min.js"
      crossorigin="anonymous"
      referrerpolicy="no-referrer"></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/shepherd.js@10.0.1/dist/css/shepherd.css"
    />

    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>
    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body class="overflow-y-auto">
    <!-- <div>
      <div
        class="absolute top-0 w-full z-20 bg-[#0961c7] text-white justify-center items-center px-2 md:px-20 py-2"
        id="promo-banner"
      >
        <div class="flex items-center ml-auto">
          <p class="hidden md:block text-base">
            LOG OFF NIGHT 2023 REGISTRATION IS OPEN NOW!
          </p>
          <p class="block md:hidden text-xs">
            LON 2023 REGISTRATION IS OPEN NOW!
          </p>
          <a
            href="https://bit.ly/3N9oR1N"
            target="_blank"
            rel="noopener noreferrer"
            class="text-xs md:text-base ml-4 lg:ml-6 border rounded h-8 px-2 flex items-center hover:bg-[#fafafa] hover:text-black duration-100 cursor-pointer"
            >REGISTER NOW
          </a>
        </div>

        <button
          class="hover:text-black/30 duration-100 ml-auto"
          id="close-banner"
          title="close banner"
        >
          <i class="fa-solid fa-xmark"></i>
        </button>
      </div>
    </div> -->

    <div id="main-modal"></div>
    <main
      class="w-full flex justify-center items-center md:max-lg:h-screen min-h-screen lg:max-xl:px-5 lg:py-8 container-max-w "
    >
      <div class="h-full w-full shadow-lg bg-white rounded-md">
        <slot />
      </div>
    </main>
  </body>
  <!-- Flowbite JS -->
  <script src="../../node_modules/flowbite/dist/flowbite.min.js"></script>

  <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"
  ></script>

  <script>
    // const banner = document.getElementById("promo-banner")!;

    // document.getElementById("close-banner")!.onclick = function () {
    //   banner.style.display = "none";
    // };
    // document.addEventListener("DOMContentLoaded", () => {
    //   setTimeout(() => {
    //     banner.classList.remove("hidden");
    //   }, 1000);
    // });
  </script>
</html>

<style is:inline>
  body::before {
    content: "";
    position: fixed;
    z-index: -100;
    inset: 0;
    width: 100%;
    height: 100%;
    background-color: #F5EBFC;
    background-image: url("/assets/images/bg/dashboard-bg.svg");
    background-size: cover;
  }
</style>
