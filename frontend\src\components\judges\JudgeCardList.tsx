import React, { useEffect, useState } from "react";
import { useStore } from "@utils/store";
import * as pkg from "react-lazy-load-image-component";
const { LazyLoadImage } = pkg;
import "react-lazy-load-image-component/src/effects/blur.css";

interface Props {
  major: string;
  salutation: string;
  name: string;
  position: string;
  organization: string;
}

const JudgeCard: React.FC<Props> = ({
  major,
  salutation,
  name,
  position,
  organization,
}) => {
  const [imageSrc, setImageSrc] = useState(`/assets/images/judges/${name}.webp`);

  const handleImageError = () => {
    setImageSrc("/assets/images/judges/Judge.webp");
  };

  if (!name) {
    return <></>;
  }

  const majorInitial = major
    .split(" ")
    .map((word) => word.charAt(0))
    .join("");

  let majorInitialColor = "";

  switch (majorInitial) {
    case "IC":
      majorInitialColor = "bg-red-ic-1";
      break;
    case "SE":
      majorInitialColor = "bg-green-se-1";
      break;
    case "CI":
      majorInitialColor = "bg-blue-ci-1";
      break;
    default:
      break;
  }

  return (
    <div className="relative group w-full h-[296px] max-sm:max-w-[160px] sm:w-[190px] sm:h-[350px] md:basis-1/4 md:w-full  md:max-lg:min-w-[160px] md:max-md:h-[296px] border-black border overflow-clip bg-white">
      <div className="relative z-1 w-full h-[70%] sm:h-[75%]">
        <LazyLoadImage
          src={imageSrc}
          alt={name + " image"}
          width="100%"
          height="100%"
          className="object-cover h-full w-full"
          effect="blur"
          onError={handleImageError}
        />
        <div
          className={`absolute group-hover:-translate-y-[296px] md:group-hover:-translate-y-[350px] transition-transform ease-in-out delay-200 duration-500 
            z-2 left-0 bottom-0 p-2 font-bold italic font-inter text-mobile-14 ${majorInitialColor}`}
        >
          {majorInitial}
        </div>
      </div>
      <div className="absolute z-2 h-[30%] sm:h-[25%] w-full group-hover:-translate-y-[296px] sm:group-hover:-translate-y-[350px] transition-transform ease-in-out delay-200 duration-500 bg-white ">
        <div className="py-2 px-1 text-center [&>p]:text-mobile-14 [&>p]:font-medium place-content-center flex flex-col justify-center items-center h-full">
          <p>
            {salutation} {name}
          </p>
        </div>
        <div className="h-[296px] sm:h-[350px] flex flex-col justify-center [&>p]:text-mobile-14 text-center bg-white">
          <div className="grow px-2 place-content-center">
            <p className="font-semibold leading-tight">{position}</p>
            <p className="mt-2 leading-tight">{organization}</p>
          </div>
          <p className={`w-full p-1 font-bold italic ${majorInitialColor}`}>
            <b>{majorInitial}</b>
          </p>
        </div>
      </div>
    </div>
  );
};

const JudgeCardSkeleton = () => {
  return (
    <div className="animate-pulse rounded-none bg-gray-300 relative group w-full h-[296px] max-sm:max-w-[160px] sm:w-[190px] sm:h-[350px] md:basis-1/4 md:w-full  md:max-lg:min-w-[160px] md:max-md:h-[296px] border-black border overflow-clip">
      <div className="relative z-1 w-full h-[70%] sm:h-[75%]">
        <div className="object-cover h-full w-full"></div>
        <div className="animate-pulse rounded-none bg-gray-300 size-8 absolute z-2 left-0 bottom-0 p-2 font-bold italic font-inter text-mobile-14">
          {/* majorInitial */}
        </div>
      </div>
      <div className="absolute z-2 h-[30%] sm:h-[25%] w-full bg-white ">
        <div className="py-2 px-1 text-center [&>p]:text-mobile-14 [&>p]:font-medium place-content-center flex flex-col justify-center items-center h-full">
          <p className="animate-pulse bg-gray-300 rounded-full w-1/2 h-1/5">
            {/* salutation name */}
          </p>
        </div>
        <div className="h-[296px] sm:h-[350px] flex flex-col justify-center [&>p]:text-mobile-14 text-center bg-white">
          <div className="grow px-2 place-content-center">
            <p className="font-semibold leading-tight">{/* position */}</p>
            <p className="mt-2 leading-tight">{/* organization */}</p>
          </div>
          <p className={`w-full p-1 font-bold italic`}>
            <b>{/* majorInitial */}</b>
          </p>
        </div>
      </div>
    </div>
  );
};

const JudgesCardList = ({ data }) => {
  const [judges, setJudges] = useState(data);
  const [loading, setIsLoading] = useState(true);

  const { filter, setFilter } = useStore();

  useEffect(() => {
    if (data) {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (filter === "All") {
      setJudges(data);
    } else {
      const filteredJudges = data.filter((judge) => judge.major === filter);
      setJudges(filteredJudges);
    }
  }, [filter]);

  return (
    <>
      {loading ? (
        <>
          {[...Array(8)].map((_, index) => (
            <JudgeCardSkeleton key={index} />
          ))}
        </>
      ) : (
        <>
          {judges.map((record: any, index: number) => {
            return (
              <JudgeCard
                key={record.name}
                major={record.major}
                salutation={record.salutation}
                name={record.name}
                position={record.position}
                organization={record.organization}
              />
            );
          })}
        </>
      )}
    </>
  );
};

export default JudgesCardList;
