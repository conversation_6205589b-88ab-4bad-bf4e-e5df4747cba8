---
import SideLayout from "@layouts/sideLayout.astro";
import CareerComponent from "@components/careers/Careers";
import data from "@data/careers.json";
import { Job } from "@modules/job";

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "Careers", path: "/careers" },
];

// Cast the jobs array to the expected Job[] type
const jobs = data.jobs as unknown as Job[];
---

<SideLayout
  title="Careers"
  pageDesc="Explore exciting career opportunities from our global network of trusted sponsor companies."
  breadcrumbs={breadcrumbs}
>
  <CareerComponent client:load careers={jobs} />
</SideLayout>