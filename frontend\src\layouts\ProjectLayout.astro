---
import Project from "@components/projects/Project";
import ProjectFilter from "@components/projects/ProjectFilter.astro";
import YearFilter from "@components/projects/YearFilter.astro";
import ProjectSearch from "@components/projects/ProjectSearchBar.astro";
import ProjectSort from "@components/projects/ProjectSort";
import Navbar from "@components/navbar/Navbar.astro";
import Breadcrumb, {
  Breadcrumb as BreadcrumbType,
} from "@components/general/Breadcrumb.astro";
import "./layout.css";

export interface Props {
  title: string;
  major?: string;
  year?: string;
  tech?: string;
  breadcrumbs?: BreadcrumbType[];
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title, major = "", year = "", tech = "", breadcrumbs } = Astro.props;

// console.log({
//   title,
//   major,
//   year,
//   tech,
//   breadcrumbs,
// }); // eslint-disable-line no-console

---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="description"
      content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects."
    />
    <!-- TBU -->
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society" />
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

    <meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025" />
    <meta
      property="og:description"
      content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects."
    />
    <!-- TBU -->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp" />
    <meta property="og:url" content="https://pixelusm.com/" />

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta
      name="twitter:description"
      content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects."
    />
    <!-- TBU -->
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp" />

    <meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />

    <!-- FontAwesome cdn css -->
    <link
      rel="stylesheet"
      href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css"
    />

    <!-- GSAP cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
    ></script>

    <!-- ScrollTrigger cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"
    ></script>

    <script>
      //@ts-ignore
      gsap.registerPlugin(ScrollTrigger);
    </script>

    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>
    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body class="!m-0">
    <div id="main-modal"></div>
    <Navbar />
    <div
      class="text-center md:text-left pt-16 overflow-visible relative container-max-w mx-auto max-xl:px-4"
      id="bg"
    >
      <!-- Header -->

      <div class="pt-16 pb-11 overflow-hidden relative">
        <Breadcrumb {breadcrumbs} />
        <div class="flex gap-6">
          <h1 class="md:text-tablet-48 lg:text-desktop-60 w-full">
            Explore <br class="xs:hidden" />Projects
          </h1>
          {
            tech && (
              <span class="self-end font-medium daisy-badge bg-primary-6 text-white border-none px-3 py-2 flex gap-2">
                {tech}
                <a href="/projects" title="Remove Tag">
                  <i class="fa-solid fa-xmark text-grey-3 hover:text-grey-5" />
                </a>
              </span>
            )
          }
        </div>
      </div>

      <p class="text-sm text-grey-5 font-medium mb-2">Filter by Major:</p>
      <ProjectFilter major={major} year={year} />

      <p class="text-sm text-grey-5 font-medium mt-6 mb-2">Filter by Year:</p>
      <YearFilter major={major} year={year} />

      <div
        class="flex flex-col md:flex-row items-end md:items-center justify-between mt-4 md:mt-6 gap-4 md:gap-8"
      >
        <ProjectSearch />
        <ProjectSort client:load />
      </div>

      <div
        id="loading-skeleton"
        class="grid md:grid-cols-2 xl:grid-cols-3 gap-6 my-10 md:my-14"
      >
        {
          [...Array(6)].map((_, i) => (
            <div class="w-full border border-black flex flex-col animate-pulse">
              {/* Thumbnail */}
              <div class="w-full aspect-video bg-grey-2" />
              {/* Details */}
              <div class="px-4 py-2 flex flex-col justify-between grow">
                {/* Project Title */}
                <div class="flex flex-col gap-2">
                  <div class="w-full h-3 bg-grey-2 rounded-full" />
                  <div class="w-full h-3 bg-grey-2 rounded-full" />
                </div>

                {/* Profile Avatar & Major Tag */}
                <div>
                  <div class="w-full border-t border-grey-4 my-4" />

                  <div class="flex flex-row justify-between">
                    <div class="flex flex-row gap-2 ">
                      <div class="w-6 h-6 bg-grey-2 rounded-full" />
                      <div class="w-6 h-6 bg-grey-2 rounded-full" />
                      <div class="w-6 h-6 bg-grey-2 rounded-full" />
                    </div>

                    <div class="w-6 h-6 bg-grey-2" />
                  </div>
                </div>
              </div>
            </div>
          ))
        }
      </div>

      <Project client:only="react" major={major} year={year} tech={tech} />

      <!-- Go to Top Button -->
      <div
        id="goToTopBtn"
        onclick={`document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;`}
        title="Go to top"
        style="display: none;"
      >
        <i
          class="p-6 fa-solid fa-arrow-up fixed bottom-5 right-5 w-9 h-9 text-lg rounded-full shadow-lg bg-white text-cs-white cursor-pointer flex justify-center items-center hover:text-primary-6 transition ease-in-out duration-300"
          id="top"></i>
      </div>
    </div>

    <script>
      window.addEventListener("scroll", function () {
        const documentHeight = Math.max(
          document.body.scrollHeight,
          document.documentElement.scrollHeight
        );
        const distanceFromBottom =
          documentHeight - window.scrollY - window.innerHeight;

        // hide go to top button when scroll is at top
        if (window.scrollY > 100) {
          document.getElementById("goToTopBtn")!.style.display = "block";
        } else {
          document.getElementById("goToTopBtn")!.style.display = "none";
        }
      });
    </script>

    <script
      is:inline
      src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"
    ></script>
    <!-- AOS cdn js -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <!-- AOS script cdn js -->
    <script is:inline src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script is:inline>
      AOS.init();
    </script>
  </body>
</html>
