[debug] [2025-05-18T04:01:46.832Z] ----------------------------------------------------------------------
[debug] [2025-05-18T04:01:46.836Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-18T04:01:46.836Z] CLI Version:   13.24.0
[debug] [2025-05-18T04:01:46.836Z] Platform:      win32
[debug] [2025-05-18T04:01:46.836Z] Node Version:  v20.11.1
[debug] [2025-05-18T04:01:46.836Z] Time:          Sun May 18 2025 12:01:46 GMT+0800 (Malaysia Time)
[debug] [2025-05-18T04:01:46.836Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-18T04:01:46.946Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-18T04:01:46.946Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-18T04:01:46.950Z] Checked if tokens are valid: false, expires at: 1747525293216
[debug] [2025-05-18T04:01:46.950Z] Checked if tokens are valid: false, expires at: 1747525293216
[debug] [2025-05-18T04:01:46.951Z] > refreshing access token with scopes: []
[debug] [2025-05-18T04:01:46.952Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-18T04:01:46.952Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T04:01:47.192Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-18T04:01:47.193Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T04:01:47.199Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-18T04:01:47.668Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-18T04:01:47.668Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-18T04:01:47.676Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T04:01:47.676Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-05-18T04:01:47.683Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-18T04:01:47.692Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-18T04:01:47.696Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T04:01:47.696Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T04:01:47.696Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T04:01:47.696Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-18T04:01:47.701Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-18T04:01:47.703Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-18T04:01:47.704Z] Checked if tokens are valid: true, expires at: 1747544506193
[debug] [2025-05-18T04:01:47.704Z] Checked if tokens are valid: true, expires at: 1747544506193
[debug] [2025-05-18T04:01:47.704Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-18T04:01:48.786Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-18T04:01:48.786Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-18T04:01:48.805Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-18T04:01:48.805Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-18T04:01:48.805Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-18T04:01:49.016Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-18T04:01:49.017Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-18T04:01:49.026Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-18T04:01:49.038Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-05-18T06:04:30.228Z] Received signal SIGHUP 1
[info]  
[info] i  emulators: Received SIGHUP for the first time. Starting a clean shutdown. 
[info] i  emulators: Please wait for a clean shutdown or send the SIGHUP signal again to stop right now. 
[info] i  emulators: Shutting down emulators. {"metadata":{"emulator":{"name":"hub"},"message":"Shutting down emulators."}}
[info] i  ui: Stopping Emulator UI {"metadata":{"emulator":{"name":"ui"},"message":"Stopping Emulator UI"}}
[debug] [2025-05-18T07:02:10.055Z] ----------------------------------------------------------------------
[debug] [2025-05-18T07:02:10.057Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-18T07:02:10.058Z] CLI Version:   13.24.0
[debug] [2025-05-18T07:02:10.058Z] Platform:      win32
[debug] [2025-05-18T07:02:10.058Z] Node Version:  v20.11.1
[debug] [2025-05-18T07:02:10.058Z] Time:          Sun May 18 2025 15:02:10 GMT+0800 (Malaysia Time)
[debug] [2025-05-18T07:02:10.058Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-18T07:02:10.170Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-18T07:02:10.171Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-18T07:02:10.174Z] Checked if tokens are valid: false, expires at: 1747544506193
[debug] [2025-05-18T07:02:10.175Z] Checked if tokens are valid: false, expires at: 1747544506193
[debug] [2025-05-18T07:02:10.175Z] > refreshing access token with scopes: []
[debug] [2025-05-18T07:02:10.176Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-18T07:02:10.176Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T07:02:10.378Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-18T07:02:10.378Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T07:02:10.385Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-18T07:02:10.890Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-18T07:02:10.890Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-18T07:02:10.898Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T07:02:10.898Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project test-pixel-2024-b287c. This may result in unexpected behavior. 
[debug] [2025-05-18T07:02:10.905Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-18T07:02:10.913Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-18T07:02:10.919Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T07:02:10.919Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T07:02:10.919Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T07:02:10.919Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-18T07:02:10.925Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-18T07:02:10.929Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-18T07:02:10.930Z] Checked if tokens are valid: true, expires at: 1747555329378
[debug] [2025-05-18T07:02:10.930Z] Checked if tokens are valid: true, expires at: 1747555329378
[debug] [2025-05-18T07:02:10.930Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-18T07:02:12.017Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-18T07:02:12.017Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-18T07:02:12.037Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-18T07:02:12.037Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-18T07:02:12.037Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-18T07:02:12.408Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-18T07:02:12.408Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-18T07:02:12.461Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-18T07:02:12.474Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-05-18T12:48:30.460Z] ----------------------------------------------------------------------
[debug] [2025-05-18T12:48:30.463Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-18T12:48:30.463Z] CLI Version:   13.24.0
[debug] [2025-05-18T12:48:30.463Z] Platform:      win32
[debug] [2025-05-18T12:48:30.463Z] Node Version:  v20.11.1
[debug] [2025-05-18T12:48:30.465Z] Time:          Sun May 18 2025 20:48:30 GMT+0800 (Malaysia Time)
[debug] [2025-05-18T12:48:30.465Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-18T12:48:30.602Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-18T12:48:30.602Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-18T12:48:30.607Z] Checked if tokens are valid: false, expires at: 1747555329378
[debug] [2025-05-18T12:48:30.607Z] Checked if tokens are valid: false, expires at: 1747555329378
[debug] [2025-05-18T12:48:30.608Z] > refreshing access token with scopes: []
[debug] [2025-05-18T12:48:30.609Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-18T12:48:30.609Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T12:48:30.824Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-18T12:48:30.825Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-18T12:48:30.866Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-18T12:48:31.350Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-18T12:48:31.351Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-18T12:48:31.364Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T12:48:31.364Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project test-pixel-2024-b287c. This may result in unexpected behavior. 
[debug] [2025-05-18T12:48:31.373Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-18T12:48:31.382Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-18T12:48:31.386Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T12:48:31.386Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T12:48:31.386Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-18T12:48:31.386Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-18T12:48:31.390Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-18T12:48:31.392Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-18T12:48:31.392Z] Checked if tokens are valid: true, expires at: 1747576109825
[debug] [2025-05-18T12:48:31.393Z] Checked if tokens are valid: true, expires at: 1747576109825
[debug] [2025-05-18T12:48:31.393Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-18T12:48:32.523Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-18T12:48:32.524Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-18T12:48:32.546Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-18T12:48:32.546Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-18T12:48:32.546Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-18T12:48:32.753Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-18T12:48:32.753Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-18T12:48:32.770Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-18T12:48:32.782Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-05-19T01:04:15.324Z] ----------------------------------------------------------------------
[debug] [2025-05-19T01:04:15.327Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-19T01:04:15.328Z] CLI Version:   13.24.0
[debug] [2025-05-19T01:04:15.328Z] Platform:      win32
[debug] [2025-05-19T01:04:15.328Z] Node Version:  v20.11.1
[debug] [2025-05-19T01:04:15.328Z] Time:          Mon May 19 2025 09:04:15 GMT+0800 (Malaysia Time)
[debug] [2025-05-19T01:04:15.328Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-19T01:04:15.332Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-05-19T01:04:15.435Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-19T01:04:15.435Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-19T01:04:15.439Z] Checked if tokens are valid: false, expires at: 1747576109825
[debug] [2025-05-19T01:04:15.439Z] Checked if tokens are valid: false, expires at: 1747576109825
[debug] [2025-05-19T01:04:15.440Z] > refreshing access token with scopes: []
[debug] [2025-05-19T01:04:15.440Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-19T01:04:15.440Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T01:04:15.749Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-19T01:04:15.749Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T01:04:15.755Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-19T01:04:16.068Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-05-19T01:04:16.068Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-05-19T01:04:16.249Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-19T01:04:16.249Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-19T01:04:16.255Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T01:04:16.255Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project test-pixel-2024-b287c. This may result in unexpected behavior. 
[debug] [2025-05-19T01:04:16.260Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-19T01:04:16.268Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-19T01:04:16.270Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T01:04:16.270Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T01:04:16.270Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T01:04:16.270Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-19T01:04:16.274Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-19T01:04:16.278Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-19T01:04:16.278Z] Checked if tokens are valid: true, expires at: 1747620254749
[debug] [2025-05-19T01:04:16.279Z] Checked if tokens are valid: true, expires at: 1747620254749
[debug] [2025-05-19T01:04:16.279Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-19T01:04:17.429Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-19T01:04:17.429Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-19T01:04:17.444Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-19T01:04:17.444Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-19T01:04:17.444Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-19T01:04:17.607Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-19T01:04:17.607Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-19T01:04:17.662Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-19T01:04:17.674Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-05-19T03:15:31.133Z] ----------------------------------------------------------------------
[debug] [2025-05-19T03:15:31.137Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-19T03:15:31.137Z] CLI Version:   13.24.0
[debug] [2025-05-19T03:15:31.137Z] Platform:      win32
[debug] [2025-05-19T03:15:31.138Z] Node Version:  v20.11.1
[debug] [2025-05-19T03:15:31.138Z] Time:          Mon May 19 2025 11:15:31 GMT+0800 (Malaysia Time)
[debug] [2025-05-19T03:15:31.138Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-19T03:15:31.349Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-19T03:15:31.349Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-19T03:15:31.356Z] Checked if tokens are valid: false, expires at: 1747620254749
[debug] [2025-05-19T03:15:31.356Z] Checked if tokens are valid: false, expires at: 1747620254749
[debug] [2025-05-19T03:15:31.356Z] > refreshing access token with scopes: []
[debug] [2025-05-19T03:15:31.359Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-19T03:15:31.359Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T03:15:31.631Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-19T03:15:31.631Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T03:15:31.643Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-19T03:15:32.243Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-19T03:15:32.244Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-19T03:15:32.256Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T03:15:32.256Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project test-pixel-2024-b287c. This may result in unexpected behavior. 
[debug] [2025-05-19T03:15:32.266Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-19T03:15:32.278Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-19T03:15:32.283Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T03:15:32.284Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T03:15:32.284Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T03:15:32.284Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-19T03:15:32.289Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-19T03:15:32.291Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-19T03:15:32.292Z] Checked if tokens are valid: true, expires at: 1747628130632
[debug] [2025-05-19T03:15:32.292Z] Checked if tokens are valid: true, expires at: 1747628130632
[debug] [2025-05-19T03:15:32.292Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-19T03:15:32.957Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-19T03:15:32.957Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-19T03:15:32.983Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-19T03:15:32.983Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-19T03:15:32.983Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-19T03:15:33.299Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-19T03:15:33.300Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-19T03:15:33.412Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-19T03:15:33.443Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
[debug] [2025-05-19T03:34:13.268Z] Received signal SIGHUP 1
[debug] [2025-05-19T13:06:43.309Z] ----------------------------------------------------------------------
[debug] [2025-05-19T13:06:43.316Z] Command:       D:\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-05-19T13:06:43.317Z] CLI Version:   13.24.0
[debug] [2025-05-19T13:06:43.317Z] Platform:      win32
[debug] [2025-05-19T13:06:43.317Z] Node Version:  v20.11.1
[debug] [2025-05-19T13:06:43.318Z] Time:          Mon May 19 2025 21:06:43 GMT+0800 (Malaysia Time)
[debug] [2025-05-19T13:06:43.318Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-05-19T13:06:43.649Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-05-19T13:06:43.650Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions, extensions"}}
[debug] [2025-05-19T13:06:43.664Z] Checked if tokens are valid: false, expires at: 1747628130632
[debug] [2025-05-19T13:06:43.664Z] Checked if tokens are valid: false, expires at: 1747628130632
[debug] [2025-05-19T13:06:43.665Z] > refreshing access token with scopes: []
[debug] [2025-05-19T13:06:43.671Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-05-19T13:06:43.672Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T13:06:43.937Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-05-19T13:06:43.938Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-05-19T13:06:43.959Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c [none]
[debug] [2025-05-19T13:06:44.559Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c 200
[debug] [2025-05-19T13:06:44.560Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c {"projectId":"test-pixel-2024-b287c","projectNumber":"833217124720","displayName":"TEST-PIXEL-2024","name":"projects/test-pixel-2024-b287c","resources":{"hostingSite":"test-pixel-2024-b287c","locationId":"asia-southeast1"},"state":"ACTIVE","etag":"1_d8b7d696-5306-49a3-90a9-bea1e147562a"}
[debug] [2025-05-19T13:06:44.587Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T13:06:44.587Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[warn] !  emulators: It seems that you are running multiple instances of the emulator suite for project test-pixel-2024-b287c. This may result in unexpected behavior. 
[debug] [2025-05-19T13:06:44.610Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-test-pixel-2024-b287c.json
[debug] [2025-05-19T13:06:44.639Z] [Extensions] Started Extensions emulator, this is a noop.
[debug] [2025-05-19T13:06:44.653Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T13:06:44.654Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T13:06:44.654Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-05-19T13:06:44.654Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mauth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-05-19T13:06:44.667Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json
[debug] [2025-05-19T13:06:44.679Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\cssocietytech_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\cssocietytech_gmail_com_application_default_credentials.json"}}
[debug] [2025-05-19T13:06:44.682Z] Checked if tokens are valid: true, expires at: 1747663602938
[debug] [2025-05-19T13:06:44.682Z] Checked if tokens are valid: true, expires at: 1747663602938
[debug] [2025-05-19T13:06:44.682Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig [none]
[debug] [2025-05-19T13:06:45.693Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig 200
[debug] [2025-05-19T13:06:45.694Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/test-pixel-2024-b287c/adminSdkConfig {"projectId":"test-pixel-2024-b287c","storageBucket":"test-pixel-2024-b287c.appspot.com","locationId":"asia-southeast1"}
[debug] [2025-05-19T13:06:45.755Z] Ignoring unsupported arg: auto_download {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: auto_download"}}
[debug] [2025-05-19T13:06:45.756Z] Ignoring unsupported arg: port {"metadata":{"emulator":{"name":"ui"},"message":"Ignoring unsupported arg: port"}}
[debug] [2025-05-19T13:06:45.756Z] Starting Emulator UI with command {"binary":"node","args":["C:\\Users\\<USER>\\.cache\\firebase\\emulators\\ui-v1.14.0\\server\\server.mjs"],"optionalArgs":[],"joinArgs":false,"shell":false,"port":4000} {"metadata":{"emulator":{"name":"ui"},"message":"Starting Emulator UI with command {\"binary\":\"node\",\"args\":[\"C:\\\\Users\\\\<USER>\\\\.cache\\\\firebase\\\\emulators\\\\ui-v1.14.0\\\\server\\\\server.mjs\"],\"optionalArgs\":[],\"joinArgs\":false,\"shell\":false,\"port\":4000}"}}
[info] i  ui: Emulator UI logging to ui-debug.log {"metadata":{"emulator":{"name":"ui"},"message":"Emulator UI logging to \u001b[1mui-debug.log\u001b[22m"}}
[debug] [2025-05-19T13:06:46.094Z] Web / API server started at 127.0.0.1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at 127.0.0.1:4000\n"}}
[debug] [2025-05-19T13:06:46.095Z] Web / API server started at ::1:4000
 {"metadata":{"emulator":{"name":"ui"},"message":"Web / API server started at ::1:4000\n"}}
[debug] [2025-05-19T13:06:46.223Z] [Extensions] Connecting Extensions emulator, this is a noop.
[info] i  functions: Watching "D:\pixel2024\frontend\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"D:\\pixel2024\\frontend\\functions\" for Cloud Functions..."}}
[error] !!  functions: Failed to load function definition from source: FirebaseError: could not deploy functions because the "functions" directory was not found. Please create it or specify a different source directory in firebase.json {"metadata":{"emulator":{"name":"functions"},"message":"Failed to load function definition from source: FirebaseError: could not deploy functions because the \u001b[1m\"functions\"\u001b[22m directory was not found. Please create it or specify a different source directory in firebase.json"}}
[debug] [2025-05-19T13:06:46.274Z] Could not find VSCode notification endpoint: FetchError: request to http://localhost:40001/vscode/notify failed, reason: . If you are not running the Firebase Data Connect VSCode extension, this is expected and not an issue.
[info] 
┌─────────────────────────────────────────────────────────────┐
│ ✔  All emulators ready! It is now safe to connect your app. │
│ i  View Emulator UI at http://127.0.0.1:4000/               │
└─────────────────────────────────────────────────────────────┘

┌────────────┬────────────────┬──────────────────────────────────┐
│ Emulator   │ Host:Port      │ View in Emulator UI              │
├────────────┼────────────────┼──────────────────────────────────┤
│ Functions  │ 127.0.0.1:5001 │ http://127.0.0.1:4000/functions  │
├────────────┼────────────────┼──────────────────────────────────┤
│ Extensions │ 127.0.0.1:5001 │ http://127.0.0.1:4000/extensions │
└────────────┴────────────────┴──────────────────────────────────┘
  Emulator Hub running at 127.0.0.1:4400
  Other reserved ports: 4500
┌─────────────────────────┬───────────────┬─────────────────────┐
│ Extension Instance Name │ Extension Ref │ View in Emulator UI │
└─────────────────────────┴───────────────┴─────────────────────┘
Issues? Report them at https://github.com/firebase/firebase-tools/issues and attach the *-debug.log files.
 
