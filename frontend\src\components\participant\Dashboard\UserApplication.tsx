import React from "react";
import { useEffect, useState, useId, useRef, ChangeEvent } from "react";
import Sidebar from "./Sidebar";
import MainSection from "./MainSection";
import type { Team, ParticipantSession } from "@/modules";
import { actionSessionStorage, widget, auth } from "@/utils";
import { ParticipantContext } from "./Context";
import Countdown from "./Countdown";
import { onAuthStateChanged } from "firebase/auth";
import careers from "@data/careers.json";
import Swal from "sweetalert2";

import type { ParticipantInfo } from "@/modules";
import { authFactory, participantFactory, Participant } from "@/modules";
import * as Yup from "yup";

const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
// const isRegistration = false;

// const Dashboard = ({ route }: { route: string }) => {
const UserApplication: React.FC = () => {
  // judging deadline
  const targetDate = new Date(import.meta.env.PUBLIC_SUBMISSION_DEADLINE);

  const { jobs: jobList } = careers;
  // const [jobsApplied, setJobsApplied] = useState(["1", "2", "3", "4", "5"]);

  const [user, setUser] = useState({} as ParticipantSession);
  const [userUid, setUserUid] = useState<string | undefined>(user.firebase_uid);
  const [jobsApplied, setJobsApplied] = useState<string[]>([]);

  useEffect(() => {
    const storedUser: ParticipantSession =
      actionSessionStorage().get("userData");

    // console.log("Stored user from sessionStorage:", storedUser);
    // console.log("job_application_id:", storedUser?.job_application_id);
    // console.log(
    //   "typeof job_application_id:",
    //   typeof storedUser?.job_application_id
    // );

    if (storedUser) {
      setUser(storedUser);

      // Ensure job_application_id is an array before filtering
      const jobIds = Array.isArray(storedUser.job_application_id) 
        ? storedUser.job_application_id 
        : typeof storedUser.job_application_id === 'string'
          ? (storedUser.job_application_id as string).split(',').map(id => id.trim())
          : [];

      setJobsApplied(jobIds.filter(id => id && id !== "__EMPTY__"));
    }
  }, []);

  const [TEAM_MIN_LIMIT, setMinLimit] = useState(0);
  const [TEAM_MAX_LIMIT, setMaxLimit] = useState(0);

  // for ProjectMode
  const [isTeam, setIsTeam] = useState(false);

  // for TeamSection
  const [team, setTeam] = useState({} as Team);

  // for ProjectSection
  const [project, setProject] = useState({} as any);

  const [dashboardLoad, setDashboardLoad] = useState(true);
  const [teamLoad, setTeamLoad] = useState(true);

  const [isLoading, setIsLoading] = useState(false);
  // const records = useStore(jobStore);

  // To solve aria-hidden warning
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  function handleTeamLoad() {
    setTeamLoad(false);
  }

  if (import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "false") {
    widget.alertError(
      "PIXEL is not officially launched yet",
      "Please wait for the official launch.",
      undefined,
      3000
    );
    setTimeout(() => {
      window.location.href = "/";
    }, 3000);
    return;
  }

  useEffect(() => {
    let unsub;

    (function () {
      unsub = onAuthStateChanged(auth, (user) => {
        if (unsub) {
          unsub();
        }

        if (!user) {
          widget
            .alertError(
              "You are not logged in",
              "Please login to continue.",
              undefined,
              3000
            )
            .then(() => {
              window.location.href = "/sign-in";
            });
        } else if (user) {
          // case where participant has done signed up (there is user object from firebase at this point), but not registered yet, and attempts to access dashboard
          const participantSessionData = actionSessionStorage().get("userData");
          if (!participantSessionData) {
            widget
              .alertError(
                "You are not logged in",
                "Please login to continue.",
                undefined,
                3000
              )
              .then(() => {
                window.location.href = "/sign-in";
              });
          }
        }
      });
    })();

    let storedUser: ParticipantSession = actionSessionStorage().get("userData");

    if (storedUser) {
      setUser(storedUser);
      setIsTeam(storedUser.is_team);

      let majorInitials = storedUser.student_major
        .split(" ")
        .map((word) => word[0].toUpperCase())
        .join("");

      setMinLimit(
        Number(import.meta.env[`PUBLIC_${majorInitials}_TEAM_MIN_LIMIT`])
      );
      setMaxLimit(
        Number(import.meta.env[`PUBLIC_${majorInitials}_TEAM_MAX_LIMIT`])
      );
    }
  }, []);

  useEffect(() => {
    if (user) {
      setDashboardLoad(false);
    } else {
      (async function () {
        await widget.alertError(
          "User not found",
          "Something went wrong while fetching user data. Returning to login page ..."
        );
        setTimeout(() => {
          window.location.href = "/sign-in";
        }, 3000);
      })();
    }
  }, [user]);

  // if all data is still fetching then show loading
  if (dashboardLoad === true) {
    return (
      <div id="container">
        <span id="first"></span>
        <span id="second"></span>
        <span id="third"></span>
      </div>
    );
  }

  // remove the job from airtable and update state
  const removeJob = async (jobId) => {
    const confirm = await widget.confirm(
      "Are you sure?",
      "You won't be able to revert this!",
      "Yes, delete it!"
    );
    if (confirm.isConfirmed) {
      try {
        widget.loading();
        // 1. Compute the updated jobs array
        const updatedJobsApplied = jobsApplied.filter((job) => job !== jobId);

        // 2. Create a new user object with updated job_application_id
        const updatedUser: Partial<ParticipantSession> = {
          job_application_id: updatedJobsApplied.length > 0 ? updatedJobsApplied : ["__EMPTY__"],
        };

        // console.log("updatedUser ");
        // console.log(updatedUser);
        // 3. Update session/backend first
        await authFactory().updateAccount(updatedUser, user.firebase_uid);

        // 4. Then update the frontend state
        setJobsApplied(updatedJobsApplied);

        await widget.alertSuccess("Success", "The job has been removed.");
        // window.location.href = "/participant/jobs-applied";
      } catch (error: any) {
        await widget.alertError("Error in updateUserAccount", error);
      }
    }
  };

  return (
    <div className="w-full h-full max-lg:min-h-screen flex flex-col bg-white md:rounded-md overflow-hidden">
      {teamLoad && <p className="hidden">Loading...</p>}
      {/* Component: Topbar */}
      {isRegistration ? (
        <div
          className="w-full relative flex justify-end items-center my-0 pr-5 py-2 gap-5"
          id="countdowntopbar"
        >
          <div className="w-full h-full absolute bg-grey-1 top-0 left-0">
            <div className="bg-primary-3 h-full overflow-hidden relative w-full clip-path-polygon"></div>

            <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1 bg-black">
              <img
                className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
                src="/assets/images/logo/pixie-full.webp"
                alt="Avatar"
              />
              <div
                role="tooltip"
                className="absolute top-4 ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip"
              >
                <p className="text-[8px] lg:text-base font-semibold whitespace-nowrap">
                  Job Application
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col mb-auto z-10">
            <p className="w-full lg:mt-2 uppercase text-md text-black font-bold tracking-wider self-start hidden md:block whitespace-nowrap">
              Time left for submission{" "}
            </p>
            <hr></hr>
          </div>
          <Countdown targetDate={targetDate} />
        </div>
      ) : (
        <div
          className="w-full relative flex justify-end items-center my-0 pr-5 py-2 gap-5"
          id="countdowntopbar"
        >
          <div className="w-full h-full absolute bg-grey-1 top-0 left-0">
            <div
              className="bg-primary-3 h-full overflow-hidden relative w-full"
              id="wavecountdown"
            ></div>
          </div>
          <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1 bg-black">
            <img
              className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
              src="/assets/images/logo/pixie-full.webp"
              alt="Avatar"
            />
            <div
              role="tooltip"
              className="relative ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip"
            >
              <p className="text-[8px] lg:text-base font-semibold whitespace-nowrap">
                Thank you for your participation!
              </p>
            </div>
          </div>
        </div>
      )}
      <div className="flex flex-col lg:flex-row h-[640px]">
        <ParticipantContext.Provider
          value={{
            user,
            setUser,
            TEAM_MIN_LIMIT,
            TEAM_MAX_LIMIT,
            handleTeamLoad,
            isTeam,
            setIsTeam,
            team,
            setTeam,
            project,
            setProject,
          }}
        >
          {/* {window.innerWidth >= 1024 ? <Sidebar /> : <DropDown/>} */}
          <Sidebar />
          {/* bg-white flex flex-col w-full p-5 relative overflow-x-hidden overflow-y-auto h-full */}
          <div className=" bg-white flex flex-col w-full relative overflow-x-hidden overflow-y-auto h-full">
            <div className="flex flex-col lg:flex-row h-auto gap-4 flex-none m-3">
              {/* Num of jobs applied */}
              <div className="card w-full flex flex-row h-auto bg-white shadow-xl rounded-lg lg:w-1/4">
                <div className="card-body flex flex-row justify-between items-center md:flex-col md:justify-center md:items-start py-4 px-7 w-full">
                  <p className="card-title text-black font-normal font-jackdawn text-sm md:pb-0 lg:text-lg xl:text-xl">
                    Jobs Applied
                  </p>
                  <p className="font-bold text-3xl text-right md:pb-0 xl:text-4xl font-jackdawn pt-2">
                    {jobsApplied.length}
                  </p>
                </div>
              </div>

              {/* Look for more jobs */}
              <div className="card flex flex-row w-full h-[103px] md:h-[123px] bg-blue bg-opacity-50 shadow-xl rounded-lg justify-center">
                {/* <img
                    src="/assets/img/bot.webp"
                    alt="Robot"
                    style={{ maxHeight: "150%", objectFit: "contain" }}
                    draggable="false"
                  /> */}
                <div className="flex flex-col h-full lg:flex-row justify-center lg:justify-between items-center gap-2 flex-1 mx-5">
                  <div className="flex flex-col gap-4">
                    <p className="text-darkblue font-medium font-inter text-sm md:text-lg xl:text-xl">
                      Look for more jobs!
                    </p>
                    <p className="hidden lg:block text-darkblue font-normal text-xs xl:text-base font-poppins">
                      Unlock your potential! Join us for a rewarding
                      <br />
                      career—growth, innovation, and success await.
                    </p>
                  </div>
                  <a href="/careers" className="leading-5">
                    <button className="daisy-btn daisy-btn-accent daisy-btn-sm md:daisy-btn-md rounded-full px-5 sm:px-10 py-5 font-medium flex flex-col justify-center font-jackdawn text-xs lg:text-base xl:text-lg bg-primary-3 border-none hover:bg-primary-5">
                      Explore Careers
                    </button>
                  </a>
                </div>
              </div>
            </div>

            {/* <div className="flex flex-col h-full gap-4 overflow-y-auto w-full lg:grid max-lg:grid-rows-6 lg:grid-cols-6 xl:grid-cols-8"> */}
            {/* Jobs that you have applied */}
            {/* <div
                className="card card-compact w-full max-lg:row-span-2 lg:col-span-2 rounded-none bg-cover bg-center flex-none h-32 lg:h-full"
                style={{
                  backgroundImage:
                    "url(/assets/bg/application-dashboard-bg2.webp)",
                }}
              >
                <div className="card-body text-white text-center font-bold tracking-widest justify-center font-jackdawn">
                  <h2 className="text-base sm:text-lg leading-5 md:text-xl lg:text-2xl xl:text-3xl">
                    Jobs that you
                    <br />
                    have applied
                  </h2>
                </div>
              </div> */}

            {/* Job applied cards */}
            <div
              className={`overflow-y-auto w-full scroll-bar flex-grow mb-2 ${
                jobsApplied.length <= 3
                  ? "flex flex-col gap-2"
                  : "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
              }`}
            >
              {jobsApplied.length === 0 ? (
                <div className="flex flex-col gap-2 h-full justify-center items-center text-lg bg-white py-8 font-sans xl:text-2xl">
                  <p>No jobs applied yet</p>
                  <button className="daisy-btn daisy-btn-sm bg-[#f2f2f2] hover:bg-[#d2d2d2] border-none dark:bg-gray text-darkblue">
                    <a href="/careers">Look for jobs</a>
                  </button>
                </div>
              ) : (
                jobsApplied.map((jobId, index) => {
                  const job = jobList.find((element) => element.id === jobId);
                  const {
                    id,
                    name: jobTitle,
                    type: jobType,
                    mode: jobMode,
                    description: jobDesc,
                    company: { name: companyName, logo: slug },
                    requirements,
                    responsibilities,
                    benefits,
                  } = job;
                  const jobDescription = `
                  <h1 class="font-semibold text-mobile-18 md:text-xl">Job Descriptions</h1>

                  <h2 class="font-semibold text-mobile-16 md:text-lg mt-5">A. Responsibilities</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-2">
                    ${
                      responsibilities
                        ? responsibilities
                            .map(
                              (responsibility) =>
                                `<li class="text-sm md:text-base">${responsibility}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>

                  <h2 class="font-semibold text-mobile-16 md:text-lg mt-5">B. Requirements</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-1">
                    ${
                      requirements
                        ? requirements
                            .map(
                              (req) =>
                                `<li class="text-sm md:text-base">${req}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>
                  
                  <h2 class="font-semibold text-lg mt-5">C. Benefits Offered</h2>
                  <ul class="list-disc list-outside pl-8 mt-2 space-y-1">
                    ${
                      benefits
                        ? benefits
                            .map(
                              (benefit) =>
                                `<li class="text-sm md:text-base">${benefit}</li>`
                            )
                            .join("")
                        : `<li class="text-sm md:text-base">This company does not provide any benefit to its employees. RUNNN!!</li>`
                    }
                  </ul>
                  `;
                  return (
                    <div
                      key={id}
                      className="card w-full bg-primary-1 rounded-md p-5 max-lg:flex flex-col justify-between items-start gap-3 lg:gap-0"
                    >
                      <div className="flex flex-col items-start justify-between w-full">
                        {/* Company logo */}
                        <img
                          // src={`/assets/company/${slug}/${slug}-logo.webp`}
                          src={`${slug}`}
                          alt="Company logo"
                          className="object-contain h-14 mb-2"
                        />
                        {/* Job title + company name */}
                        <div className="flex flex-col w-full gap-0">
                          <div className="flex flex-row w-full justify-between align-middle items-center">
                            <div className=" text-black font-bold text-md font-sans lg:text-xl xl:text-2xl md:mb-1">
                              {jobTitle ? jobTitle : "Job title"}
                            </div>
                            <button
                              onClick={() => removeJob(id)}
                              className={
                                !isClient
                                  ? ""
                                  : "daisy-btn daisy-btn-ghost fa fa-trash text-xl aria-hidden btn-md p-2 text-black hover:text-darkblue flex justify-center "
                              }
                              disabled={id === "default-id"}
                              aria-hidden={isClient}
                            />
                          </div>
                          <div className="-mt-1 text-darkblue text-sm font-sans lg:mb-2 xl:mb-4 font-semibold gap-1 text-primary-6">
                            {companyName ? companyName : "Company Name"}
                          </div>
                        </div>
                      </div>

                      {/* Badges */}
                      <div className="flex flex-row w-full max-lg:justify-between items-center gap-2">
                        <div className="flex flex-row gap-1">
                          <div className="daisy-badge bg-primary-4 text-xs font-semibold text-black">
                            {jobType ? jobType : "Full-Time"}
                          </div>
                          <div className="daisy-badge bg-primary-4 text-xs font-semibold text-black">
                            {jobMode ? jobMode : "Remote"}
                          </div>
                        </div>
                        <button
                          className="underline btn-xs -mr-2 normal-case text-darkblue font-normal"
                          onClick={() => {
                            const modal = document.getElementById(
                              `job_modal_${index}`
                            ) as HTMLDialogElement;
                            modal?.showModal();
                          }}
                        >
                          See details
                        </button>
                        <dialog
                          id={`job_modal_${index}`}
                          className="daisy-modal daisy-modal-bottom sm:daisy-modal-middle"
                        >
                          <div className="daisy-modal-box min-w-[70%]">
                            <div className="daisy-modal-action sticky top-0 right-0 md:top-2 md:right-2 float-right">
                              <form method="dialog">
                                <button className="daisy-btn daisy-btn-sm daisy-btn-circle">
                                  ✕
                                </button>
                              </form>
                            </div>
                            <div
                              id="modal-content"
                              className="markdown prose max-w-none"
                              dangerouslySetInnerHTML={{
                                __html: jobDescription || "",
                              }}
                            ></div>
                          </div>
                          <form
                            method="dialog"
                            className="daisy-modal-backdrop"
                          >
                            <button>close</button>
                          </form>
                        </dialog>
                      </div>
                    </div>
                  );
                })
              )}
            </div>
            {/* </div> */}
          </div>
        </ParticipantContext.Provider>
      </div>
    </div>
  );
};

export default UserApplication;
