import { Api } from "@/utils";
const api = new Api("https://pixelpandaapi.pixelusm.com");

type TableName =
  | "lecturer-committees"
  | "student-committees"
  | "sponsors"
  | "partners"
  | "participants"
  | "projects"
  | "judges"
  | "evaluations"
  | "supervisors";

type Operation = "first" | "get" | "post" | "put" | "patch" | "delete";

interface RequestParams {
  fields?: string;
  filterByFormula?: string;
  maxRecords?: number;
  pageSize?: number;
  sort?: string;
  view?: string;
  offset?: string;
}

class ProxyApi {
  csrfQueue: Promise<void>;
  requestQueue: Promise<any>;

  constructor() {
    this.csrfQueue = Promise.resolve();
    this.requestQueue = Promise.resolve();
  }

  async getCSRFToken(): Promise<string> {
    try {
      let result: string;
      this.csrfQueue = this.csrfQueue.then(async () => {
        const response = await fetch("/api/csrf");
        if (!response.ok) {
          throw new Error("Failed to set CSRF token");
        }
        result = await response.text();
      });

      await this.csrfQueue;
      return result;
    } catch (error) {
      this.requestQueue = Promise.resolve(); // Reset the promise chain
      throw new Error("Error in getCSRFToken: " + error.message); // Re-throw the error to be handled by the calling code}
    }
  }

  private async request(
    operation: Operation,
    table: TableName,
    recordId: string = "",
    data: any = {},
    typecast: boolean = false,
    params: RequestParams = {}
  ): Promise<any> {
    try {
      return this.requestQueue.then(async () => {
        const csrfToken = await this.getCSRFToken();

        const body = {
          operation,
          table,
          recordId,
          data,
          typecast,
          params,
        };
        // console.log(body);

        const response = await fetch("/api/proxyApi", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-CSRF-Token": csrfToken,
          },
          body: JSON.stringify(body),
        });

        if (!response.ok) {
          const errorResult = await response.json();
          throw new Error(errorResult.error || "API request failed");
        }

        const result = await response.json();
        
        return result.offset ? result : result.data;
      });
    } catch (error) {
      this.requestQueue = Promise.resolve(); // Reset the promise chain
      throw new Error("Error in sending request in proxyApi: " + error.message); // Re-throw the error to be handled by the calling code}
    }
  }

  /**
   * Fetch a specific record by ID.
   *
   * @example
   * ```ts
   * try {
   *   const { data } = await proxyApi.first("tablename", "recordid");
   * } catch (error) {
   *   widget.alertError("Error in first", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {string} id - The ID of the record to fetch.
   * @returns {Promise<any>} The response data.
   */
  async first(table: TableName, id: string): Promise<any> {
    try {
      return await this.request("first", table, id);
    } catch (error) {
      throw new Error("Error in proxyApi.first: " + error.message);
    }
  }

  /**
   * Fetch records with optional parameters.
   *
   * @example
   * ```ts
   * try {
   *   const { data } = await proxyApi.get("tablename",         
        {
          fields: "field1, field2, field3", // comma-separated list of fields
          filterByFormula: `AND(year="2023",
                              OR(
                                FIND("Best Project", {awards}),
                                FIND("Best Presenter", {awards})
                              )
                            )`,
          maxRecords: 1000,
          pageSize: 100,
          sort: "field1:asc, field2:desc", // comma-separated list of sort fields
          view: "view1",
        },
        true   // set it to true so that it directly calls the backend in prerender as you wont see the backend url when fetch in frontmatter
      );
   * } catch (error) {
   *   widget.alertError("Error in get", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {RequestParams} [params] - Optional parameters for the request.
   * @param {boolean} [isPrerender] - Whether the request is being made in prerender.
   * @returns {Promise<any>} The response data.
   */
  async get(
    table: TableName,
    params?: RequestParams,
    isPrerender: boolean = false
  ): Promise<any> {
    try {
      if (isPrerender) {
        const response = await api.get(table, params);
        const { data, offset } = response;
        return offset ? { data, offset } : data;
      } else {
        return await this.request(
          "get",
          table,
          undefined,
          undefined,
          undefined,
          params
        );
      }
    } catch (error) {
      throw new Error("Error in proxyApi.get: " + error);
    }
  }

  /**
   * Create a new record.
   *
   * @example
   * ```ts
   * try {
   *   const { data } = await proxyApi.post("tablename", { name: "New Record", ... }, false);
   * } catch (error) {
   *   widget.alertError("Error in post", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {any} data - The data for the new record.
   * @param {boolean} [typecast] - Whether to typecast the data. Necessary to create new option in multiple select fields.
   * @returns {Promise<any>} The response data.
   */
  async post(table: TableName, data: any, typecast?: boolean): Promise<any> {
    try {
      return await this.request("post", table, undefined, data, typecast);
    } catch (error) {
      throw new Error("Error in proxyApi.post: " + error);
    }
  }

  /**
   * Destructively overwrite a record by ID.
   *
   * @example
   * ```ts
   * try {
   *   const { data } = 
   *     await proxyApi.put("tablename", "recordid", 
                        { 
                          name: "Updated Record",
                          phone: "************" 
                        }
                      );
   * } catch (error) {
   *   widget.alertError("Error in put", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {string} recordId - The ID of the record to overwrite.
   * @param {any} data - The new data for the record.
   * @returns {Promise<any>} The response data.
   */
  async put(table: TableName, recordId: string, data: any): Promise<any> {
    try {
      return await this.request("put", table, recordId, data);
    } catch (error) {
      throw new Error("Error in proxyApi.put: " + error);
    }
  }

  /**
   * Update a record by ID.
   *
   * @example
   * ```ts
   * try {
   *   const { data } =
   *     await proxyApi.patch("tablename", { name: "Updated Name" }, "recordid", false);
   * } catch (error) {
   *   widget.alertError("Error in patch", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {string} recordId - The ID of the record to update.
   * @param {any} data - The data to update in the record.
   * @param {boolean} [typecast] - Whether to typecast the data.
   * @returns {Promise<any>} The response data.
   */
  async patch(
    table: TableName,
    data: any,
    recordId?: string,
    typecast?: boolean
  ): Promise<any> {
    try {
      return await this.request("patch", table, recordId, data, typecast);
    } catch (error) {
      throw new Error("Error in proxyApi.patch: " + error);
    }
  }

  /**
   * Delete a record by ID.
   *
   * @example
   * ```ts
   * try {
   *   const { data } = await proxyApi.delete("tablename", "recordid");
   * } catch (error) {
   *   widget.alertError("Error in delete", error);
   * }
   * ```
   * @param {TableName} table - The name of the table.
   * @param {string} recordId - The ID of the record to delete.
   * @returns {Promise<any>} The response data.
   */
  async delete(table: TableName, recordId: string): Promise<any> {
    try {
      return await this.request("delete", table, recordId);
    } catch (error) {
      throw new Error("Error in proxyApi.delete: " + error);
    }
  }

  /**
   * Test the connection to the backend.
   *
   * @example
   * ```ts
   * const test = await proxyApi.testBackend();
   * if (test !== "Connected to PIXEL Backend") {
   *   throw new Error("Backend not connected to " + Astro.url.origin);
   * }
   * ```
   * @returns {Promise<any>} The response data.
   */
  async testBackend(): Promise<any> {
    try {
      return await api.testBackend();
    } catch (error) {
      throw new Error("Error in proxyApi.testBackend: " + error);
    }
  }
}

const proxyApi = new ProxyApi();

export { proxyApi };
