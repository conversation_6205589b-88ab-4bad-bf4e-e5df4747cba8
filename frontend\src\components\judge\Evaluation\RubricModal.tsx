import React from "react";
import evaluationSubcategories from "@/data/judging-rubrics.json";

type RubricModalProps = {
  id: string;
  category: string;
};

const RubricModal: React.FC<RubricModalProps> = ({ id, category }) => {
  // Filter rubrics by category
  const categoryRubrics = evaluationSubcategories.filter(rubric => rubric.category === category.toLowerCase());

  return (
    <dialog
      id={id}
      className="daisy-modal daisy-modal-bottom sm:daisy-modal-middle"
    >
      <div className="daisy-modal-box min-w-[60%]">
        <div className="daisy-modal-action sticky top-0 right-0 md:top-2 md:right-2 float-right">
          <form method="dialog">
            <button className="daisy-btn daisy-btn-sm daisy-btn-circle">
              ✕
            </button>
          </form>
        </div>
        <h3 className="font-semibold text-mobile-18 md:text-xl">
          {category} Judging Rubrics
        </h3>
        <div className="py-4">
          <p className="text-sm text-gray-600 mb-4">
            This section evaluates the key aspects of the project including:
          </p>

          <div className="grid gap-4 text-sm text-gray-700">
            {categoryRubrics.map((rubric) => (
              <div key={rubric.id} className={rubric.id !== 0 ? "border-t pt-3" : ""}>
                <p className="font-medium text-gray-800">
                  {rubric.sub_category}
                  <span className="text-sm">
                    &nbsp;&#40;{rubric.max}%&#41;
                  </span>
                </p>
                <p className="mt-1 text-sm text-green-700">
                  ✓ Excellent: {rubric.rubric.excellent.split('.')[0]}.
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
      <form method="dialog" className="daisy-modal-backdrop">
        <button>close</button>
      </form>
    </dialog>
  );
};

export default RubricModal;