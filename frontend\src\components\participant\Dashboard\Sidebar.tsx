import React, { useContext, useEffect, useState } from "react";
import { ParticipantContext } from "./Context";
import { widget, actionSessionStorage, auth } from "@/utils";
import startTour from "@components/participant/participantTour";
import ReadMore from "@components/general/ReadMore";

const Sidebar: React.FC = () => {
  const { user: participant, TEAM_MAX_LIMIT } = useContext(ParticipantContext);

  async function logOut() {
    const confirm = await widget.confirm(
      "Are you sure?",
      "Log out from current session?",
      "Yes, log out"
    );
    if (!confirm.isConfirmed) return;

    actionSessionStorage().clear();
    auth.signOut();
    window.location.href = "/sign-in";
  }

  useEffect(() => {
    const tourCompleted = localStorage.getItem("tourCompleted");
    if (!tourCompleted) {
      startTour(participant.project_id, participant.is_team, TEAM_MAX_LIMIT);
      localStorage.setItem("tourCompleted", "true");
    }
  }, []);

  return (
    <div
      id="sidebar"
      className="bg-white lg:h-full w-full lg:w-[88px] transition-all duration-300 relative group lg:overflow-hidden lg:hover:overflow-visible lg:border-r-2"
    >
      <div className=" flex flex-row lg:flex-col lg:w-[88px] justify-between lg:h-full bg-white lg:absolute z-20 p-5">
        {/* Menu buttons */}
        <div className="flex flex-row lg:flex-col gap-3 ">
          {/* Profile Picture */}
          <div
            className="rounded-full size-12 ring ring-primary-3 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff]"
            title={participant.display_name}
          >
            <div className="size-12 rounded-full max-w-none overflow-hidden bg-white">
              <img
                src={participant.photo}
                alt="Avatar"
                className="h-full w-full object-contain"
              />
            </div>
          </div>
          {/* Edit details */}
          <a
            href="/participant/edit"
            className="flex flex-row items-center justify-center h-12 w-12 rounded-full transition hover:bg-primary-1 hover:text-primary-6 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff] border border-grey-1 hover:border-none "
            title="Edit Profile"
            id="editprofilebtn"
          >
            <i className="fa-solid fa-pen-to-square"></i>
          </a>
          {/* Submit project */}
          <a
            href="/participant"
            className="flex flex-row items-center justify-center h-12 w-12 rounded-full transition hover:bg-primary-1 hover:text-primary-6 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff] border border-grey-1 hover:border-none "
            title="Submit Project"
            id="submitprojectbtn"
          >
            <i className="fa-solid fa-upload"></i>
          </a>
          {/* View Applied Jobs */}
          {/* <a
            href="/participant/jobs-applied"
            className="flex flex-row items-center justify-center h-12 w-12 rounded-full transition hover:bg-primary-1 hover:text-primary-6 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff] border border-grey-1 hover:border-none "
            title="View Job Applications"
            id="jobapplicationbtn"
          >
            <i className="fa-solid fa-briefcase"></i>
          </a> */}
          {/* User Guide */}
          <div
            className="flex flex-row items-center justify-center h-12 w-12 rounded-full transition hover:bg-primary-1 hover:text-primary-6 shadow-[5px_5px_9px_#cfcfcf_,_-5px_-5px_9px_#ffffff] border border-grey-1 hover:border-none cursor-pointer"
            title="User Guide"
            id="userguidebtn"
            onClick={() => {
              startTour(
                participant.project_id,
                participant.is_team,
                TEAM_MAX_LIMIT
              );
            }}
          >
            <i className="fa-solid fa-circle-play"></i>
          </div>
        </div>
        {/* Logout */}
        <div
          className="flex flex-row justify-center items-center rounded-full size-12 bg-red-500 hover:bg-red-600 transition duration-200 ease-in-out hover:cursor-pointer shadow-lg"
          title="Sign Out"
          onClick={logOut}
          id="logoutbtn"
        >
          <i className="fa-solid fa-arrow-right-from-bracket text-white"></i>
        </div>
      </div>

      {/* Profile Section after hovering */}
      <div className="hidden lg:flex flex-col p-5 h-full bg-white absolute top-0 right-0 lg:w-[320px] lg:group-hover:translate-x-[320px] duration-500  transition-all lg:group-hover:z-10 border-r-2 overflow-y-auto">
        <div>
          <p className="font-bold">{participant.display_name}</p>
          <p className="text-mobile-14">{participant.student_email}</p>
        </div>

        <div>
          <div className="mt-8 mb-4">
            <p className="font-bold text-mobile-20 md:text-tablet-20">
              My Profile
            </p>
            <hr className="mt-0 w-full border-t border-black/30" />
          </div>

          <div className="flex flex-col gap-5">
            <div>
              <h3 className="font-bold">Full Name</h3>
              <p className="text-mobile-14">{participant.student_name}</p>
            </div>
            <div>
              <p className="font-bold">Personal Email</p>
              <p className="text-mobile-14">{participant.personal_email}</p>
            </div>
            <div>
              <p className="font-bold">Matric Number</p>
              <p className="text-mobile-14">{participant.matric_no}</p>
            </div>
            <div>
              <p className="font-bold">Major</p>
              <p className="text-mobile-14">{participant.student_major}</p>
            </div>
            {/* Social Media */}
            <div className="w-full">
              <p className="font-bold">Resume</p>
              <div className="flex gap-3 my-1">
                {participant.resume ? (
                  <a
                    href={participant.resume}
                    target="_blank"
                    rel="noreferrer"
                    className="flex items-center justify-center h-8 w-8 bg-black/60 rounded-full hover:bg-black/80"
                    aria-label="resume"
                  >
                    <i className="fa-solid fa-file-lines text-white fa-sm"></i>
                  </a>
                ) : (
                  <span
                    className="flex items-center justify-center h-8 w-8 bg-black/60 hover:bg-black/80 rounded-full cursor-not-allowed opacity-50"
                    aria-label="no-resume"
                  >
                    <i className="fa-solid fa-file-lines text-white fa-sm"></i>
                  </span>
                )}
              </div>
            </div>
            <div>
              <p className="font-bold">About Me</p>
              <ReadMore
                className="w-full text-mobile-14 lg:text-base text-justify whitespace-pre-wrap"
                limit={30}
              >
                {participant.desc}
              </ReadMore>
            </div>
            {/* Social Media */}
            <div className="w-full">
              <p className="font-bold">Social Network</p>
              <div className="flex gap-3 my-1">
                <a
                  href={participant.github}
                  target="_blank"
                  rel="noreferrer"
                  className="flex items-center justify-center h-8 w-8 bg-black/60 rounded-full hover:bg-black/80"
                  aria-label="github"
                >
                  <i className="fa-brands fa-github text-white fa-sm"></i>
                </a>
                <a
                  href={participant.linkedin}
                  target="_blank"
                  rel="noreferrer"
                  className="flex items-center justify-center h-8 w-8 bg-black/60 rounded-full hover:bg-black/80"
                  aria-label="linkedin"
                >
                  <i className="fa-brands fa-linkedin-in text-white fa-sm"></i>
                </a>
                {participant.portfolio && (
                  <a
                    href={participant.portfolio}
                    target="_blank"
                    rel="noreferrer"
                    className="flex items-center justify-center h-8 w-8 bg-black/60 rounded-full hover:bg-black/80"
                    aria-label="portfolio"
                  >
                    <i className="fa-solid fa-globe text-white fa-sm"></i>
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Sidebar;
