---
import SideLayout2 from "@layouts/sideLayout2.astro";
import WinnerCard from "@components/awards/WinnerCard";
import AwardList from "@components/awards/AwardList";
import { categories } from "../category";
import { proxyApi, mapProject } from "@/utils";

export const prerender = true;

const { year, major } = Astro.params;

export function getStaticPaths() {
  var currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
  var isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";
  var adjustedYear = !isPostEvent ? parseInt(currentYear) - 1 : parseInt(currentYear);

  const years = [];
  for (let y = 2023; y <= adjustedYear; y++) {
    years.push(y.toString());
  }

  const paths = [];

  for (const year of years) {
    for (const category of categories) {
      paths.push({
        params: { major: category.name, year: year },
      });
    }
  }

  return paths;
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

if (!isPostEvent && year >= currentYear) {
  return Astro.redirect("/awards/" + (parseInt(currentYear) - 1));
}

if (!categories.some((category) => category.name === major)) {
  return Astro.redirect("/awards");
}

let query: string;
if (major === "Best Presenter") {
  // filter by year and awards column include "Best Presenter"
  query = `AND(year="${year}",  awards!="", FIND("${major}", {awards}))`;
} else {
  // filter by year and major
  query = `AND(year="${year}",  awards!="", project_major="${major}")`;
}

const result = await proxyApi.get("projects", { filterByFormula: query }, true);

if (!result) {
  return Astro.redirect("/awards/" + year);
}

const awardTypes = [
  "Best Presenter",
  "Best Project",
  "1st Runner Up",
  "2nd Runner Up",
  "Gold",
  "Silver",
  "Bronze",
];
const awardsByTypes = {};
// Filter the records based on the awards
awardTypes.forEach((winner) => {
  awardsByTypes[winner] = result.filter(
    (record) => record.awards && record.awards.includes(winner)
  );
});

const isBestPresenterAward = major === "Best Presenter";

// map the data for each project to match the interface of Project
Object.keys(awardsByTypes).forEach((award) => {
  awardsByTypes[award] = awardsByTypes[award].map((project) => {
    return mapProject(project);
  });
});

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: `Awards ${year}`, path: "/awards/" + year},
  { name: major, path: `/awards/${year}/${major.split(" ").join("%20")}`}
];
---

<SideLayout2 title=`${year} ${major} AwardList` {breadcrumbs}>
  <section class="flex flex-col items-center justify-between w-full">
    <div
      class="md:relative overflow-clip bg-white flex max-md:flex-row max-md:items-center md:flex-col md:items-start border-2 border-black rounded-t-lg w-full p-6 gap-4"
    >
      <!-- floating big icon -->
      <i
        class={`max-md:hidden md:absolute -z-1 text-[320px] -bottom-20 -right-10 !text-black opacity-15 ${categories.find((category) => category.name === major)?.icon}`}
      ></i>

      <div class="flex flex-row items-center justify-between w-full">
        <div
          class="flex flex-row md:flex-col items-center md:items-start max-md:gap-4"
        >
          <i
            class={`text-mobile-24 lg:text-4xl md:w-fit p-4 bg-black/5 rounded-lg ${categories.find((category) => category.name === major)?.icon}`}
          ></i>
          <span class="max-md:hidden md:block mt-4 font-semibold text-tablet-14"
            >Major</span
          >
          <h1
            class="font-semibold text-mobile-24 md:text-tablet-34 lg:text-desktop-48 max-md:max-w-[10ch] md:mt-2"
          >
            {major}
          </h1>
        </div>
        <span
          class="max-md:block md:hidden items-center font-medium text-mobile-14 text-black"
          >{result.length}</span
        >
      </div>
    </div>

    <!-- black strip -->
    <div
      class="flex flex-row items-center max-md:justify-center md:justify-between w-full px-6 py-3 bg-black rounded-b-lg"
    >
      <div class="max-md:hidden md:block flex flex-row items-end gap-2">
        <span
          class="inline !font-semibold text-tablet-24 lg:text-desktop-34 text-white"
        >
          {result.length}
        </span>
        <span class="text-tablet-16 lg:text-desktop-24 font-medium text-white"
          >{result.length === 1 ? "Project" : "Projects"}</span
        >
      </div>
      <div class="flex flex-row justify-center md:justify-end gap-4">
        {
          !isBestPresenterAward && (
            <>
              <div class="flex flex-row items-center lg:items-end gap-2">
                <span class="max-md:text-mobile-24 md:text-tablet-34 lg:text-desktop-34 text-yellow-300 !font-bold">
                  {awardsByTypes["Gold"].length}
                </span>
                <span class="max-md:text-mobile-16 md:text-tablet-20 lg:text-desktop-24 !font-bold text-yellow-300">
                  GOLD
                </span>
              </div>
              {awardsByTypes["Silver"].length > 0 && (
                <div class="flex flex-row items-center lg:items-end gap-2">
                  <span class="max-md:text-mobile-24 md:text-tablet-34 lg:text-desktop-34 text-gray-300 !font-bold">
                    {awardsByTypes["Silver"].length}
                  </span>
                  <span class="max-md:text-mobile-16 md:text-tablet-20 lg:text-desktop-24 !font-bold text-gray-300">
                    SILVER
                  </span>
                </div>
              )}
              {awardsByTypes["Bronze"].length > 0 && (
                <div class="flex flex-row items-center lg:items-end gap-2">
                  <span class="max-md:text-mobile-24 md:text-tablet-34 lg:text-desktop-34 text-amber-700 !font-bold">
                    {awardsByTypes["Bronze"].length}
                  </span>
                  <span class="max-md:text-mobile-16 md:text-tablet-20 lg:text-desktop-24 !font-bold text-amber-700">
                    BRONZE
                  </span>
                </div>
              )}
            </>
          )
        }
      </div>
    </div>
  </section>

  {
    isBestPresenterAward ? (
      <div class="mt-16 md:mt-20">
        {/* [0] because there's only 1 person for that award*/}
        <WinnerCard
          project={awardsByTypes["Best Presenter"][0]}
          award_title="BEST PRESENTER"
          horizontal={true}
          client:only="react"
        />
      </div>
    ) : (
      <div class="mt-16 md:mt-20">
        <WinnerCard
          project={awardsByTypes["Best Project"][0]}
          award_title="BEST PROJECT"
          horizontal={true}
          client:only="react"
        />
        <div class="flex flex-col md:flex-row gap-16 md:gap-8 mt-16 md:mt-24">
          <WinnerCard
            project={awardsByTypes["1st Runner Up"][0]}
            award_title="2ND PLACE"
            client:only="react"
          />
          <WinnerCard
            project={awardsByTypes["2nd Runner Up"][0]}
            award_title="3RD PLACE"
            client:only="react"
          />
        </div>
        <section class="flex flex-col mt-20 md:mt-40 lg:mt-32 gap-10">
          {["Gold", "Silver", "Bronze"].map(
            (award) =>
              awardsByTypes[award].length > 0 && (
                <AwardList
                  client:only="react"
                  year={year}
                  award_title={`${award.toUpperCase()} AWARD`}
                  projects={awardsByTypes[award]}
                />
              )
          )}
        </section>
      </div>
    )
  }
</SideLayout2>
