import CryptoJS from "crypto-js";

export function compressedImage(profile_pic: File) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const exportedQuality = 0.7;

    const file = profile_pic;
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = function (event) {
      const img = new Image();
      img.src = reader.result as string;
      img.onload = function () {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const imagetowebp = canvas.toBlob(
          (blob: any) => {
            const url = URL.createObjectURL(blob);
            const webpFile = new File([blob], file.name, {
              type: "image/webp",
            });

            if (webpFile) {
              resolve(webpFile);
            } else {
              reject("error");
            }
          },
          "image/webp",
          exportedQuality
        );
      };
    };
  });
}

export function actionSessionStorage() {
  type SessionKey = "userData" | "judgeData" | "projectData" | "version";

  // actionSessionStorage().create("userData", {
  // 	name: "Astro User",
  // 	role: "Developer",
  // 	joined: new Date().toLocaleDateString(),
  // });
  function create(key: SessionKey, data: any) {
    try {
      sessionStorage.setItem(key, encrypt(JSON.stringify(data)));
    } catch (error) {
      throw new Error("Error in actionSessionStorage");
    }
  }

  // let data = actionSessionStorage().get("userData")
  function get(key: SessionKey) {
    const data = sessionStorage.getItem(key)!;

    if (data) {
      return JSON.parse(decrypt(data));
      // return JSON.parse(data);
    } else {
      return null;
    }
  }

  // actionSessionStorage().update("userData", {
  // 	role: "Frontend Developer",
  // });
  function update(key: SessionKey, data: any) {
    const response = sessionStorage.getItem(key)!;

    if (response) {
      const decode = JSON.parse(decrypt(response));
      create(key, { ...decode, ...data });
    } else {
      return null;
    }
  }

  // actionSessionStorage().destroy("userData");
  function destroy(key: SessionKey) {
    sessionStorage.removeItem(key);
  }

  // actionSessionStorage().clear();
  function clear(keepVersion: boolean = true) {
    if (keepVersion) {
      const version = sessionStorage.getItem("version");
      sessionStorage.clear();
      if (version) {
        sessionStorage.setItem("version", version);
      }
    } else {
      // clear all session storage
      sessionStorage.clear();
    }
  }

  return {
    create,
    get,
    update,
    destroy,
    clear,
  };
}

/**
 * Remove any undefined data and convert class instance into object
 * @param data object that need to be sanitize
 * @returns sanitized object
 */
export function sanitizeData(data: any) {
  const temp: any = {};
  deletePropertiesOfUndefined(data);
  copyObject(data, temp);
  return temp;
}

/**
 * Remove any undefined or null data from object
 * @param data object that needed it undefined data to be removed
 */
function deletePropertiesOfUndefined(data: any) {
  for (const key in data) {
    if (data[key] === undefined || data[key] === null) {
      delete data[key];
    } else if (typeof data[key] === "object") {
      deletePropertiesOfUndefined(data[key]);
      if (Object.keys(data[key]).length === 0) {
        delete data[key];
      }
    }
  }
}

/**
 * Convert class instance to object form
 * @param data source data that want to be replicated
 * @param temp object that the converted data will be stored
 */
function copyObject(data: any, temp: any) {
  for (const key in data) {
    if (key === "created_at" || key === "updated_at") {
      temp[key] = data[key];
      continue;
    }
    if (Array.isArray(data[key])) {
      temp[key] = [];
      for (let i = 0; i < data[key].length; i++) {
        if (typeof data[key][i] === "object") {
          temp[key][i] = {};
          copyObject(data[key][i], temp[key][i]);
        } else {
          temp[key][i] = data[key][i];
        }
      }
    } else if (typeof data[key] === "object") {
      temp[key] = {};
      copyObject(data[key], temp[key]);
    } else {
      temp[key] = data[key];
    }
  }
}

export function encrypt(data: any) {
  // set the teamDoc data to session storage for caching with encryption
  const encoded = CryptoJS.AES.encrypt(
    data,
    import.meta.env.PUBLIC_ENCRYPTION_KEY as string
  ).toString();
  return encoded
    .replace(/\+/g, "p1L2u3S")
    .replace(/\//g, "s1L2a3S4h")
    .replace(/=/g, "e1Q2u3A4l");
}

export function decrypt(encoded: string) {
  const decoded = CryptoJS.AES.decrypt(
    encoded
      .replace(/p1L2u3S/g, "+")
      .replace(/s1L2a3S4h/g, "/")
      .replace(/e1Q2u3A4l/g, "="),
    import.meta.env.PUBLIC_ENCRYPTION_KEY as string
  ).toString(CryptoJS.enc.Utf8);
  return decoded;
}

export function encryptProject(text: string) {
  const chunkSize = Number(import.meta.env.PUBLIC_PROJECT_KEY);
  let temp = [];

  while (text?.length > 0) {
    temp.push(text.substring(0, chunkSize));
    text = text.substring(chunkSize);
  }

  return temp.reverse().join("");
}
//--------------------------------------------------------------

export function showButtonLoading(e: any, state: boolean, text: any) {
  if (e.target !== undefined) e = e.target;
  if (state) {
    e.setAttribute("disabled", "disabled");
    e.innerHTML =
      '<i class="fa-solid fa-circle-notch fa-spin text-cs-white"></i> ';
  } else {
    e.removeAttribute("disabled");
    e.innerHTML = text;
  }
}

// shuffleArray([1, 2, 3, 4]).then((shuffled) => console.log(shuffled));
export async function shuffleArray(array: any): Promise<any> {
  return new Promise((resolve) => {
    if (array) {
      // array = Array.from({ length: 5 }, () => array).flat();

      for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
      }

      resolve(Array.from(array)); // return new array reference
    } else {
      throw new Error("Array is empty");
    }
  });
}

// const array = [
//   { name: 'John', age: 30 },
//   { name: 'Alice', age: 25 },
//   { name: 'Bob', age: 35 },
// ];
//
// sortArray(array, 'name', 'asc')
//   .then(sortedArray => console.log(sortedArray))
//   .catch(error => console.error(error));
export async function sortArray<T>(args: {
  array: T[];
  key: keyof T;
  type?: "asc" | "desc";
  skipError?: boolean;
}): Promise<T[]> {
  const { array, key, type = "asc", skipError = false } = args;
  return new Promise((resolve) => {
    if (array) {
      array.sort((a: T, b: T) => {
        const x = a[key];
        const y = b[key];
        if (typeof x === "string" && typeof y === "string") {
          if (type === "asc") return x.localeCompare(y);
          else return y.localeCompare(x);
        } else if (typeof x === "number" && typeof y === "number") {
          if (type === "asc") return x - y;
          else return y - x;
        } else {
          return 0;
        }
      });
      resolve(Array.from(array));
    } else {
      if (!skipError) {
        throw new Error("Array is empty");
      } else {
        console.warn("Array is empty");
        resolve([]);
      }
    }
  });
}
