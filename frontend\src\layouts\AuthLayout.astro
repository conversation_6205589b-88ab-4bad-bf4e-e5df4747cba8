---
import "./layout.css";

export interface Props {
  title: string;
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <meta name="color-scheme" content="light only" />
    <link rel="icon" type="image/svg+xml" href="/assets/favicon/favicon.ico" />

    <!-- FontAwesome cdn js -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css"
      integrity="sha512-MV7K8+y+gLIBoVD59lQIYicR65iaqukzvf/nwasF0nqhPay5w/9lJmVM2hMDcnK1OnMGCdVK+iQrJ7lzPJQd1w=="
      crossorigin="anonymous"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/cropperjs@1.5.13/dist/cropper.min.css"
      rel="stylesheet"
      crossorigin="anonymous"
    />

    <link
      href="https://cdn.jsdelivr.net/npm/cropperjs@1.5.13/dist/cropper.min.css"
      rel="stylesheet"
    />
    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>
    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body>
    <div id="main-modal"></div>

    <main
      class="w-full flex justify-center items-center max-lg:min-h-[100dvh] lg:min-h-screen lg:max-xl:px-5 lg:py-8 container-max-w"
    >
      <div class="flex justify-center items-center w-full shadow-lg bg-white rounded-md">
        <slot />
      </div>
    </main>
    <script src="../../node_modules/flowbite/dist/flowbite.min.js"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/cropperjs@1.5.13/dist/cropper.min.js"
    ></script>
  </body>
</html>

<style is:inline>
  body::before {
    content: "";
    position: fixed;
    z-index: -100;
    inset: 0;
    width: 100%;
    height: 100%;
    background-color: #F5EBFC;
    background-image: url("/assets/images/bg/dashboard-bg.svg");
    background-size: cover;
  }
</style>