import Rating from "@mui/material/Rating";
import type { Dispatch, SetStateAction } from "react";
import { useState } from "react";
import { EvaluationData } from "@modules/judge";

import evaluationSubcategories from "@data/judging-rubrics.json";

type EvaluationSummaryProps = {
  category: Record<string, string[]> | null;
  data: Partial<EvaluationData>;
};

const evaluationCategories = Array.from(
  new Map(
    (evaluationSubcategories as Array<any>).map((item) => [
      item.category,
      {
        label: item.category.charAt(0).toUpperCase() + item.category.slice(1),
        key: item.category,
        max: 0,
      },
    ])
  ).values()
).map((cat) => ({
  ...cat,
  max: (evaluationSubcategories as Array<any>)
    .filter((item) => item.category === cat.key)
    .reduce((sum, item) => sum + (item.max || 0), 0),
}));

function EvaluationSummary({ category, data }: EvaluationSummaryProps) {
  // Exclude the 'comments' field from data
  const { comments, ...safeData } = data ?? {};

  return (
    <>
      {evaluationCategories.map(({ label, key, max }) => (
        <div
          key={key}
          className="w-full flex flex-col md:flex-row justify-center items-center md:justify-between md:gap-x-32 gap-y-5"
        >
          <div className="flex flex-row gap-2 items-center">
            <p className="text-base font-medium">{label}</p>
          </div>
          {(category?.[key] || [])
            .map((k) => Number(safeData[k as keyof EvaluationData]) || 0)
            .reduce((a, b) => a + b, 0)}
          /{max}
        </div>
      ))}

      <div className="w-full flex flex-col md:flex-row justify-center items-center md:justify-between md:gap-x-32 gap-y-5 border-y-2 border-dotted border-[rgba(0,0,0,0.2)] py-4">
        <div className="flex flex-row gap-2 items-center">
          <p className="text-xl font-bold">Total</p>
        </div>
        <span>
          {Object.keys(safeData).length > 0
            ? Object.values(safeData).reduce(
                (a, b) => (a as number) + (b as number),
                0
              )
            : 0}
          /{evaluationCategories.reduce((sum, { max }) => sum + max, 0)}
        </span>
      </div>
    </>
  );
}

export default EvaluationSummary;
