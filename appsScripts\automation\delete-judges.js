/**
 * This script automates the deletion of judge accounts from Firebase Authentication.
 * It reads UIDs from a Google Sheet and deletes the corresponding Firebase Auth accounts.
 * 
 * @prerequisites
 * - Firebase service account JSON file located at './service-account.json'
 * - Google Sheets API enabled in Google Cloud Console
 * - Proper permissions for the service account (Firebase Admin, Google Sheets API access)
 * 
 * @spreadsheet_format
 * The spreadsheet must have the following columns:
 * - Column A: UID (Firebase Auth UIDs to be deleted)
 * 
 * @usage
 * 1. Ensure the Google Sheet has UIDs in Column A
 * 2. Update the SPREADSHEET_ID constant with your Google Sheet ID
 * 3. Ensure the service account has necessary permissions
 * 4. Run: node delete-judges.js
 * 
 * @important_notes
 * - THIS DELETES ACTUAL FIREBASE ACCOUNTS - use with extreme caution
 * - The script will process all UIDs in Column A starting from A2
 * - Error handling is implemented for individual accounts, so the script continues even if one deletion fails
 */

const admin = require('firebase-admin');
const { google } = require('googleapis');
const sheets = google.sheets('v4');
const path = require('path');

// Get absolute path to service account file
const serviceAccountPath = path.resolve(__dirname, 'service-account.json');

// Initialize Firebase Admin with service account
const serviceAccount = require(serviceAccountPath);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

async function deleteJudgeAccounts() {
  try {
    // 1. Read from Google Sheet
    const auth = new google.auth.GoogleAuth({
      keyFile: serviceAccountPath,
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    
    const sheetsClient = await auth.getClient();
    const spreadsheetId = '1DKZ5AMgljL0gRjTZy98e47yWPY5x5SM8DL5ji2hFNoU';
    const range = 'judges!A2:A'; // Only read UIDs from Column A

    const response = await sheets.spreadsheets.values.get({
      auth: sheetsClient,
      spreadsheetId,
      range,
    });

    const rows = response.data.values;
    if (!rows || rows.length === 0) {
      console.log('No UIDs found in sheet');
      return;
    }

    // 2. Delete accounts
    console.log(`Found ${rows.length} UIDs to process`);
    
    for (const row of rows) {
      const uid = row[0];
      if (!uid) {
        console.log('Skipping empty UID');
        continue;
      }

      try {
        // Delete the user from Firebase Auth
        await admin.auth().deleteUser(uid);
        console.log(`Successfully deleted account with UID: ${uid}`);
      } catch (error) {
        console.error(`Error deleting account with UID ${uid}:`, error.message);
      }
    }

    console.log('\nDeletion process completed!');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
deleteJudgeAccounts(); 