---

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

---

<div class="h-full pt-10 md:pt-16 lg:pt-24 px-4 md:px-5  gap-y-[-20]  pb-5 bg-[#0c151a] mx-4 mb-4 md:mx-5 md:mb-5 rounded-2xl">
  <div class="flex flex-col md:flex-row w-full h-full justify-between container-max-w">
    <div class="flex flex-row text-white lg:w-[70%] justify-between items-center">
      <!-- pixel logo -->
      <button id="top-btn" class="min-w-[100px] w-[30%] md:w-full">
        <img src="/assets/images/logo/PIXEL-logo.webp" alt="PIXEL Logo" />
      </button>
      <!-- MOBILE NAVIGATION -->
      <div class="md:hidden flex items-end">
        <button
          type="button"
          id="top-btn"
          class="flex justify-center items-center w-10 h-10 rounded-full text-white border-white transition duration-100 cursor-pointer hover:border-primary-6 hover:text-primary-6 border-2"
        >
          <i class="fa-solid fa-angle-up fa-md text-2xl"></i>
        </button>
      </div>
    </div>

    <!-- MEDIUM NAVIGATION -->
    <div class="text-white">
      <div class="hidden md:flex justify-start mt-8 sm:justify-end sm:mb-8 sm:mt-0">
        <button
          title="Back to top"
          type="button"
          id="top-btn"
          class="flex justify-center items-center w-12 h-12 rounded-full text-white border-white transition duration-100 cursor-pointer hover:border-primary-6 hover:text-primary-6 border-2"
        >
          <i class="fa-solid fa-angle-up fa-md text-2xl"></i>
        </button>
      </div>

      <!-- social media -->
      <div
        class="w-full flex flex-col md:flex-row gap-5 justify-start md:justify-between my-5 md:my-0"
      >
        <div
          class="w-full md:w-[50%] text-light uppercase text-sm md:text-base text-gray-300 text-center md:text-left"
        >
          <p class="mb-3 text-white">Follow Us</p><hr class="w-full border-white" />
        </div>
        <div class="h-10 w-full justify-center md:justify-end md:w-auto flex gap-3 text-white">
          <div class="opacity-80 hover:opacity-100 duration-200">
            <a href="https://www.facebook.com/usm.pixel" target="_blank"
              ><i class="text-3xl md:text-4xl h-full fa-brands fa-facebook text-white hover:text-primary-6 transition duration-200"></i></a
            >
          </div>
          <div class="opacity-80 hover:opacity-100 duration-200">
            <a href="https://www.instagram.com/pixel.usm/" target="_blank"
              ><i class="text-3xl md:text-4xl h-full fa-brands fa-instagram text-white hover:text-primary-6 transition duration-200"></i></a
            >
          </div>
          <div class="opacity-80 hover:opacity-100 duration-200">
            <a href="https://www.linkedin.com/company/pixel-usm/" target="_blank"
              ><i class="text-3xl md:text-4xl h-full fa-brands fa-linkedin text-white hover:text-primary-6 transition duration-200"></i></a
            >
          </div>
        </div>
      </div>

      <!-- organization logos -->
      <div
        class="h-12 sm:h-14 lg:h-16 w-full justify-center md:justify-end md:w-auto flex gap-3 text-white bg-primary-1 rounded-lg px-4 py-2 mt-5"
      >
        <div class="">
          <a href="http://www.usm.my/" target="_blank" aria-label="Universiti Sains Malaysia"
            ><img
              class="object-scale-down h-full"
              src="/assets/images/organization/usm.png"
              alt="usm"
              width="100%"
              height="100%"
            /></a
          >
        </div>
        <div class="">
          <a href="https://cs.usm.my/" target="_blank" aria-label="School of Computer Sciences"
            ><img
              class="object-scale-down h-full"
              src="/assets/images/organization/school.png"
              alt="school"
              width="100%"
              height="100%"
            /></a
          >
        </div>
        <div class="">
          <a href="https://cssocietyusm.com/" target="_blank" aria-label="USM Computer Science Society Website"
            ><img
              class="object-scale-down h-full"
              src="/assets/images/organization/css.png"
              alt="css"
              width="100%"
              height="100%"
            /></a
          >
        </div>
      </div>
    </div>
  </div>
  <div class="text-xs md:text-mobile-14 text-white opacity-90 text-center mt-16">
    © {currentYear} PIXEL & School of Computer Sciences, Universiti Sains Malaysia
    <br /> Disclaimer: All contents, intellectual properties & copyright reserved to Universiti Sains
    Malaysia (USM)
  </div>
</div>

<script>
  const buttons = document.querySelectorAll("#top-btn");
  buttons.forEach((button) => {
    button.addEventListener("click", () => {
      topFunction();
    });
  });
  function topFunction() {
    document.body.scrollTop = 0; // For Safari
    document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
  }
</script>
