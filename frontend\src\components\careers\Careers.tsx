import React, { useState, useEffect, useRef } from "react";
import CareerFilter from "./CareerFilter";
import CareerCard from "./CareerCard";
import { jobFactory, type Job } from "@/modules";
import { createPortal } from "react-dom";
import { careerContext } from "./Context";
import BaseModal from "@components/general/BaseModal";
import CareerDetail from "./CareerDetail";
import type { ParticipantInfo } from "@/modules";
import { authFactory, participantFactory, Participant } from "@/modules";
import {
  widget,
  auth,
  compressedImage,
  actionSessionStorage,
  googleDriveUpload,
} from "@/utils";
import type { Team, ParticipantSession } from "@/modules";

type Props = {
  careers: Job[];
};

const Careers: React.FC<Props> = ({ careers }) => {
  // const user: Partial<Participant> = actionSessionStorage().get("userData");
  const allJobs = useRef(careers);
  const firstTimeRender = useRef(false);
  const jobDetailRef = useRef<any>(null);
  const filterRef = useRef<any>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [search, setSearch] = useState("");  // Search state
  const [sort, setSort] = useState("default");
  const [selectedJob, setSelectedJob] = useState<Job | undefined>(undefined);
  const [searchContainer, setSearchContainer] = useState<HTMLElement | null>(
    null
  );
  const [filters, setFilters] = useState({
    mode: "",
    type: "",
    location: "",
  });
  const [user, setUser] = useState({} as ParticipantSession);
  const [userUid, setUserUid] = useState<string | undefined>(user.firebase_uid);
  const [jobsApplied, setJobsApplied] = useState<string[]>([]);

  // SSR-safe document access
  useEffect(() => {
    if (typeof document !== "undefined") {
      const element = document.getElementById("search-input-container");
      setSearchContainer(element);
    }
  }, []);

  // On component mount, set all jobs
  useEffect(() => {
    setIsLoading(true);
    (async () => {
      setJobs(careers);
      setIsLoading(false);
    })();
    firstTimeRender.current = true;
  }, [careers]);

  // Update the job list based on search query and filters
  useEffect(() => {
    setIsLoading(true);
    const filteredJobs = allJobs.current.filter((job) => {
      const lowerCaseSearch = search.toLowerCase();
      const matchesSearch =
        job.name.toLowerCase().includes(lowerCaseSearch) ||
        job.company?.name.toLowerCase().includes(lowerCaseSearch) ||
        job.location.toLowerCase().includes(lowerCaseSearch);

      const matchesFilters =
        (!filters.mode || job.mode === filters.mode) &&
        (!filters.type || job.type === filters.type) &&
        (!filters.location || job.location === filters.location);

      return matchesSearch && matchesFilters;
    });

    // Apply sorting
    let sortedJobs = [...filteredJobs];
    if (sort === "name") {
      sortedJobs = sortedJobs.sort((a, b) => a.name.localeCompare(b.name));
    } else if (sort === "company_name") {
      sortedJobs = sortedJobs.sort((a, b) =>
        a.company?.name.localeCompare(b.company?.name || "")
      );
    }

    setJobs(sortedJobs);
    setIsLoading(false);
  }, [search, sort, filters]);

  function openModal(id: string) {
    setSelectedJob(jobs.find((job) => job.id === id));
    jobDetailRef.current?.show();
  }

  useEffect(() => {
    const storedUser: ParticipantSession =
      actionSessionStorage().get("userData");

    // console.log("Stored user from sessionStorage:", storedUser);
    // console.log("job_application_id:", storedUser?.job_application_id);
    // console.log(
    //   "typeof job_application_id:",
    //   typeof storedUser?.job_application_id
    // );

    if (storedUser) {
      setUser(storedUser);

      // console.log(
      //   "storedUser.job_application_id:",
      //   storedUser.job_application_id
      // );

      // Ensure job_application_id is an array before filtering
      const jobIds = Array.isArray(storedUser.job_application_id)
        ? storedUser.job_application_id
        : typeof storedUser.job_application_id === "string"
          ? storedUser.job_application_id === ""
            ? []
            : (storedUser.job_application_id as string)
                .split(",")
                .map((id) => id.trim())
          : [];

      setJobsApplied(jobIds.filter((id) => id !== "__EMPTY__"));
    }
  }, []);

  const handleJobApply = async (jobId) => {
    // console.log("Applying job with jobId ", jobId);
    if (!user) {
      setTimeout(() => {
        const modal = document.getElementById(
          `job_modal_${jobId}`
        ) as HTMLDialogElement;
        if (modal && modal.open) {
          modal.close();
        }
      }, 50); // short delay
      widget
        .alertError(
          "You are not registered",
          "Please make sure you are registered before applying for any job.",
          undefined,
          3000
        )
        .then(() => {
          window.location.href = "/sign-in";
        });
      return; // prevent the next block from executing
    }

    if (!user.resume) {
      setTimeout(() => {
        const modal = document.getElementById(
          `job_modal_${jobId}`
        ) as HTMLDialogElement;
        if (modal && modal.open) {
          modal.close();
        }
      }, 50); // short delay
      
      const result = await widget.confirm(
        "Resume needed",
        "You need to upload your resume before applying for jobs. Would you like to update your profile now?",
        "Update Resume"
      );
      
      if (result.isConfirmed) {
        window.location.href = "/participant";
      }
      return;
    }

    //
    try {
      widget.loading();
      // 1. Compute the updated jobs array
      const updatedJobsApplied = [...jobsApplied];
      updatedJobsApplied.push(jobId);

      // 2. Create a new user object with updated job_application_id
      const updatedUser: Partial<ParticipantSession> = {
        job_application_id:
          updatedJobsApplied.length > 0 ? updatedJobsApplied : ["__EMPTY__"],
      };

      // console.log("updatedUser ");
      // console.log(updatedUser);
      // 3. Update session/backend first
      await authFactory().updateAccount(updatedUser, user.firebase_uid);
      (
        document.getElementById(`job_modal_${jobId}`) as HTMLDialogElement
      ).close();

      // 4. Then update the frontend state
      setJobsApplied(updatedJobsApplied);

      await widget.alertSuccess("Success", "The job has been applied.");
      // window.location.href = "/participant/jobs-applied";
    } catch (error: any) {
      await widget.alertError("Error in updateUserAccount", error);
    }
  };

  // remove the job from airtable and update state
  const removeJob = async (jobId) => {
    // console.log("Removing job with jobId ", jobId);
    if (!user) {
      setTimeout(() => {
        const modal = document.getElementById(
          `job_modal_${jobId}`
        ) as HTMLDialogElement;
        if (modal && modal.open) {
          modal.close();
        }
      }, 50); // short delay
      widget
        .alertError(
          "You are not registered",
          "Please make sure you are registered before applying for any job.",
          undefined,
          3000
        )
        .then(() => {
          window.location.href = "/sign-in";
        });
    }

    try {
      widget.loading();
      // 1. Compute the updated jobs array
      const updatedJobsApplied = jobsApplied.filter((job) => job !== jobId);

      // 2. Create a new user object with updated job_application_id
      const updatedUser: Partial<ParticipantSession> = {
        job_application_id:
          updatedJobsApplied.length > 0 ? updatedJobsApplied : ["__EMPTY__"],
      };

      // console.log("updatedUser ");
      // console.log(updatedUser);
      // 3. Update session/backend first
      await authFactory().updateAccount(updatedUser, user.firebase_uid);
      (
        document.getElementById(`job_modal_${jobId}`) as HTMLDialogElement
      ).close();
      // 4. Then update the frontend state
      setJobsApplied(updatedJobsApplied);

      await widget.alertSuccess("Success", "The job has been removed.");
      // window.location.href = "/participant/jobs-applied";
    } catch (error: any) {
      await widget.alertError("Error in updateUserAccount", error);
    }
  };

  const CareerFooter = (company_name) => {
    return (
      <a
        href="/participant/jobs-applied"
        className="py-2 px-6 bg-primary-6 text-white rounded-full hover:bg-primary-7 transition-all duration-200 hover:shadow-lg"
      >
        Apply Now
      </a>
    );
  };

  return (
    <careerContext.Provider
      value={{
        sort,
        updateSort: setSort,
        openModal,
        jobs: allJobs.current,
        updateFilters: setFilters,
      }}
    >
      <BaseModal
        ref={jobDetailRef}
        footer={
          <CareerFooter company_name={(selectedJob as any)?.company_name} />
        }
      >
        {selectedJob && <CareerDetail job={selectedJob} />}
      </BaseModal>

      {/* Filter Modal */}
      <CareerFilter ref={filterRef} />

      {/* Search and Filter Section */}
      <div className="w-full max-w-7xl mx-auto px-4">
        <div className="rounded-2xl px-6">
          <div className="flex items-center gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <i className="fa-solid fa-magnifying-glass absolute left-4 top-1/2 -translate-y-1/2 text-primary-6"></i>
                <input
                  type="text"
                  className="w-full pl-12 pr-4 py-3 bg-primary-1 border-none rounded-full focus:outline-none focus:ring-2 focus:ring-primary-5 text-grey-5 placeholder-grey-4"
                  placeholder="Search by job title, company name, or location..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                />
              </div>
            </div>

            {/* Filter Button */}
            <button
              onClick={() => filterRef.current?.show()}
              className="p-3 text-primary-6 rounded-full hover:text-primary-7 transition-colors"
              title="Filter & Sort"
            >
              <i className="fa-solid fa-sliders text-lg"></i>
            </button>
          </div>

          {/* Results Count */}
          <div className="flex justify-between items-center mt-4">
            <div className="text-gray-400 px-4 py-2 text-sm">
              <span className="text-sm text-gray-400">{jobs.length}</span> jobs
              found
            </div>
          </div>
        </div>
      </div>

      {/* Job Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 px-4 max-w-7xl mx-auto mt-8">
        {jobs.map((job, i) => (
          <CareerCard
            key={"CareerCard-" + i}
            job={job}
            handleJobApply={handleJobApply}
            removeJob={removeJob}
            isApplied={jobsApplied.includes(job.id)}
          />
        ))}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="fixed inset-0 bg-white/50 flex items-center justify-center z-50">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary-6 border-t-transparent"></div>
        </div>
      )}
    </careerContext.Provider>
  );
};

export default Careers;
