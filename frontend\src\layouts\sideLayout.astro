---
import Navbar from "@components/navbar/Navbar.astro";
import Footer from "@components/landing/Footer.astro";
import "./layout.css";
import Breadcrumb, { Breadcrumb as BreadcrumbType } from "@components/general/Breadcrumb.astro";

export interface Props {
  title: string;
  pageDesc: string;
  hasMenu?: boolean;
  breadcrumbs?: BreadcrumbType[];
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title, pageDesc, hasMenu = true, breadcrumbs } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society" />
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

    <meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025" />
    <meta property="og:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp" />
    <meta property="og:url" content="https://pixelusm.com/" />

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta name="twitter:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp" />

    <meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />

    <!-- FontAwesome cdn css -->
    <link
      rel="stylesheet"
      href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css"
    />

    <!-- GSAP cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
    ></script>

    <!-- ScrollTrigger cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"
    ></script>

    <script>
      //@ts-ignore
      gsap.registerPlugin(ScrollTrigger);
    </script>

    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>

    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body class="overflow-x-hidden">
    <!-- main-modal is for modal window, leave it -->
    <div id="main-modal"></div>
    <Navbar />
    <div class="h-20 bg-bgColor"></div>
    <main class="my-16 md:my-20 lg:my-20 px-4 md:px-5 xl:px-0 container-max-w relative">
      <div
        class="max-md:hidden fixed md:right-[-40rem] lg:right-[-38rem] xl:right-[-20rem] min-[2200px]:right-[0rem] md:top-[8rem] *:text-[6.875rem] *:leading-[90%] *:font-bold *:opacity-5 z-[-1]"
      >
        <p>PIXEL {currentYear} PIXEL</p>
        <p>{currentYear} PIXEL {currentYear}</p>
      </div>
      <article class="w-full">
        {
          !!pageDesc && (
            <>
              <Breadcrumb {breadcrumbs} />
              <h1 class="max-md:text-center">{title}</h1>
              <p class="sm:max-w-[400px] lg:max-w-[600px] mt-8 md:mt-10 md:text-tablet-20 lg:text-desktop-24 !font-normal max-md:text-center max-md:mx-auto">
                {pageDesc}
              </p>
              <div
                class={`flex justify-center md:justify-between ${hasMenu && "mt-12 md:mt-14"}`}
              >
                <hr
                  class={`max-md:hidden md:w-[240px] lg:w-[400px] place-self-end ${!hasMenu && "mt-12 md:mt-14"}`}
                />
                <slot name="menu" />
              </div>
            </>
          )
        }
        <div class="my-16 md:my-20 lg:my-24">
          <slot />
        </div>
      </article>
    </main>
    <Footer />
    <script
      is:inline
      src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"
    ></script>
    <!-- AOS cdn js -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <!-- AOS script cdn js -->
    <script is:inline src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script is:inline>
      AOS.init();
    </script>
  </body>
</html>