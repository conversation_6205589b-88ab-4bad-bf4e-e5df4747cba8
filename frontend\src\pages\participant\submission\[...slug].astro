---
import DashboardLayout from "@layouts/DashboardLayout.astro";
import ProjectSubmissionForm from "@components/participant/Submission/ProjectSubmissionForm";
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";

const { slug } = Astro.params;
const modes = ["view", "edit", "create"];
const pageMode = modes.find((mode) => mode === slug?.split("/")[0]);
const project_id = slug?.split("/")[1];

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

if (!pageMode) return Astro.redirect("/404");
---

<DashboardLayout
  title={`${pageMode === "create" ? "Create " : pageMode === "edit" ? "Edit " : ""} Submission`}
>
  {
    pageMode === "view" ? (
      <div class="w-full mx-auto bg-white rounded-md flex flex-col  justify-center">
        <ProjectSubmissionView client:only="react" project_id={project_id} />
      </div>
    ) : (
      <>
        <ProjectSubmissionForm
          client:only="react"
          pageMode={pageMode}
          project_id={project_id}
        />
        <div class="text-xs md:text-mobile-14 bg-black/90 text-white opacity-90 text-center mt-4 py-6">
          © {currentYear} PIXEL & School of Computer Sciences, Universiti Sains
          Malaysia
          <br /> Disclaimer: All contents, intellectual properties & copyright
          reserved to Universiti Sains Malaysia (USM)
        </div>
      </>
    )
  }
</DashboardLayout>

<style is:inline>
  /* media queries */
  @media (max-width: 768px) {
    .labelNumber::before {
      margin-left: -1.5rem;
      font-size: 1.2rem;
      width: 30px;
    }
  }

  .labelNumber::before {
    content: attr(data-label);
    margin-left: -2rem;
    width: 30px;
    display: flex;
    justify-content: center;
    opacity: 0.2;
    font-weight: bold;
    font-size: 1.5rem;
    position: absolute;
  }
</style>
