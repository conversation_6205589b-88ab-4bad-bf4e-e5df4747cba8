/**
 * Clears all rows in the given sheet except the header row (row 1).
 *
 * @param {string} sheetName - The name of the sheet to clear.
 * "fyp_students" | "evaluations"
 * 
 */
function clearSheetDataOnly(sheetName) {
  const sheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName(sheetName);
  if (!sheet) {
    Logger.log(`❌ Sheet "${sheetName}" not found.`);
    return;
  }

  const lastRow = sheet.getLastRow();
  const maxCols = sheet.getMaxColumns();

  // Clear from row 2 to last row (but keep header row)
  if (lastRow > 1) {
    sheet.getRange(2, 1, lastRow - 1, maxCols).clearContent();
    Logger.log(`✅ Cleared data in "${sheetName}" except for header.`);
  } else {
    Logger.log(`ℹ️ Sheet "${sheetName}" has no data beyond the header.`);
  }
}