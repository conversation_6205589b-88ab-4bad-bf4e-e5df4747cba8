import React, { useContext, useState, useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { careerContext } from "./Context";
import { Job } from "@/modules";
import BaseModal from "@components/general/BaseModal";

const CareerFilter = forwardRef((props, ref) => {
  const { sort, updateSort, jobs, updateFilters } = useContext(careerContext);
  const [uniqueValues, setUniqueValues] = useState({
    modes: new Set<string>(),
    types: new Set<string>(),
    locations: new Set<string>(),
  });
  const modalRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    show: () => modalRef.current?.show(),
    hide: () => modalRef.current?.hide(),
  }));

  // Extract unique values from jobs
  useEffect(() => {
    const modes = new Set<string>();
    const types = new Set<string>();
    const locations = new Set<string>();

    jobs.forEach((job) => {
      if (job.mode) modes.add(job.mode);
      if (job.type) types.add(job.type);
      if (job.location) locations.add(job.location);
    });

    setUniqueValues({
      modes,
      types,
      locations,
    });
  }, [jobs]);

  const handleFilterChange = (filterType: string, value: string) => {
    updateFilters({
      mode: filterType === "mode" ? value : "",
      type: filterType === "type" ? value : "",
      location: filterType === "location" ? value : "",
    });
  };

  const handleReset = () => {
    updateFilters({ mode: "", type: "", location: "" });
    updateSort("default");
    modalRef.current?.hide();
  };

  return (
    <BaseModal
      ref={modalRef}
      title="Filter & Sort"
      footer={
        <div className="flex justify-end gap-4">
          <button
            onClick={() => modalRef.current?.hide()}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Close
          </button>
          <button
            onClick={handleReset}
            className="px-4 py-2 text-primary-6 hover:text-primary-7 transition-colors"
          >
            Reset Filters
          </button>
        </div>
      }
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
        {/* Left Column */}
        <div className="space-y-6">
          {/* Work Mode Filter */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <i className="fa-solid fa-briefcase text-primary-6"></i>
              Work Mode
            </label>
            <select
              className="bg-primary-1 border-none rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-5 text-grey-5"
              onChange={(e) => handleFilterChange("mode", e.target.value)}
            >
              <option value="">All Modes</option>
              {Array.from(uniqueValues.modes).map((mode) => (
                <option key={mode} value={mode}>
                  {mode}
                </option>
              ))}
            </select>
          </div>

          {/* Work Type Filter */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <i className="fa-solid fa-clock text-primary-6"></i>
              Work Type
            </label>
            <select
              className="bg-primary-1 border-none rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-5 text-grey-5"
              onChange={(e) => handleFilterChange("type", e.target.value)}
            >
              <option value="">All Types</option>
              {Array.from(uniqueValues.types).map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-6">
          {/* Location Filter */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <i className="fa-solid fa-location-dot text-primary-6"></i>
              Location
            </label>
            <select
              className="bg-primary-1 border-none rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-5 text-grey-5"
              onChange={(e) => handleFilterChange("location", e.target.value)}
            >
              <option value="">All Locations</option>
              {Array.from(uniqueValues.locations).map((location) => (
                <option key={location} value={location}>
                  {location}
                </option>
              ))}
            </select>
          </div>

          {/* Sort Options */}
          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <i className="fa-solid fa-sort text-primary-6"></i>
              Sort By
            </label>
            <select
              className="bg-primary-1 border-none rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-5 text-grey-5"
              value={sort}
              onChange={(e) => updateSort(e.target.value)}
            >
              <option value="default">Default</option>
              <option value="name">Job Title</option>
              <option value="company_name">Company Name</option>
            </select>
          </div>
        </div>
      </div>
    </BaseModal>
  );
});

CareerFilter.displayName = "CareerFilter";

export default CareerFilter;
