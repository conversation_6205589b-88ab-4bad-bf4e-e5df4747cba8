{"version": 3, "sources": ["../bundle-nXxcai/checked-fetch.js", "../../../node_modules/hono/dist/utils/html.js", "../../../node_modules/hono/dist/context.js", "../../../node_modules/hono/dist/compose.js", "../../../node_modules/hono/dist/http-exception.js", "../../../node_modules/hono/dist/utils/body.js", "../../../node_modules/hono/dist/utils/url.js", "../../../node_modules/hono/dist/request.js", "../../../node_modules/hono/dist/router.js", "../../../node_modules/hono/dist/hono-base.js", "../../../node_modules/hono/dist/router/reg-exp-router/node.js", "../../../node_modules/hono/dist/router/reg-exp-router/trie.js", "../../../node_modules/hono/dist/router/reg-exp-router/router.js", "../../../node_modules/hono/dist/router/smart-router/router.js", "../../../node_modules/hono/dist/router/trie-router/node.js", "../../../node_modules/hono/dist/router/trie-router/router.js", "../../../node_modules/hono/dist/hono.js", "../../../node_modules/hono/dist/middleware/cors/index.js", "../../../src/utils/config.ts", "../../../src/utils/crypto.ts", "../../../src/utils/Api.ts", "../../../src/routes/Router.ts", "../../../src/routes/Middleware.ts", "../../../node_modules/hono/dist/middleware/cache/index.js", "../../../node_modules/hono/dist/utils/color.js", "../../../node_modules/hono/dist/middleware/logger/index.js", "../../../src/index.ts", "../../../node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts", "../../../node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts", "../bundle-nXxcai/middleware-insertion-facade.js", "../../../node_modules/wrangler/templates/middleware/common.ts", "../bundle-nXxcai/middleware-loader.entry.ts"], "sourceRoot": "D:\\PIXEL\\pixel2024\\backend\\.wrangler\\tmp\\dev-mZacZf", "sourcesContent": ["const urls = new Set();\n\nfunction checkURL(request, init) {\n\tconst url =\n\t\trequest instanceof URL\n\t\t\t? request\n\t\t\t: new URL(\n\t\t\t\t\t(typeof request === \"string\"\n\t\t\t\t\t\t? new Request(request, init)\n\t\t\t\t\t\t: request\n\t\t\t\t\t).url\n\t\t\t\t);\n\tif (url.port && url.port !== \"443\" && url.protocol === \"https:\") {\n\t\tif (!urls.has(url.toString())) {\n\t\t\turls.add(url.toString());\n\t\t\tconsole.warn(\n\t\t\t\t`WARNING: known issue with \\`fetch()\\` requests to custom HTTPS ports in published Workers:\\n` +\n\t\t\t\t\t` - ${url.toString()} - the custom port will be ignored when the Worker is published using the \\`wrangler deploy\\` command.\\n`\n\t\t\t);\n\t\t}\n\t}\n}\n\nglobalThis.fetch = new Proxy(globalThis.fetch, {\n\tapply(target, thisArg, argArray) {\n\t\tconst [request, init] = argArray;\n\t\tcheckURL(request, init);\n\t\treturn Reflect.apply(target, thisArg, argArray);\n\t},\n});\n", "// src/utils/html.ts\nvar HtmlEscapedCallbackPhase = {\n  Stringify: 1,\n  BeforeStream: 2,\n  Stream: 3\n};\nvar raw = (value, callbacks) => {\n  const escapedString = new String(value);\n  escapedString.isEscaped = true;\n  escapedString.callbacks = callbacks;\n  return escapedString;\n};\nvar escapeRe = /[&<>'\"]/;\nvar stringBufferToString = async (buffer) => {\n  let str = \"\";\n  const callbacks = [];\n  for (let i = buffer.length - 1; ; i--) {\n    str += buffer[i];\n    i--;\n    if (i < 0) {\n      break;\n    }\n    let r = await buffer[i];\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    const isEscaped = r.isEscaped;\n    r = await (typeof r === \"object\" ? r.toString() : r);\n    if (typeof r === \"object\") {\n      callbacks.push(...r.callbacks || []);\n    }\n    if (r.isEscaped ?? isEscaped) {\n      str += r;\n    } else {\n      const buf = [str];\n      escapeToBuffer(r, buf);\n      str = buf[0];\n    }\n  }\n  return raw(str, callbacks);\n};\nvar escapeToBuffer = (str, buffer) => {\n  const match = str.search(escapeRe);\n  if (match === -1) {\n    buffer[0] += str;\n    return;\n  }\n  let escape;\n  let index;\n  let lastIndex = 0;\n  for (index = match; index < str.length; index++) {\n    switch (str.charCodeAt(index)) {\n      case 34:\n        escape = \"&quot;\";\n        break;\n      case 39:\n        escape = \"&#39;\";\n        break;\n      case 38:\n        escape = \"&amp;\";\n        break;\n      case 60:\n        escape = \"&lt;\";\n        break;\n      case 62:\n        escape = \"&gt;\";\n        break;\n      default:\n        continue;\n    }\n    buffer[0] += str.substring(lastIndex, index) + escape;\n    lastIndex = index + 1;\n  }\n  buffer[0] += str.substring(lastIndex, index);\n};\nvar resolveCallback = async (str, phase, preserveCallbacks, context, buffer) => {\n  const callbacks = str.callbacks;\n  if (!callbacks?.length) {\n    return Promise.resolve(str);\n  }\n  if (buffer) {\n    buffer[0] += str;\n  } else {\n    buffer = [str];\n  }\n  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(\n    (res) => Promise.all(\n      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))\n    ).then(() => buffer[0])\n  );\n  if (preserveCallbacks) {\n    return raw(await resStr, callbacks);\n  } else {\n    return resStr;\n  }\n};\nexport {\n  HtmlEscapedCallbackPhase,\n  escapeToBuffer,\n  raw,\n  resolveCallback,\n  stringBufferToString\n};\n", "// src/context.ts\nimport { HtmlEscapedCallbackPhase, resolveCallback } from \"./utils/html.js\";\nvar TEXT_PLAIN = \"text/plain; charset=UTF-8\";\nvar setHeaders = (headers, map = {}) => {\n  Object.entries(map).forEach(([key, value]) => headers.set(key, value));\n  return headers;\n};\nvar Context = class {\n  req;\n  env = {};\n  _var = {};\n  finalized = false;\n  error = void 0;\n  #status = 200;\n  #executionCtx;\n  #headers = void 0;\n  #preparedHeaders = void 0;\n  #res;\n  #isFresh = true;\n  layout = void 0;\n  renderer = (content) => this.html(content);\n  notFoundHandler = () => new Response();\n  constructor(req, options) {\n    this.req = req;\n    if (options) {\n      this.#executionCtx = options.executionCtx;\n      this.env = options.env;\n      if (options.notFoundHandler) {\n        this.notFoundHandler = options.notFoundHandler;\n      }\n    }\n  }\n  get event() {\n    if (this.#executionCtx && \"respondWith\" in this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no FetchEvent\");\n    }\n  }\n  get executionCtx() {\n    if (this.#executionCtx) {\n      return this.#executionCtx;\n    } else {\n      throw Error(\"This context has no ExecutionContext\");\n    }\n  }\n  get res() {\n    this.#isFresh = false;\n    return this.#res ||= new Response(\"404 Not Found\", { status: 404 });\n  }\n  set res(_res) {\n    this.#isFresh = false;\n    if (this.#res && _res) {\n      this.#res.headers.delete(\"content-type\");\n      for (const [k, v] of this.#res.headers.entries()) {\n        if (k === \"set-cookie\") {\n          const cookies = this.#res.headers.getSetCookie();\n          _res.headers.delete(\"set-cookie\");\n          for (const cookie of cookies) {\n            _res.headers.append(\"set-cookie\", cookie);\n          }\n        } else {\n          _res.headers.set(k, v);\n        }\n      }\n    }\n    this.#res = _res;\n    this.finalized = true;\n  }\n  render = (...args) => this.renderer(...args);\n  setLayout = (layout) => this.layout = layout;\n  getLayout = () => this.layout;\n  setRenderer = (renderer) => {\n    this.renderer = renderer;\n  };\n  header = (name, value, options) => {\n    if (value === void 0) {\n      if (this.#headers) {\n        this.#headers.delete(name);\n      } else if (this.#preparedHeaders) {\n        delete this.#preparedHeaders[name.toLocaleLowerCase()];\n      }\n      if (this.finalized) {\n        this.res.headers.delete(name);\n      }\n      return;\n    }\n    if (options?.append) {\n      if (!this.#headers) {\n        this.#isFresh = false;\n        this.#headers = new Headers(this.#preparedHeaders);\n        this.#preparedHeaders = {};\n      }\n      this.#headers.append(name, value);\n    } else {\n      if (this.#headers) {\n        this.#headers.set(name, value);\n      } else {\n        this.#preparedHeaders ??= {};\n        this.#preparedHeaders[name.toLowerCase()] = value;\n      }\n    }\n    if (this.finalized) {\n      if (options?.append) {\n        this.res.headers.append(name, value);\n      } else {\n        this.res.headers.set(name, value);\n      }\n    }\n  };\n  status = (status) => {\n    this.#isFresh = false;\n    this.#status = status;\n  };\n  set = (key, value) => {\n    this._var ??= {};\n    this._var[key] = value;\n  };\n  get = (key) => {\n    return this._var ? this._var[key] : void 0;\n  };\n  get var() {\n    return { ...this._var };\n  }\n  newResponse = (data, arg, headers) => {\n    if (this.#isFresh && !headers && !arg && this.#status === 200) {\n      return new Response(data, {\n        headers: this.#preparedHeaders\n      });\n    }\n    if (arg && typeof arg !== \"number\") {\n      const header = new Headers(arg.headers);\n      if (this.#headers) {\n        this.#headers.forEach((v, k) => {\n          if (k === \"set-cookie\") {\n            header.append(k, v);\n          } else {\n            header.set(k, v);\n          }\n        });\n      }\n      const headers2 = setHeaders(header, this.#preparedHeaders);\n      return new Response(data, {\n        headers: headers2,\n        status: arg.status ?? this.#status\n      });\n    }\n    const status = typeof arg === \"number\" ? arg : this.#status;\n    this.#preparedHeaders ??= {};\n    this.#headers ??= new Headers();\n    setHeaders(this.#headers, this.#preparedHeaders);\n    if (this.#res) {\n      this.#res.headers.forEach((v, k) => {\n        if (k === \"set-cookie\") {\n          this.#headers?.append(k, v);\n        } else {\n          this.#headers?.set(k, v);\n        }\n      });\n      setHeaders(this.#headers, this.#preparedHeaders);\n    }\n    headers ??= {};\n    for (const [k, v] of Object.entries(headers)) {\n      if (typeof v === \"string\") {\n        this.#headers.set(k, v);\n      } else {\n        this.#headers.delete(k);\n        for (const v2 of v) {\n          this.#headers.append(k, v2);\n        }\n      }\n    }\n    return new Response(data, {\n      status,\n      headers: this.#headers\n    });\n  };\n  body = (data, arg, headers) => {\n    return typeof arg === \"number\" ? this.newResponse(data, arg, headers) : this.newResponse(data, arg);\n  };\n  text = (text, arg, headers) => {\n    if (!this.#preparedHeaders) {\n      if (this.#isFresh && !headers && !arg) {\n        return new Response(text);\n      }\n      this.#preparedHeaders = {};\n    }\n    this.#preparedHeaders[\"content-type\"] = TEXT_PLAIN;\n    return typeof arg === \"number\" ? this.newResponse(text, arg, headers) : this.newResponse(text, arg);\n  };\n  json = (object, arg, headers) => {\n    const body = JSON.stringify(object);\n    this.#preparedHeaders ??= {};\n    this.#preparedHeaders[\"content-type\"] = \"application/json; charset=UTF-8\";\n    return typeof arg === \"number\" ? this.newResponse(body, arg, headers) : this.newResponse(body, arg);\n  };\n  html = (html, arg, headers) => {\n    this.#preparedHeaders ??= {};\n    this.#preparedHeaders[\"content-type\"] = \"text/html; charset=UTF-8\";\n    if (typeof html === \"object\") {\n      if (!(html instanceof Promise)) {\n        html = html.toString();\n      }\n      if (html instanceof Promise) {\n        return html.then((html2) => resolveCallback(html2, HtmlEscapedCallbackPhase.Stringify, false, {})).then((html2) => {\n          return typeof arg === \"number\" ? this.newResponse(html2, arg, headers) : this.newResponse(html2, arg);\n        });\n      }\n    }\n    return typeof arg === \"number\" ? this.newResponse(html, arg, headers) : this.newResponse(html, arg);\n  };\n  redirect = (location, status = 302) => {\n    this.#headers ??= new Headers();\n    this.#headers.set(\"Location\", location);\n    return this.newResponse(null, status);\n  };\n  notFound = () => {\n    return this.notFoundHandler(this);\n  };\n};\nexport {\n  Context,\n  TEXT_PLAIN\n};\n", "// src/compose.ts\nimport { Context } from \"./context.js\";\nvar compose = (middleware, onError, onNotFound) => {\n  return (context, next) => {\n    let index = -1;\n    return dispatch(0);\n    async function dispatch(i) {\n      if (i <= index) {\n        throw new Error(\"next() called multiple times\");\n      }\n      index = i;\n      let res;\n      let isError = false;\n      let handler;\n      if (middleware[i]) {\n        handler = middleware[i][0][0];\n        if (context instanceof Context) {\n          context.req.routeIndex = i;\n        }\n      } else {\n        handler = i === middleware.length && next || void 0;\n      }\n      if (!handler) {\n        if (context instanceof Context && context.finalized === false && onNotFound) {\n          res = await onNotFound(context);\n        }\n      } else {\n        try {\n          res = await handler(context, () => {\n            return dispatch(i + 1);\n          });\n        } catch (err) {\n          if (err instanceof Error && context instanceof Context && onError) {\n            context.error = err;\n            res = await onError(err, context);\n            isError = true;\n          } else {\n            throw err;\n          }\n        }\n      }\n      if (res && (context.finalized === false || isError)) {\n        context.res = res;\n      }\n      return context;\n    }\n  };\n};\nexport {\n  compose\n};\n", "// src/http-exception.ts\nvar HTTPException = class extends Error {\n  res;\n  status;\n  constructor(status = 500, options) {\n    super(options?.message, { cause: options?.cause });\n    this.res = options?.res;\n    this.status = status;\n  }\n  getResponse() {\n    if (this.res) {\n      return this.res;\n    }\n    return new Response(this.message, {\n      status: this.status\n    });\n  }\n};\nexport {\n  HTTPException\n};\n", "// src/utils/body.ts\nimport { HonoRequest } from \"../request.js\";\nvar parseBody = async (request, options = { all: false }) => {\n  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;\n  const contentType = headers.get(\"Content-Type\");\n  if (contentType !== null && contentType.startsWith(\"multipart/form-data\") || contentType !== null && contentType.startsWith(\"application/x-www-form-urlencoded\")) {\n    return parseFormData(request, options);\n  }\n  return {};\n};\nasync function parseFormData(request, options) {\n  const formData = await request.formData();\n  if (formData) {\n    return convertFormDataToBodyData(formData, options);\n  }\n  return {};\n}\nfunction convertFormDataToBodyData(formData, options) {\n  const form = {};\n  formData.forEach((value, key) => {\n    const shouldParseAllValues = options.all || key.endsWith(\"[]\");\n    if (!shouldParseAllValues) {\n      form[key] = value;\n    } else {\n      handleParsingAllValues(form, key, value);\n    }\n  });\n  return form;\n}\nvar handleParsingAllValues = (form, key, value) => {\n  const formKey = form[key];\n  if (formKey && Array.isArray(formKey)) {\n    ;\n    form[key].push(value);\n  } else if (formKey) {\n    form[key] = [formKey, value];\n  } else {\n    form[key] = value;\n  }\n};\nexport {\n  parseBody\n};\n", "// src/utils/url.ts\nvar splitPath = (path) => {\n  const paths = path.split(\"/\");\n  if (paths[0] === \"\") {\n    paths.shift();\n  }\n  return paths;\n};\nvar splitRoutingPath = (routePath) => {\n  const { groups, path } = extractGroupsFromPath(routePath);\n  const paths = splitPath(path);\n  return replaceGroupMarks(paths, groups);\n};\nvar extractGroupsFromPath = (path) => {\n  const groups = [];\n  path = path.replace(/\\{[^}]+\\}/g, (match, index) => {\n    const mark = `@${index}`;\n    groups.push([mark, match]);\n    return mark;\n  });\n  return { groups, path };\n};\nvar replaceGroupMarks = (paths, groups) => {\n  for (let i = groups.length - 1; i >= 0; i--) {\n    const [mark] = groups[i];\n    for (let j = paths.length - 1; j >= 0; j--) {\n      if (paths[j].includes(mark)) {\n        paths[j] = paths[j].replace(mark, groups[i][1]);\n        break;\n      }\n    }\n  }\n  return paths;\n};\nvar patternCache = {};\nvar getPattern = (label) => {\n  if (label === \"*\") {\n    return \"*\";\n  }\n  const match = label.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n  if (match) {\n    if (!patternCache[label]) {\n      if (match[2]) {\n        patternCache[label] = [label, match[1], new RegExp(\"^\" + match[2] + \"$\")];\n      } else {\n        patternCache[label] = [label, match[1], true];\n      }\n    }\n    return patternCache[label];\n  }\n  return null;\n};\nvar getPath = (request) => {\n  const url = request.url;\n  const queryIndex = url.indexOf(\"?\", 8);\n  return url.slice(url.indexOf(\"/\", 8), queryIndex === -1 ? void 0 : queryIndex);\n};\nvar getQueryStrings = (url) => {\n  const queryIndex = url.indexOf(\"?\", 8);\n  return queryIndex === -1 ? \"\" : \"?\" + url.slice(queryIndex + 1);\n};\nvar getPathNoStrict = (request) => {\n  const result = getPath(request);\n  return result.length > 1 && result[result.length - 1] === \"/\" ? result.slice(0, -1) : result;\n};\nvar mergePath = (...paths) => {\n  let p = \"\";\n  let endsWithSlash = false;\n  for (let path of paths) {\n    if (p[p.length - 1] === \"/\") {\n      p = p.slice(0, -1);\n      endsWithSlash = true;\n    }\n    if (path[0] !== \"/\") {\n      path = `/${path}`;\n    }\n    if (path === \"/\" && endsWithSlash) {\n      p = `${p}/`;\n    } else if (path !== \"/\") {\n      p = `${p}${path}`;\n    }\n    if (path === \"/\" && p === \"\") {\n      p = \"/\";\n    }\n  }\n  return p;\n};\nvar checkOptionalParameter = (path) => {\n  if (!path.match(/\\:.+\\?$/)) {\n    return null;\n  }\n  const segments = path.split(\"/\");\n  const results = [];\n  let basePath = \"\";\n  segments.forEach((segment) => {\n    if (segment !== \"\" && !/\\:/.test(segment)) {\n      basePath += \"/\" + segment;\n    } else if (/\\:/.test(segment)) {\n      if (/\\?/.test(segment)) {\n        if (results.length === 0 && basePath === \"\") {\n          results.push(\"/\");\n        } else {\n          results.push(basePath);\n        }\n        const optionalSegment = segment.replace(\"?\", \"\");\n        basePath += \"/\" + optionalSegment;\n        results.push(basePath);\n      } else {\n        basePath += \"/\" + segment;\n      }\n    }\n  });\n  return results.filter((v, i, a) => a.indexOf(v) === i);\n};\nvar _decodeURI = (value) => {\n  if (!/[%+]/.test(value)) {\n    return value;\n  }\n  if (value.indexOf(\"+\") !== -1) {\n    value = value.replace(/\\+/g, \" \");\n  }\n  return /%/.test(value) ? decodeURIComponent_(value) : value;\n};\nvar _getQueryParam = (url, key, multiple) => {\n  let encoded;\n  if (!multiple && key && !/[%+]/.test(key)) {\n    let keyIndex2 = url.indexOf(`?${key}`, 8);\n    if (keyIndex2 === -1) {\n      keyIndex2 = url.indexOf(`&${key}`, 8);\n    }\n    while (keyIndex2 !== -1) {\n      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);\n      if (trailingKeyCode === 61) {\n        const valueIndex = keyIndex2 + key.length + 2;\n        const endIndex = url.indexOf(\"&\", valueIndex);\n        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));\n      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {\n        return \"\";\n      }\n      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);\n    }\n    encoded = /[%+]/.test(url);\n    if (!encoded) {\n      return void 0;\n    }\n  }\n  const results = {};\n  encoded ??= /[%+]/.test(url);\n  let keyIndex = url.indexOf(\"?\", 8);\n  while (keyIndex !== -1) {\n    const nextKeyIndex = url.indexOf(\"&\", keyIndex + 1);\n    let valueIndex = url.indexOf(\"=\", keyIndex);\n    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {\n      valueIndex = -1;\n    }\n    let name = url.slice(\n      keyIndex + 1,\n      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex\n    );\n    if (encoded) {\n      name = _decodeURI(name);\n    }\n    keyIndex = nextKeyIndex;\n    if (name === \"\") {\n      continue;\n    }\n    let value;\n    if (valueIndex === -1) {\n      value = \"\";\n    } else {\n      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);\n      if (encoded) {\n        value = _decodeURI(value);\n      }\n    }\n    if (multiple) {\n      if (!(results[name] && Array.isArray(results[name]))) {\n        results[name] = [];\n      }\n      ;\n      results[name].push(value);\n    } else {\n      results[name] ??= value;\n    }\n  }\n  return key ? results[key] : results;\n};\nvar getQueryParam = _getQueryParam;\nvar getQueryParams = (url, key) => {\n  return _getQueryParam(url, key, true);\n};\nvar decodeURIComponent_ = decodeURIComponent;\nexport {\n  checkOptionalParameter,\n  decodeURIComponent_,\n  getPath,\n  getPathNoStrict,\n  getPattern,\n  getQueryParam,\n  getQueryParams,\n  getQueryStrings,\n  mergePath,\n  splitPath,\n  splitRoutingPath\n};\n", "// src/request.ts\nimport { parseBody } from \"./utils/body.js\";\nimport { getQueryParam, getQueryParams, decodeURIComponent_ } from \"./utils/url.js\";\nvar HonoRequest = class {\n  raw;\n  #validatedData;\n  #matchResult;\n  routeIndex = 0;\n  path;\n  bodyCache = {};\n  constructor(request, path = \"/\", matchResult = [[]]) {\n    this.raw = request;\n    this.path = path;\n    this.#matchResult = matchResult;\n    this.#validatedData = {};\n  }\n  param(key) {\n    return key ? this.getDecodedParam(key) : this.getAllDecodedParams();\n  }\n  getDecodedParam(key) {\n    const paramKey = this.#matchResult[0][this.routeIndex][1][key];\n    const param = this.getParamValue(paramKey);\n    return param ? /\\%/.test(param) ? decodeURIComponent_(param) : param : void 0;\n  }\n  getAllDecodedParams() {\n    const decoded = {};\n    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);\n    for (const key of keys) {\n      const value = this.getParamValue(this.#matchResult[0][this.routeIndex][1][key]);\n      if (value && typeof value === \"string\") {\n        decoded[key] = /\\%/.test(value) ? decodeURIComponent_(value) : value;\n      }\n    }\n    return decoded;\n  }\n  getParamValue(paramKey) {\n    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;\n  }\n  query(key) {\n    return getQueryParam(this.url, key);\n  }\n  queries(key) {\n    return getQueryParams(this.url, key);\n  }\n  header(name) {\n    if (name) {\n      return this.raw.headers.get(name.toLowerCase()) ?? void 0;\n    }\n    const headerData = {};\n    this.raw.headers.forEach((value, key) => {\n      headerData[key] = value;\n    });\n    return headerData;\n  }\n  async parseBody(options) {\n    if (this.bodyCache.parsedBody) {\n      return this.bodyCache.parsedBody;\n    }\n    const parsedBody = await parseBody(this, options);\n    this.bodyCache.parsedBody = parsedBody;\n    return parsedBody;\n  }\n  cachedBody = (key) => {\n    const { bodyCache, raw } = this;\n    const cachedBody = bodyCache[key];\n    if (cachedBody) {\n      return cachedBody;\n    }\n    if (!bodyCache[key]) {\n      for (const keyOfBodyCache of Object.keys(bodyCache)) {\n        if (keyOfBodyCache === \"parsedBody\") {\n          continue;\n        }\n        return (async () => {\n          let body = await bodyCache[keyOfBodyCache];\n          if (keyOfBodyCache === \"json\") {\n            body = JSON.stringify(body);\n          }\n          return await new Response(body)[key]();\n        })();\n      }\n    }\n    return bodyCache[key] = raw[key]();\n  };\n  json() {\n    return this.cachedBody(\"json\");\n  }\n  text() {\n    return this.cachedBody(\"text\");\n  }\n  arrayBuffer() {\n    return this.cachedBody(\"arrayBuffer\");\n  }\n  blob() {\n    return this.cachedBody(\"blob\");\n  }\n  formData() {\n    return this.cachedBody(\"formData\");\n  }\n  addValidatedData(target, data) {\n    this.#validatedData[target] = data;\n  }\n  valid(target) {\n    return this.#validatedData[target];\n  }\n  get url() {\n    return this.raw.url;\n  }\n  get method() {\n    return this.raw.method;\n  }\n  get matchedRoutes() {\n    return this.#matchResult[0].map(([[, route]]) => route);\n  }\n  get routePath() {\n    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;\n  }\n};\nexport {\n  HonoRequest\n};\n", "// src/router.ts\nvar METHOD_NAME_ALL = \"ALL\";\nvar METHOD_NAME_ALL_LOWERCASE = \"all\";\nvar METHODS = [\"get\", \"post\", \"put\", \"delete\", \"options\", \"patch\"];\nvar MESSAGE_MATCHER_IS_ALREADY_BUILT = \"Can not add a route since the matcher is already built.\";\nvar UnsupportedPathError = class extends Error {\n};\nexport {\n  MESSAGE_MATCHER_IS_ALREADY_BUILT,\n  METHODS,\n  METHOD_NAME_ALL,\n  METHOD_NAME_ALL_LOWERCASE,\n  UnsupportedPathError\n};\n", "// src/hono-base.ts\nimport { compose } from \"./compose.js\";\nimport { Context } from \"./context.js\";\nimport { HTTPException } from \"./http-exception.js\";\nimport { HonoRequest } from \"./request.js\";\nimport { METHOD_NAME_ALL, METHOD_NAME_ALL_LOWERCASE, METHODS } from \"./router.js\";\nimport { getPath, getPathNoStrict, getQueryStrings, mergePath } from \"./utils/url.js\";\nvar COMPOSED_HANDLER = Symbol(\"composedHandler\");\nfunction defineDynamicClass() {\n  return class {\n  };\n}\nvar notFoundHandler = (c) => {\n  return c.text(\"404 Not Found\", 404);\n};\nvar errorHandler = (err, c) => {\n  if (err instanceof HTTPException) {\n    return err.getResponse();\n  }\n  console.error(err);\n  return c.text(\"Internal Server Error\", 500);\n};\nvar Hono = class extends defineDynamicClass() {\n  router;\n  getPath;\n  _basePath = \"/\";\n  #path = \"/\";\n  routes = [];\n  constructor(options = {}) {\n    super();\n    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];\n    allMethods.forEach((method) => {\n      this[method] = (args1, ...args) => {\n        if (typeof args1 === \"string\") {\n          this.#path = args1;\n        } else {\n          this.addRoute(method, this.#path, args1);\n        }\n        args.forEach((handler) => {\n          if (typeof handler !== \"string\") {\n            this.addRoute(method, this.#path, handler);\n          }\n        });\n        return this;\n      };\n    });\n    this.on = (method, path, ...handlers) => {\n      if (!method) {\n        return this;\n      }\n      for (const p of [path].flat()) {\n        this.#path = p;\n        for (const m of [method].flat()) {\n          handlers.map((handler) => {\n            this.addRoute(m.toUpperCase(), this.#path, handler);\n          });\n        }\n      }\n      return this;\n    };\n    this.use = (arg1, ...handlers) => {\n      if (typeof arg1 === \"string\") {\n        this.#path = arg1;\n      } else {\n        this.#path = \"*\";\n        handlers.unshift(arg1);\n      }\n      handlers.forEach((handler) => {\n        this.addRoute(METHOD_NAME_ALL, this.#path, handler);\n      });\n      return this;\n    };\n    const strict = options.strict ?? true;\n    delete options.strict;\n    Object.assign(this, options);\n    this.getPath = strict ? options.getPath ?? getPath : getPathNoStrict;\n  }\n  clone() {\n    const clone = new Hono({\n      router: this.router,\n      getPath: this.getPath\n    });\n    clone.routes = this.routes;\n    return clone;\n  }\n  notFoundHandler = notFoundHandler;\n  errorHandler = errorHandler;\n  route(path, app) {\n    const subApp = this.basePath(path);\n    if (!app) {\n      return subApp;\n    }\n    app.routes.map((r) => {\n      let handler;\n      if (app.errorHandler === errorHandler) {\n        handler = r.handler;\n      } else {\n        handler = async (c, next) => (await compose([], app.errorHandler)(c, () => r.handler(c, next))).res;\n        handler[COMPOSED_HANDLER] = r.handler;\n      }\n      subApp.addRoute(r.method, r.path, handler);\n    });\n    return this;\n  }\n  basePath(path) {\n    const subApp = this.clone();\n    subApp._basePath = mergePath(this._basePath, path);\n    return subApp;\n  }\n  onError = (handler) => {\n    this.errorHandler = handler;\n    return this;\n  };\n  notFound = (handler) => {\n    this.notFoundHandler = handler;\n    return this;\n  };\n  mount(path, applicationHandler, optionHandler) {\n    const mergedPath = mergePath(this._basePath, path);\n    const pathPrefixLength = mergedPath === \"/\" ? 0 : mergedPath.length;\n    const handler = async (c, next) => {\n      let executionContext = void 0;\n      try {\n        executionContext = c.executionCtx;\n      } catch {\n      }\n      const options = optionHandler ? optionHandler(c) : [c.env, executionContext];\n      const optionsArray = Array.isArray(options) ? options : [options];\n      const queryStrings = getQueryStrings(c.req.url);\n      const res = await applicationHandler(\n        new Request(\n          new URL((c.req.path.slice(pathPrefixLength) || \"/\") + queryStrings, c.req.url),\n          c.req.raw\n        ),\n        ...optionsArray\n      );\n      if (res) {\n        return res;\n      }\n      await next();\n    };\n    this.addRoute(METHOD_NAME_ALL, mergePath(path, \"*\"), handler);\n    return this;\n  }\n  addRoute(method, path, handler) {\n    method = method.toUpperCase();\n    path = mergePath(this._basePath, path);\n    const r = { path, method, handler };\n    this.router.add(method, path, [handler, r]);\n    this.routes.push(r);\n  }\n  matchRoute(method, path) {\n    return this.router.match(method, path);\n  }\n  handleError(err, c) {\n    if (err instanceof Error) {\n      return this.errorHandler(err, c);\n    }\n    throw err;\n  }\n  dispatch(request, executionCtx, env, method) {\n    if (method === \"HEAD\") {\n      return (async () => new Response(null, await this.dispatch(request, executionCtx, env, \"GET\")))();\n    }\n    const path = this.getPath(request, { env });\n    const matchResult = this.matchRoute(method, path);\n    const c = new Context(new HonoRequest(request, path, matchResult), {\n      env,\n      executionCtx,\n      notFoundHandler: this.notFoundHandler\n    });\n    if (matchResult[0].length === 1) {\n      let res;\n      try {\n        res = matchResult[0][0][0][0](c, async () => {\n          c.res = await this.notFoundHandler(c);\n        });\n      } catch (err) {\n        return this.handleError(err, c);\n      }\n      return res instanceof Promise ? res.then(\n        (resolved) => resolved || (c.finalized ? c.res : this.notFoundHandler(c))\n      ).catch((err) => this.handleError(err, c)) : res;\n    }\n    const composed = compose(matchResult[0], this.errorHandler, this.notFoundHandler);\n    return (async () => {\n      try {\n        const context = await composed(c);\n        if (!context.finalized) {\n          throw new Error(\n            \"Context is not finalized. You may forget returning Response object or `await next()`\"\n          );\n        }\n        return context.res;\n      } catch (err) {\n        return this.handleError(err, c);\n      }\n    })();\n  }\n  fetch = (request, ...rest) => {\n    return this.dispatch(request, rest[1], rest[0], request.method);\n  };\n  request = (input, requestInit, Env, executionCtx) => {\n    if (input instanceof Request) {\n      if (requestInit !== void 0) {\n        input = new Request(input, requestInit);\n      }\n      return this.fetch(input, Env, executionCtx);\n    }\n    input = input.toString();\n    const path = /^https?:\\/\\//.test(input) ? input : `http://localhost${mergePath(\"/\", input)}`;\n    const req = new Request(path, requestInit);\n    return this.fetch(req, Env, executionCtx);\n  };\n  fire = () => {\n    addEventListener(\"fetch\", (event) => {\n      event.respondWith(this.dispatch(event.request, event, void 0, event.request.method));\n    });\n  };\n};\nexport {\n  COMPOSED_HANDLER,\n  Hono as HonoBase\n};\n", "// src/router/reg-exp-router/node.ts\nvar LABEL_REG_EXP_STR = \"[^/]+\";\nvar ONLY_WILDCARD_REG_EXP_STR = \".*\";\nvar TAIL_WILDCARD_REG_EXP_STR = \"(?:|/.*)\";\nvar PATH_ERROR = Symbol();\nvar regExpMetaChars = new Set(\".\\\\+*[^]$()\");\nfunction compareKey(a, b) {\n  if (a.length === 1) {\n    return b.length === 1 ? a < b ? -1 : 1 : -1;\n  }\n  if (b.length === 1) {\n    return 1;\n  }\n  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {\n    return 1;\n  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {\n    return -1;\n  }\n  if (a === LABEL_REG_EXP_STR) {\n    return 1;\n  } else if (b === LABEL_REG_EXP_STR) {\n    return -1;\n  }\n  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;\n}\nvar Node = class {\n  index;\n  varIndex;\n  children = /* @__PURE__ */ Object.create(null);\n  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {\n    if (tokens.length === 0) {\n      if (this.index !== void 0) {\n        throw PATH_ERROR;\n      }\n      if (pathErrorCheckOnly) {\n        return;\n      }\n      this.index = index;\n      return;\n    }\n    const [token, ...restTokens] = tokens;\n    const pattern = token === \"*\" ? restTokens.length === 0 ? [\"\", \"\", ONLY_WILDCARD_REG_EXP_STR] : [\"\", \"\", LABEL_REG_EXP_STR] : token === \"/*\" ? [\"\", \"\", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\\:([^\\{\\}]+)(?:\\{(.+)\\})?$/);\n    let node;\n    if (pattern) {\n      const name = pattern[1];\n      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;\n      if (name && pattern[2]) {\n        regexpStr = regexpStr.replace(/^\\((?!\\?:)(?=[^)]+\\)$)/, \"(?:\");\n        if (/\\((?!\\?:)/.test(regexpStr)) {\n          throw PATH_ERROR;\n        }\n      }\n      node = this.children[regexpStr];\n      if (!node) {\n        if (Object.keys(this.children).some(\n          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.children[regexpStr] = new Node();\n        if (name !== \"\") {\n          node.varIndex = context.varIndex++;\n        }\n      }\n      if (!pathErrorCheckOnly && name !== \"\") {\n        paramMap.push([name, node.varIndex]);\n      }\n    } else {\n      node = this.children[token];\n      if (!node) {\n        if (Object.keys(this.children).some(\n          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR\n        )) {\n          throw PATH_ERROR;\n        }\n        if (pathErrorCheckOnly) {\n          return;\n        }\n        node = this.children[token] = new Node();\n      }\n    }\n    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);\n  }\n  buildRegExpStr() {\n    const childKeys = Object.keys(this.children).sort(compareKey);\n    const strList = childKeys.map((k) => {\n      const c = this.children[k];\n      return (typeof c.varIndex === \"number\" ? `(${k})@${c.varIndex}` : regExpMetaChars.has(k) ? `\\\\${k}` : k) + c.buildRegExpStr();\n    });\n    if (typeof this.index === \"number\") {\n      strList.unshift(`#${this.index}`);\n    }\n    if (strList.length === 0) {\n      return \"\";\n    }\n    if (strList.length === 1) {\n      return strList[0];\n    }\n    return \"(?:\" + strList.join(\"|\") + \")\";\n  }\n};\nexport {\n  Node,\n  PATH_ERROR\n};\n", "// src/router/reg-exp-router/trie.ts\nimport { Node } from \"./node.js\";\nvar Trie = class {\n  context = { varIndex: 0 };\n  root = new Node();\n  insert(path, index, pathErrorCheckOnly) {\n    const paramAssoc = [];\n    const groups = [];\n    for (let i = 0; ; ) {\n      let replaced = false;\n      path = path.replace(/\\{[^}]+\\}/g, (m) => {\n        const mark = `@\\\\${i}`;\n        groups[i] = [mark, m];\n        i++;\n        replaced = true;\n        return mark;\n      });\n      if (!replaced) {\n        break;\n      }\n    }\n    const tokens = path.match(/(?::[^\\/]+)|(?:\\/\\*$)|./g) || [];\n    for (let i = groups.length - 1; i >= 0; i--) {\n      const [mark] = groups[i];\n      for (let j = tokens.length - 1; j >= 0; j--) {\n        if (tokens[j].indexOf(mark) !== -1) {\n          tokens[j] = tokens[j].replace(mark, groups[i][1]);\n          break;\n        }\n      }\n    }\n    this.root.insert(tokens, index, paramAssoc, this.context, pathErrorCheckOnly);\n    return paramAssoc;\n  }\n  buildRegExp() {\n    let regexp = this.root.buildRegExpStr();\n    if (regexp === \"\") {\n      return [/^$/, [], []];\n    }\n    let captureIndex = 0;\n    const indexReplacementMap = [];\n    const paramReplacementMap = [];\n    regexp = regexp.replace(/#(\\d+)|@(\\d+)|\\.\\*\\$/g, (_, handlerIndex, paramIndex) => {\n      if (typeof handlerIndex !== \"undefined\") {\n        indexReplacementMap[++captureIndex] = Number(handlerIndex);\n        return \"$()\";\n      }\n      if (typeof paramIndex !== \"undefined\") {\n        paramReplacementMap[Number(paramIndex)] = ++captureIndex;\n        return \"\";\n      }\n      return \"\";\n    });\n    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];\n  }\n};\nexport {\n  Trie\n};\n", "// src/router/reg-exp-router/router.ts\nimport {\n  METHOD_NAME_ALL,\n  UnsupportedPathError,\n  MESSAGE_MATCHER_IS_ALREADY_BUILT\n} from \"../../router.js\";\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { PATH_ERROR } from \"./node.js\";\nimport { Trie } from \"./trie.js\";\nvar emptyParam = [];\nvar nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];\nvar wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\nfunction buildWildcardRegExp(path) {\n  return wildcardRegExpCache[path] ??= new RegExp(\n    path === \"*\" ? \"\" : `^${path.replace(\n      /\\/\\*$|([.\\\\+*[^\\]$()])/g,\n      (_, metaChar) => metaChar ? `\\\\${metaChar}` : \"(?:|/.*)\"\n    )}$`\n  );\n}\nfunction clearWildcardRegExpCache() {\n  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);\n}\nfunction buildMatcherFromPreprocessedRoutes(routes) {\n  const trie = new Trie();\n  const handlerData = [];\n  if (routes.length === 0) {\n    return nullMatcher;\n  }\n  const routesWithStaticPathFlag = routes.map(\n    (route) => [!/\\*|\\/:/.test(route[0]), ...route]\n  ).sort(\n    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length\n  );\n  const staticMap = /* @__PURE__ */ Object.create(null);\n  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {\n    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];\n    if (pathErrorCheckOnly) {\n      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];\n    } else {\n      j++;\n    }\n    let paramAssoc;\n    try {\n      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);\n    } catch (e) {\n      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;\n    }\n    if (pathErrorCheckOnly) {\n      continue;\n    }\n    handlerData[j] = handlers.map(([h, paramCount]) => {\n      const paramIndexMap = /* @__PURE__ */ Object.create(null);\n      paramCount -= 1;\n      for (; paramCount >= 0; paramCount--) {\n        const [key, value] = paramAssoc[paramCount];\n        paramIndexMap[key] = value;\n      }\n      return [h, paramIndexMap];\n    });\n  }\n  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();\n  for (let i = 0, len = handlerData.length; i < len; i++) {\n    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {\n      const map = handlerData[i][j]?.[1];\n      if (!map) {\n        continue;\n      }\n      const keys = Object.keys(map);\n      for (let k = 0, len3 = keys.length; k < len3; k++) {\n        map[keys[k]] = paramReplacementMap[map[keys[k]]];\n      }\n    }\n  }\n  const handlerMap = [];\n  for (const i in indexReplacementMap) {\n    handlerMap[i] = handlerData[indexReplacementMap[i]];\n  }\n  return [regexp, handlerMap, staticMap];\n}\nfunction findMiddleware(middleware, path) {\n  if (!middleware) {\n    return void 0;\n  }\n  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {\n    if (buildWildcardRegExp(k).test(path)) {\n      return [...middleware[k]];\n    }\n  }\n  return void 0;\n}\nvar RegExpRouter = class {\n  name = \"RegExpRouter\";\n  middleware;\n  routes;\n  constructor() {\n    this.middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n    this.routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };\n  }\n  add(method, path, handler) {\n    const { middleware, routes } = this;\n    if (!middleware || !routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    if (!middleware[method]) {\n      ;\n      [middleware, routes].forEach((handlerMap) => {\n        handlerMap[method] = /* @__PURE__ */ Object.create(null);\n        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {\n          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];\n        });\n      });\n    }\n    if (path === \"/*\") {\n      path = \"*\";\n    }\n    const paramCount = (path.match(/\\/:/g) || []).length;\n    if (/\\*$/.test(path)) {\n      const re = buildWildcardRegExp(path);\n      if (method === METHOD_NAME_ALL) {\n        Object.keys(middleware).forEach((m) => {\n          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n        });\n      } else {\n        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];\n      }\n      Object.keys(middleware).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(middleware[m]).forEach((p) => {\n            re.test(p) && middleware[m][p].push([handler, paramCount]);\n          });\n        }\n      });\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          Object.keys(routes[m]).forEach(\n            (p) => re.test(p) && routes[m][p].push([handler, paramCount])\n          );\n        }\n      });\n      return;\n    }\n    const paths = checkOptionalParameter(path) || [path];\n    for (let i = 0, len = paths.length; i < len; i++) {\n      const path2 = paths[i];\n      Object.keys(routes).forEach((m) => {\n        if (method === METHOD_NAME_ALL || method === m) {\n          routes[m][path2] ||= [\n            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []\n          ];\n          routes[m][path2].push([handler, paramCount - len + i + 1]);\n        }\n      });\n    }\n  }\n  match(method, path) {\n    clearWildcardRegExpCache();\n    const matchers = this.buildAllMatchers();\n    this.match = (method2, path2) => {\n      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];\n      const staticMatch = matcher[2][path2];\n      if (staticMatch) {\n        return staticMatch;\n      }\n      const match = path2.match(matcher[0]);\n      if (!match) {\n        return [[], emptyParam];\n      }\n      const index = match.indexOf(\"\", 1);\n      return [matcher[1][index], match];\n    };\n    return this.match(method, path);\n  }\n  buildAllMatchers() {\n    const matchers = /* @__PURE__ */ Object.create(null);\n    [...Object.keys(this.routes), ...Object.keys(this.middleware)].forEach((method) => {\n      matchers[method] ||= this.buildMatcher(method);\n    });\n    this.middleware = this.routes = void 0;\n    return matchers;\n  }\n  buildMatcher(method) {\n    const routes = [];\n    let hasOwnRoute = method === METHOD_NAME_ALL;\n    [this.middleware, this.routes].forEach((r) => {\n      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];\n      if (ownRoute.length !== 0) {\n        hasOwnRoute ||= true;\n        routes.push(...ownRoute);\n      } else if (method !== METHOD_NAME_ALL) {\n        routes.push(\n          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])\n        );\n      }\n    });\n    if (!hasOwnRoute) {\n      return null;\n    } else {\n      return buildMatcherFromPreprocessedRoutes(routes);\n    }\n  }\n};\nexport {\n  RegExpRouter\n};\n", "// src/router/smart-router/router.ts\nimport { UnsupportedPathError, MESSAGE_MATCHER_IS_ALREADY_BUILT } from \"../../router.js\";\nvar SmartRouter = class {\n  name = \"SmartRouter\";\n  routers = [];\n  routes = [];\n  constructor(init) {\n    Object.assign(this, init);\n  }\n  add(method, path, handler) {\n    if (!this.routes) {\n      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);\n    }\n    this.routes.push([method, path, handler]);\n  }\n  match(method, path) {\n    if (!this.routes) {\n      throw new Error(\"Fatal error\");\n    }\n    const { routers, routes } = this;\n    const len = routers.length;\n    let i = 0;\n    let res;\n    for (; i < len; i++) {\n      const router = routers[i];\n      try {\n        routes.forEach((args) => {\n          router.add(...args);\n        });\n        res = router.match(method, path);\n      } catch (e) {\n        if (e instanceof UnsupportedPathError) {\n          continue;\n        }\n        throw e;\n      }\n      this.match = router.match.bind(router);\n      this.routers = [router];\n      this.routes = void 0;\n      break;\n    }\n    if (i === len) {\n      throw new Error(\"Fatal error\");\n    }\n    this.name = `SmartRouter + ${this.activeRouter.name}`;\n    return res;\n  }\n  get activeRouter() {\n    if (this.routes || this.routers.length !== 1) {\n      throw new Error(\"No active router has been determined yet.\");\n    }\n    return this.routers[0];\n  }\n};\nexport {\n  SmartRouter\n};\n", "// src/router/trie-router/node.ts\nimport { METHOD_NAME_ALL } from \"../../router.js\";\nimport { splitPath, splitRoutingPath, getPattern } from \"../../utils/url.js\";\nvar Node = class {\n  methods;\n  children;\n  patterns;\n  order = 0;\n  name;\n  params = /* @__PURE__ */ Object.create(null);\n  constructor(method, handler, children) {\n    this.children = children || /* @__PURE__ */ Object.create(null);\n    this.methods = [];\n    this.name = \"\";\n    if (method && handler) {\n      const m = /* @__PURE__ */ Object.create(null);\n      m[method] = { handler, possibleKeys: [], score: 0, name: this.name };\n      this.methods = [m];\n    }\n    this.patterns = [];\n  }\n  insert(method, path, handler) {\n    this.name = `${method} ${path}`;\n    this.order = ++this.order;\n    let curNode = this;\n    const parts = splitRoutingPath(path);\n    const possibleKeys = [];\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const p = parts[i];\n      if (Object.keys(curNode.children).includes(p)) {\n        curNode = curNode.children[p];\n        const pattern2 = getPattern(p);\n        if (pattern2) {\n          possibleKeys.push(pattern2[1]);\n        }\n        continue;\n      }\n      curNode.children[p] = new Node();\n      const pattern = getPattern(p);\n      if (pattern) {\n        curNode.patterns.push(pattern);\n        possibleKeys.push(pattern[1]);\n      }\n      curNode = curNode.children[p];\n    }\n    if (!curNode.methods.length) {\n      curNode.methods = [];\n    }\n    const m = /* @__PURE__ */ Object.create(null);\n    const handlerSet = {\n      handler,\n      possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),\n      name: this.name,\n      score: this.order\n    };\n    m[method] = handlerSet;\n    curNode.methods.push(m);\n    return curNode;\n  }\n  gHSets(node, method, nodeParams, params) {\n    const handlerSets = [];\n    for (let i = 0, len = node.methods.length; i < len; i++) {\n      const m = node.methods[i];\n      const handlerSet = m[method] || m[METHOD_NAME_ALL];\n      const processedSet = /* @__PURE__ */ Object.create(null);\n      if (handlerSet !== void 0) {\n        handlerSet.params = /* @__PURE__ */ Object.create(null);\n        handlerSet.possibleKeys.forEach((key) => {\n          const processed = processedSet[handlerSet.name];\n          handlerSet.params[key] = params[key] && !processed ? params[key] : nodeParams[key] ?? params[key];\n          processedSet[handlerSet.name] = true;\n        });\n        handlerSets.push(handlerSet);\n      }\n    }\n    return handlerSets;\n  }\n  search(method, path) {\n    const handlerSets = [];\n    this.params = /* @__PURE__ */ Object.create(null);\n    const curNode = this;\n    let curNodes = [curNode];\n    const parts = splitPath(path);\n    for (let i = 0, len = parts.length; i < len; i++) {\n      const part = parts[i];\n      const isLast = i === len - 1;\n      const tempNodes = [];\n      for (let j = 0, len2 = curNodes.length; j < len2; j++) {\n        const node = curNodes[j];\n        const nextNode = node.children[part];\n        if (nextNode) {\n          nextNode.params = node.params;\n          if (isLast === true) {\n            if (nextNode.children[\"*\"]) {\n              handlerSets.push(\n                ...this.gHSets(nextNode.children[\"*\"], method, node.params, /* @__PURE__ */ Object.create(null))\n              );\n            }\n            handlerSets.push(...this.gHSets(nextNode, method, node.params, /* @__PURE__ */ Object.create(null)));\n          } else {\n            tempNodes.push(nextNode);\n          }\n        }\n        for (let k = 0, len3 = node.patterns.length; k < len3; k++) {\n          const pattern = node.patterns[k];\n          const params = { ...node.params };\n          if (pattern === \"*\") {\n            const astNode = node.children[\"*\"];\n            if (astNode) {\n              handlerSets.push(...this.gHSets(astNode, method, node.params, /* @__PURE__ */ Object.create(null)));\n              tempNodes.push(astNode);\n            }\n            continue;\n          }\n          if (part === \"\") {\n            continue;\n          }\n          const [key, name, matcher] = pattern;\n          const child = node.children[key];\n          const restPathString = parts.slice(i).join(\"/\");\n          if (matcher instanceof RegExp && matcher.test(restPathString)) {\n            params[name] = restPathString;\n            handlerSets.push(...this.gHSets(child, method, node.params, params));\n            continue;\n          }\n          if (matcher === true || matcher instanceof RegExp && matcher.test(part)) {\n            if (typeof key === \"string\") {\n              params[name] = part;\n              if (isLast === true) {\n                handlerSets.push(...this.gHSets(child, method, params, node.params));\n                if (child.children[\"*\"]) {\n                  handlerSets.push(...this.gHSets(child.children[\"*\"], method, params, node.params));\n                }\n              } else {\n                child.params = params;\n                tempNodes.push(child);\n              }\n            }\n          }\n        }\n      }\n      curNodes = tempNodes;\n    }\n    const results = handlerSets.sort((a, b) => {\n      return a.score - b.score;\n    });\n    return [results.map(({ handler, params }) => [handler, params])];\n  }\n};\nexport {\n  Node\n};\n", "// src/router/trie-router/router.ts\nimport { checkOptionalParameter } from \"../../utils/url.js\";\nimport { Node } from \"./node.js\";\nvar TrieRouter = class {\n  name = \"TrieRouter\";\n  node;\n  constructor() {\n    this.node = new Node();\n  }\n  add(method, path, handler) {\n    const results = checkOptionalParameter(path);\n    if (results) {\n      for (const p of results) {\n        this.node.insert(method, p, handler);\n      }\n      return;\n    }\n    this.node.insert(method, path, handler);\n  }\n  match(method, path) {\n    return this.node.search(method, path);\n  }\n};\nexport {\n  TrieRouter\n};\n", "// src/hono.ts\nimport { HonoBase } from \"./hono-base.js\";\nimport { RegExpRouter } from \"./router/reg-exp-router/index.js\";\nimport { SmartRouter } from \"./router/smart-router/index.js\";\nimport { TrieRouter } from \"./router/trie-router/index.js\";\nvar Hono = class extends HonoBase {\n  constructor(options = {}) {\n    super(options);\n    this.router = options.router ?? new SmartRouter({\n      routers: [new RegExpRouter(), new TrieRouter()]\n    });\n  }\n};\nexport {\n  Hono\n};\n", "// src/middleware/cors/index.ts\nvar cors = (options) => {\n  const defaults = {\n    origin: \"*\",\n    allowMethods: [\"GET\", \"HEAD\", \"PUT\", \"POST\", \"DELETE\", \"PATCH\"],\n    allowHeaders: [],\n    exposeHeaders: []\n  };\n  const opts = {\n    ...defaults,\n    ...options\n  };\n  const findAllowOrigin = ((optsOrigin) => {\n    if (typeof optsOrigin === \"string\") {\n      return () => optsOrigin;\n    } else if (typeof optsOrigin === \"function\") {\n      return optsOrigin;\n    } else {\n      return (origin) => optsOrigin.includes(origin) ? origin : optsOrigin[0];\n    }\n  })(opts.origin);\n  return async function cors2(c, next) {\n    function set(key, value) {\n      c.res.headers.set(key, value);\n    }\n    const allowOrigin = findAllowOrigin(c.req.header(\"origin\") || \"\", c);\n    if (allowOrigin) {\n      set(\"Access-Control-Allow-Origin\", allowOrigin);\n    }\n    if (opts.origin !== \"*\") {\n      set(\"Vary\", \"Origin\");\n    }\n    if (opts.credentials) {\n      set(\"Access-Control-Allow-Credentials\", \"true\");\n    }\n    if (opts.exposeHeaders?.length) {\n      set(\"Access-Control-Expose-Headers\", opts.exposeHeaders.join(\",\"));\n    }\n    if (c.req.method === \"OPTIONS\") {\n      if (opts.maxAge != null) {\n        set(\"Access-Control-Max-Age\", opts.maxAge.toString());\n      }\n      if (opts.allowMethods?.length) {\n        set(\"Access-Control-Allow-Methods\", opts.allowMethods.join(\",\"));\n      }\n      let headers = opts.allowHeaders;\n      if (!headers?.length) {\n        const requestHeaders = c.req.header(\"Access-Control-Request-Headers\");\n        if (requestHeaders) {\n          headers = requestHeaders.split(/\\s*,\\s*/);\n        }\n      }\n      if (headers?.length) {\n        set(\"Access-Control-Allow-Headers\", headers.join(\",\"));\n        c.res.headers.append(\"Vary\", \"Access-Control-Request-Headers\");\n      }\n      c.res.headers.delete(\"Content-Length\");\n      c.res.headers.delete(\"Content-Type\");\n      return new Response(null, {\n        headers: c.res.headers,\n        status: 204,\n        statusText: c.res.statusText\n      });\n    }\n    await next();\n  };\n};\nexport {\n  cors\n};\n", "export const airtableTableIds = {\r\n\t// test-pixel-2024\r\n\tdev: {\r\n\t\t'lecturer-committees': 'tblxZzG59RtotfzHe',\r\n\t\t'student-committees': 'tblk9oEv9bm9ZSLOm',\r\n\t\tsponsors: 'tblqlcf4TbQF3ak88',\r\n\t\tpartners: 'tblvSRoyEuXSYVTwM',\r\n\t\tparticipants: 'tblhoCRoIj6siTO5t',\r\n\t\tprojects: 'tbltHjlTEyMZ2saRl',\r\n\t\tjudges: 'tbl6T4oc8v0xDMHie',\r\n\t\tevaluations: 'tbl27CktlZWC19usq',\r\n\t\tsupervisors: 'tbl6snipFozMptRmX',\r\n\t},\r\n\t// pixel-2024\r\n\tprod: {\r\n\t\t'lecturer-committees': 'tblv7TO7bygJnMVkO',\r\n\t\t'student-committees': 'tblihIMxbS9uTp7rW',\r\n\t\tsponsors: 'tblotwn6VSD0XHGLI',\r\n\t\tpartners: 'tblt0bwAGbKdSsf9m',\r\n\t\tparticipants: 'tblfwWZqK0TNcqaI3',\r\n\t\tprojects: 'tblrPDtVGfzkWZwuV',\r\n\t\tjudges: 'tbl41oweacNSxj3VO',\r\n\t\tevaluations: 'tbl0fWsvnGJXVGQ50',\r\n\t\tsupervisors: 'tbl4AHqrH5m7j0dZx',\r\n\t},\r\n};\r\n\r\n// remember to turn off cache when modifying this\r\nexport const encryptionSettings = [\r\n\t{\r\n\t\ttable: 'lecturer-committees',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'photo', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'student-committees',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'ic', type: 0 }, // sensitive info and not needed to display\r\n\t\t\t// { column: 'photo', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'sponsors',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'image', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'partners',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'image', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'participants',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'firebase_uid', type: 0 },\r\n\t\t\t// // { column: 'ic', type: 0 }, // not encrypting this prolly going to show to participant?\r\n\t\t\t// { column: 'project_id', type: 0 },\r\n\t\t\t// { column: 'update', type: 0 },\r\n\t\t\t// { column: 'photoX', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'projects',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'supervisor_id', type: 1 },\r\n\t\t\t// { column: 'updated_by', type: 1 },\r\n\t\t\t// { column: 'leader_id', type: 1 }, // somehow when setup airtable, it's link to multiple record\r\n\t\t\t// { column: 'members_id', type: 1 },\r\n\t\t\t// { column: 'members_photo', type: 2 },\r\n\t\t\t// { column: 'members_firebase_uid', type: 1 },\r\n\t\t\t// { column: 'evaluation_id', type: 0 },\r\n\t\t\t// { column: 'judge', type: 1 },\r\n\t\t\t// { column: 'judge_id', type: 1 },\r\n\t\t\t// { column: 'members_photoX', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'judges',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'firebase_uid', type: 0 },\r\n\t\t\t// { column: 'assigned_projects', type: 1 },\r\n\t\t\t// { column: 'evaluated_projects_id', type: 1 },\r\n\t\t\t// { column: 'photoX', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'evaluations',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'evaluation_id', type: 0 },\r\n\t\t\t// { column: 'judge_id', type: 1 },\r\n\t\t\t// { column: 'project_id', type: 1 }, // somehow when setup airtable, it's link to multiple record\r\n\t\t\t// { column: 'judge_photo', type: 2 },\r\n\t\t],\r\n\t},\r\n\t{\r\n\t\ttable: 'supervisors',\r\n\t\tencrypt_fields: [\r\n\t\t\t{ column: 'id', type: 0 },\r\n\t\t\t// { column: 'projects', type: 1 },\r\n\t\t],\r\n\t},\r\n];\r\n", "import { encryptionSettings } from './config';\r\n\r\nclass Crypto {\r\n\tprivate secretKey = 'csstechiespixel';\r\n\r\n\tprivate encrypt(plaintext) {\r\n\t\tlet ciphertext = '';\r\n\t\tfor (let i = 0; i < plaintext.length; i++) {\r\n\t\t\tlet charCode = plaintext.charCodeAt(i) ^ this.secretKey.charCodeAt(i % this.secretKey.length);\r\n\t\t\tciphertext += String.fromCharCode(charCode);\r\n\t\t}\r\n\t\treturn encodeURIComponent(ciphertext);\r\n\t}\r\n\r\n\tdecrypt(ciphertext) {\r\n\t\tciphertext = decodeURIComponent(ciphertext);\r\n\t\tlet plaintext = '';\r\n\t\tfor (let i = 0; i < ciphertext.length; i++) {\r\n\t\t\tlet charCode = ciphertext.charCodeAt(i) ^ this.secretKey.charCodeAt(i % this.secretKey.length);\r\n\t\t\tplaintext += String.fromCharCode(charCode);\r\n\t\t}\r\n\t\treturn plaintext;\r\n\t}\r\n\r\n\tencryptFields(data: any, table: string) {\r\n\t\tconst settings = encryptionSettings.find((option) => option.table === table);\r\n\r\n\t\tif (!settings) return data;\r\n\t\tif (!data) return undefined;\r\n\r\n\t\tfor (const field of settings.encrypt_fields) {\r\n\t\t\tconst { column, type } = field;\r\n\r\n\t\t\t// check if the column exists in the unencrypted data\r\n\t\t\tif (data.hasOwnProperty(column)) {\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t// Column type: single value field\r\n\t\t\t\t\t\t// {targeted_field: 'id', name: 'name'}\r\n\t\t\t\t\t\t// => {targeted_field: 'encrypted_id', name: 'name'}\r\n\t\t\t\t\t\tif (typeof data[column] === 'string') {\r\n\t\t\t\t\t\t\tdata[column] = this.encrypt(data[column]);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t// Column type: Multiselect or Link to multiple records\r\n\t\t\t\t\t\t// {targeted_field: ['id1', 'id2'], names: ['name1', 'name2']}\r\n\t\t\t\t\t\t// => {targeted_field: ['encrypted_id1', 'encrypted_id2'], names: ['name1', 'name2']}\r\n\t\t\t\t\t\tif (Array.isArray(data[column])) {\r\n\t\t\t\t\t\t\tdata[column] = data[column].map((item: string) => this.encrypt(item));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\t// Column type: attachment field (eg) photo, pdf\r\n\t\t\t\t\t\t// {photos: [{id: 'id1', name: 'name1'}, {id: 'id2', name: 'name2'}]}\r\n\t\t\t\t\t\t// => {photos: [{id: 'encrypted_id1', name: 'name1'}, {id: 'encrypted_id2', name: 'name2'}]}\r\n\t\t\t\t\t\tif (Array.isArray(data[column])) {\r\n\t\t\t\t\t\t\tdata[column] = data[column].map((file: any) => {\r\n\t\t\t\t\t\t\t\tif (file.id) {\r\n\t\t\t\t\t\t\t\t\tfile.id = this.encrypt(file.id);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\treturn file;\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tthrow new Error(`Invalid column_type for column: ${column}`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn data;\r\n\t}\r\n\r\n\tdecryptFields(data: any, table: string) {\r\n\t\tconst settings = encryptionSettings.find((option) => option.table === table);\r\n\t\tif (!settings) return data;\r\n\r\n\t\tif (Object.keys(data.fields).length == 0) {\r\n\t\t\treturn data;\r\n\t\t}\r\n\r\n\t\tfor (const field of settings.encrypt_fields) {\r\n\t\t\tconst { column, type } = field;\r\n\t\t\t// console.log(data.hasOwnProperty(column)); // false\r\n\t\t\tif (data.fields.hasOwnProperty(column)) {\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 0:\r\n\t\t\t\t\t\t// Single field decryption\r\n\t\t\t\t\t\tif (typeof data.fields[column] === 'string') {\r\n\t\t\t\t\t\t\tdata.fields[column] = this.decrypt(data.fields[column]);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1:\r\n\t\t\t\t\t\t// Multiselect field decryption\r\n\t\t\t\t\t\tif (Array.isArray(data.fields[column])) {\r\n\t\t\t\t\t\t\tdata.fields[column] = data.fields[column].map((item: string) => this.decrypt(item));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 2:\r\n\t\t\t\t\t\t// Special case like photo field decryption\r\n\t\t\t\t\t\tif (Array.isArray(data.fields[column])) {\r\n\t\t\t\t\t\t\tdata.fields[column] = data.fields[column].map((file: any) => {\r\n\t\t\t\t\t\t\t\tif (file.id) {\r\n\t\t\t\t\t\t\t\t\tfile.id = this.decrypt(file.id);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\treturn file;\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.error(`Mismatch in expected type for column: ${column}`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\tthrow new Error(`Invalid column_type for column: ${column}`);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\treturn data;\r\n\t}\r\n}\r\n\r\nexport default new Crypto();\r\n", "import { StatusCode } from 'hono/utils/http-status';\r\nimport Crypto from './crypto';\r\n\r\ntype ResponseType = {\r\n\tmessage: string;\r\n\tresponse_code: StatusCode;\r\n\tdata: any;\r\n\toffset?: string;\r\n};\r\n\r\nclass Api {\r\n\tprivate tableName: string;\r\n\tprivate input: string;\r\n\tprivate ENCRYPT_FEATURE: boolean;\r\n\r\n\tprivate endpoint: string;\r\n\r\n\tprivate headers: {\r\n\t\tAuthorization: string;\r\n\t\t'Content-Type': string;\r\n\t};\r\n\r\n\tconstructor(c: any) {\r\n\t\tconst tableId = c.get('tableId');\r\n\t\tlet recordId = c.get('recordId');\r\n\t\tif (c.env.ENCRYPT_FEATURE) recordId = Crypto.decrypt(recordId);\r\n\t\tthis.ENCRYPT_FEATURE = c.env.ENCRYPT_FEATURE;\r\n\r\n\t\tconst query = c.get('query');\r\n\r\n\t\tthis.tableName = c.get('tableName');\r\n\t\tthis.input = c.get('input');\r\n\r\n\t\t// configuring\r\n\t\tlet airtableBaseId: string;\r\n\t\tlet airtableTableId: string;\r\n\t\tconst mode = c.get('mode');\r\n\t\tswitch (mode) {\r\n\t\t\t// TEST-PIXEL-2024 Airtable\r\n\t\t\tcase 'dev':\r\n\t\t\t\tairtableTableId = c.env.DEV_AIRTABLETOKEN as string;\r\n\t\t\t\tairtableBaseId = 'appX3SKijXuWC2zpD';\r\n\t\t\t\tbreak;\r\n\t\t\t// PIXEL2024 Airtable\r\n\t\t\tcase 'prod':\r\n\t\t\t\tairtableTableId = c.env.PROD_AIRTABLETOKEN as string;\r\n\t\t\t\tairtableBaseId = 'appVbcSklEhhwzV2d';\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tthrow new Error('Invalid mode');\r\n\t\t}\r\n\r\n\t\tthis.endpoint = `https://api.airtable.com/v0/${airtableBaseId}/${tableId}${recordId ? '/' + recordId : query}`;\r\n\r\n\t\tthis.headers = {\r\n\t\t\tAuthorization: `Bearer ${airtableTableId}`,\r\n\t\t\t'Content-Type': 'application/json',\r\n\t\t};\r\n\t}\r\n\r\n\t/**\r\n\t * Format response to be return\r\n\t */\r\n\tprivate sanitizeData(message: string, response_code: StatusCode, data: any, offset?: string): ResponseType {\r\n\t\treturn {\r\n\t\t\tmessage,\r\n\t\t\tresponse_code,\r\n\t\t\tdata,\r\n\t\t\toffset,\r\n\t\t};\r\n\t}\r\n\r\n\tprivate async transformData(response: Response): Promise<any> {\r\n\t\tconst data = await response.json();\r\n\r\n\t\tif (!!data.records) {\r\n\t\t\tconst transformedData = data.records.map((record) => {\r\n\t\t\t\t// Assuming api.get result: [{id, field, field2}, {id2, field, field2}]\r\n\t\t\t\treturn {\r\n\t\t\t\t\tid: record.id,\r\n\t\t\t\t\t...record.fields,\r\n\t\t\t\t};\r\n\t\t\t});\r\n\r\n\t\t\t// if records fetched are more than 100, return data with offset\r\n\t\t\treturn data.offset ? { transformedData, offset: data.offset } : transformedData;\r\n\t\t} else {\r\n\t\t\treturn { id: data.id, ...data.fields };\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @route GET /BASE_TABLE\r\n\t * @desc Get all records from the BASE_TABLE\r\n\t */\r\n\tasync get(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t});\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.get: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\r\n\t\t\tif (data.offset != undefined) {\r\n\t\t\t\treturn this.sanitizeData(\r\n\t\t\t\t\t'OK',\r\n\t\t\t\t\t200,\r\n\t\t\t\t\tthis.ENCRYPT_FEATURE ? data.transformedData.map((item: any) => Crypto.encryptFields(item, this.tableName)) : data.transformedData,\r\n\t\t\t\t\tdata.offset\r\n\t\t\t\t);\r\n\t\t\t} else {\r\n\t\t\t\treturn this.sanitizeData(\r\n\t\t\t\t\t'OK',\r\n\t\t\t\t\t200,\r\n\t\t\t\t\tthis.ENCRYPT_FEATURE ? data.map((item: any) => Crypto.encryptFields(item, this.tableName)) : data\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.get: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @route GET /BASE_TABLE/:id\r\n\t * @param {string} airtableRecordId or firebaseUid\r\n\t * @desc Get a single record from the BASE_TABLE\r\n\t */\r\n\tasync first(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'GET',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.first: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\t\t\treturn this.sanitizeData('OK', 200, this.ENCRYPT_FEATURE ? Crypto.encryptFields(data, this.tableName) : data);\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.first: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @route POST /BASE_TABLE\r\n\t * @param {object} input\r\n\t * @returns http status code\r\n\t */\r\n\tasync post(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'POST',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t\tbody: JSON.stringify(this.input),\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.post: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\t\t\treturn this.sanitizeData('OK', 201, this.ENCRYPT_FEATURE ? Crypto.encryptFields(data, this.tableName) : data);\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.post: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @route PUT /BASE_TABLE/:id\r\n\t * @returns http status code\r\n\t */\r\n\tasync put(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'PUT',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t\tbody: JSON.stringify(this.input),\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.put: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\t\t\treturn this.sanitizeData('OK', 200, this.ENCRYPT_FEATURE ? Crypto.encryptFields(data, this.tableName) : data);\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.put: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @example\r\n\t * getAirtableID = getMembersAirtableId(uid).then(async (recordId) => {\r\n\t *\r\n\t *   response = await api.patch(\r\n\t *     \"/participants/\" + recordId,\r\n\t *     JSON.stringify({\r\n\t *       fields: { ...data },\r\n\t *     })\r\n\t *   );\r\n\t * })\r\n\t */\r\n\tasync patch(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'PATCH',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t\tbody: JSON.stringify(this.input),\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.patch: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\t\t\treturn this.sanitizeData('OK', 200, this.ENCRYPT_FEATURE ? Crypto.encryptFields(data, this.tableName) : data);\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.patch: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @example\r\n\t * try {\r\n\t * \tawait api.delete(\"projects\", id);\r\n\t * \t} catch (error) {\r\n\t * \tconsole.error(error);\r\n\t * \tthrow new Error(error);\r\n\t * }\r\n\t */\r\n\tasync delete(): Promise<ResponseType> {\r\n\t\ttry {\r\n\t\t\tconst response = await fetch(this.endpoint, {\r\n\t\t\t\tmethod: 'DELETE',\r\n\t\t\t\theaders: this.headers,\r\n\t\t\t});\r\n\r\n\t\t\tif (!response.ok) {\r\n\t\t\t\tconst message = await response.text();\r\n\t\t\t\treturn this.sanitizeData('error in backend api.delete: ' + message, 500, undefined);\r\n\t\t\t}\r\n\r\n\t\t\tconst data = await this.transformData(response);\r\n\t\t\treturn this.sanitizeData('OK', 200, this.ENCRYPT_FEATURE ? Crypto.encryptFields(data, this.tableName) : data);\r\n\t\t} catch (error) {\r\n\t\t\treturn this.sanitizeData('error in backend api.delete: ' + error, 500, null);\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport default Api;\r\n", "import Api from '@/utils/Api';\r\nimport { Hono } from 'hono';\r\n\r\nconst route = new Hono();\r\n\r\n//get all records\r\nroute.get('/', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, response_code, data, offset } = await api.get();\r\n\t\treturn c.json({ data, message, offset }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend GET /' }, 500);\r\n\t}\r\n});\r\n\r\n//get one record\r\nroute.get('/:id', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, response_code, data } = await api.first();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend GET /:id' }, 500);\r\n\t}\r\n});\r\n\r\n//create a record\r\nroute.post('/', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, response_code, data } = await api.post();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend POST /' }, 500);\r\n\t}\r\n});\r\n\r\n//update a record\r\nroute.put('/:id', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, data, response_code } = await api.put();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend PUT /' }, 500);\r\n\t}\r\n});\r\n\r\nroute.patch('/:id', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, data, response_code } = await api.patch();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend PATCH /:id' }, 500);\r\n\t}\r\n});\r\n\r\nroute.patch('/', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, data, response_code } = await api.patch();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend PATCH /' }, 500);\r\n\t}\r\n});\r\n\r\n//delete a record\r\nroute.delete('/:id', async (c) => {\r\n\tconst api = new Api(c);\r\n\r\n\ttry {\r\n\t\tconst { message, response_code, data } = await api.delete();\r\n\t\treturn c.json({ data, message }, response_code);\r\n\t} catch (error) {\r\n\t\treturn c.json({ data: null, message: 'error in backend DELETE /:id' }, 500);\r\n\t}\r\n});\r\n\r\nexport { route as Router };\r\n", "import { airtableTableIds } from '@/utils';\r\nimport Crypto from '@/utils/crypto';\r\n\r\nconst MiddleWare = async (c, next) => {\r\n\tconst url = new URL(c.req.url);\r\n\tconst mode = url.pathname.split('/')[1];\r\n\tconst table = url.pathname.split('/')[2];\r\n\r\n\t// values for api constructor\r\n\tc.set('mode', mode);\r\n\tc.set('tableId', airtableTableIds[mode][table]);\r\n\tc.set('tableName', table);\r\n\r\n\tc.set('recordId', url.pathname.split('/')[3]);\r\n\tc.set('query', url.search ?? '');\r\n\r\n\tconst method = c.req.method;\r\n\tif (method === 'POST' || method === 'PUT' || method === 'PATCH') {\r\n\t\tconst data = await c.req.json();\r\n\t\tlet input: any;\r\n\t\tif (c.env.ENCRYPT_FEATURE) {\r\n\t\t\tinput = Crypto.decryptFields(data, table);\r\n\t\t} else {\r\n\t\t\tinput = data;\r\n\t\t}\r\n\r\n\t\tc.set('input', input);\r\n\t}\r\n\tawait next();\r\n};\r\n\r\nexport { MiddleWare };\r\n", "// src/middleware/cache/index.ts\nvar cache = (options) => {\n  if (!globalThis.caches) {\n    console.log(\"Cache Middleware is not enabled because caches is not defined.\");\n    return async (_c, next) => await next();\n  }\n  if (options.wait === void 0) {\n    options.wait = false;\n  }\n  const cacheControlDirectives = options.cacheControl?.split(\",\").map((directive) => directive.toLowerCase());\n  const varyDirectives = Array.isArray(options.vary) ? options.vary : options.vary?.split(\",\").map((directive) => directive.trim());\n  if (options.vary?.includes(\"*\")) {\n    throw new Error(\n      'Middleware vary configuration cannot include \"*\", as it disallows effective caching.'\n    );\n  }\n  const addHeader = (c) => {\n    if (cacheControlDirectives) {\n      const existingDirectives = c.res.headers.get(\"Cache-Control\")?.split(\",\").map((d) => d.trim().split(\"=\", 1)[0]) ?? [];\n      for (const directive of cacheControlDirectives) {\n        let [name, value] = directive.trim().split(\"=\", 2);\n        name = name.toLowerCase();\n        if (!existingDirectives.includes(name)) {\n          c.header(\"Cache-Control\", `${name}${value ? `=${value}` : \"\"}`, { append: true });\n        }\n      }\n    }\n    if (varyDirectives) {\n      const existingDirectives = c.res.headers.get(\"Vary\")?.split(\",\").map((d) => d.trim()) ?? [];\n      const vary = Array.from(\n        new Set(\n          [...existingDirectives, ...varyDirectives].map((directive) => directive.toLowerCase())\n        )\n      ).sort();\n      if (vary.includes(\"*\")) {\n        c.header(\"Vary\", \"*\");\n      } else {\n        c.header(\"Vary\", vary.join(\", \"));\n      }\n    }\n  };\n  return async function cache2(c, next) {\n    let key = c.req.url;\n    if (options.keyGenerator) {\n      key = await options.keyGenerator(c);\n    }\n    const cacheName = typeof options.cacheName === \"function\" ? await options.cacheName(c) : options.cacheName;\n    const cache3 = await caches.open(cacheName);\n    const response = await cache3.match(key);\n    if (response) {\n      return new Response(response.body, response);\n    }\n    await next();\n    if (!c.res.ok) {\n      return;\n    }\n    addHeader(c);\n    const res = c.res.clone();\n    if (options.wait) {\n      await cache3.put(key, res);\n    } else {\n      c.executionCtx.waitUntil(cache3.put(key, res));\n    }\n  };\n};\nexport {\n  cache\n};\n", "// src/utils/color.ts\nfunction getColorEnabled() {\n  const { process, Deno } = globalThis;\n  const isNoColor = typeof process !== \"undefined\" ? \"NO_COLOR\" in process?.env : typeof Deno?.noColor === \"boolean\" ? Deno.noColor : false;\n  return !isNoColor;\n}\nexport {\n  getColorEnabled\n};\n", "// src/middleware/logger/index.ts\nimport { getColorEnabled } from \"../../utils/color.js\";\nimport { getPath } from \"../../utils/url.js\";\nvar humanize = (times) => {\n  const [delimiter, separator] = [\",\", \".\"];\n  const orderTimes = times.map((v) => v.replace(/(\\d)(?=(\\d\\d\\d)+(?!\\d))/g, \"$1\" + delimiter));\n  return orderTimes.join(separator);\n};\nvar time = (start) => {\n  const delta = Date.now() - start;\n  return humanize([delta < 1e3 ? delta + \"ms\" : Math.round(delta / 1e3) + \"s\"]);\n};\nvar colorStatus = (status) => {\n  const colorEnabled = getColorEnabled();\n  const out = {\n    7: colorEnabled ? `\\x1B[35m${status}\\x1B[0m` : `${status}`,\n    5: colorEnabled ? `\\x1B[31m${status}\\x1B[0m` : `${status}`,\n    4: colorEnabled ? `\\x1B[33m${status}\\x1B[0m` : `${status}`,\n    3: colorEnabled ? `\\x1B[36m${status}\\x1B[0m` : `${status}`,\n    2: colorEnabled ? `\\x1B[32m${status}\\x1B[0m` : `${status}`,\n    1: colorEnabled ? `\\x1B[32m${status}\\x1B[0m` : `${status}`,\n    0: colorEnabled ? `\\x1B[33m${status}\\x1B[0m` : `${status}`\n  };\n  const calculateStatus = status / 100 | 0;\n  return out[calculateStatus];\n};\nfunction log(fn, prefix, method, path, status = 0, elapsed) {\n  const out = prefix === \"<--\" /* Incoming */ ? `  ${prefix} ${method} ${path}` : `  ${prefix} ${method} ${path} ${colorStatus(status)} ${elapsed}`;\n  fn(out);\n}\nvar logger = (fn = console.log) => {\n  return async function logger2(c, next) {\n    const { method } = c.req;\n    const path = getPath(c.req.raw);\n    log(fn, \"<--\" /* Incoming */, method, path);\n    const start = Date.now();\n    await next();\n    log(fn, \"-->\" /* Outgoing */, method, path, c.res.status, time(start));\n  };\n};\nexport {\n  logger\n};\n", "import { Hono } from 'hono';\r\nimport { cors } from 'hono/cors';\r\nimport { Router } from './routes/Router';\r\nimport { MiddleWare } from './routes/Middleware';\r\nimport { cache } from 'hono/cache';\r\nimport { logger } from 'hono/logger'\r\n\r\nconst app = new Hono();\r\nconst origins = ['http://localhost:4321', 'https://pixelusm.com'];\r\n\r\n// Function to dynamically match subdomains under pixel2024.pages.dev at https://(branch-name).pixel2024.pages.dev\r\nconst dynamicOrigin = /^https:\\/\\/[a-zA-Z0-9-]+\\.pixel2024\\.pages\\.dev(\\/.*)?$/;\r\n\r\nconst corsConfig = {\r\n\torigin: (origin: string) => {\r\n\t\treturn origins.includes(origin) || dynamicOrigin.test(origin) ? origin : null;\r\n\t},\r\n\tallowHeaders: ['X-Custom-Header', 'Upgrade-Insecure-Requests', 'Content-Type', 'Origin', 'X-CSRF-Token'],\r\n\tallowMethods: ['POST', 'GET', 'PUT', 'DELETE', 'PATCH'],\r\n\texposeHeaders: ['Content-Length', 'X-Kuma-Revision', 'Origin'],\r\n\tmaxAge: 600,\r\n\tcredentials: true,\r\n};\r\n\r\napp.use('*', cors(corsConfig));\r\napp.use(logger())\r\napp.get('/', (c) => c.text('Hello Welcome to the PIXEL 2024 TEST API!'));\r\napp.get('/testbackend', (c) => c.text('Connected to PIXEL Backend'));\r\n\r\napp.use('/dev/*', async (c, next) => await MiddleWare(c, next));\r\napp.use('/prod/*', async (c, next) => await MiddleWare(c, next));\r\n\r\nconst cacheConfig = {\r\n\tcacheName: 'hono',\r\n\tcacheControl: 'max-age=1800', // expire in 1 hour\r\n};\r\n\r\nconst routes = [\r\n\t{ path: 'lecturer-committees', cache: true },\r\n\t{ path: 'student-committees', cache: true },\r\n\t{ path: 'sponsors', cache: true },\r\n\t{ path: 'partners', cache: true },\r\n\t{ path: 'participants', cache: false }, //true when the submission is closed\r\n\t{ path: 'projects', cache: false }, // true when the submission is closed\r\n\t{ path: 'judges', cache: false },\r\n\t{ path: 'evaluations', cache: false },\r\n\t{ path: 'supervisors', cache: true },\r\n];\r\n\r\nroutes.forEach((route) => {\r\n\t// cache the routes we are fetching only\r\n\tif (route.cache) {\r\n\t\tapp.get(`/dev/${route.path}`, cache(cacheConfig));\r\n\t\tapp.get(`/prod/${route.path}`, cache(cacheConfig));\r\n\t}\r\n\tapp.route(`/dev/${route.path}`, Router);\r\n\tapp.route(`/prod/${route.path}`, Router);\r\n});\r\n\r\nexport default app;\r\n", "import type { Middleware } from \"./common\";\n\nconst drainBody: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} finally {\n\t\ttry {\n\t\t\tif (request.body !== null && !request.bodyUsed) {\n\t\t\t\tconst reader = request.body.getReader();\n\t\t\t\twhile (!(await reader.read()).done) {}\n\t\t\t}\n\t\t} catch (e) {\n\t\t\tconsole.error(\"Failed to drain the unused request body.\", e);\n\t\t}\n\t}\n};\n\nexport default drainBody;\n", "import type { Middleware } from \"./common\";\n\ninterface JsonError {\n\tmessage?: string;\n\tname?: string;\n\tstack?: string;\n\tcause?: JsonError;\n}\n\nfunction reduceError(e: any): JsonError {\n\treturn {\n\t\tname: e?.name,\n\t\tmessage: e?.message ?? String(e),\n\t\tstack: e?.stack,\n\t\tcause: e?.cause === undefined ? undefined : reduceError(e.cause),\n\t};\n}\n\n// See comment in `bundle.ts` for details on why this is needed\nconst jsonError: Middleware = async (request, env, _ctx, middlewareCtx) => {\n\ttry {\n\t\treturn await middlewareCtx.next(request, env);\n\t} catch (e: any) {\n\t\tconst error = reduceError(e);\n\t\treturn Response.json(error, {\n\t\t\tstatus: 500,\n\t\t\theaders: { \"MF-Experimental-Error-Stack\": \"true\" },\n\t\t});\n\t}\n};\n\nexport default jsonError;\n", "\t\t\t\timport worker, * as OTHER_EXPORTS from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\timport * as __MIDDLEWARE_0__ from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-ensure-req-body-drained.ts\";\nimport * as __MIDDLEWARE_1__ from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\middleware-miniflare3-json-error.ts\";\n\n\t\t\t\texport * from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\src\\\\index.ts\";\n\t\t\t\tconst MIDDLEWARE_TEST_INJECT = \"__INJECT_FOR_TESTING_WRANGLER_MIDDLEWARE__\";\n\t\t\t\texport const __INTERNAL_WRANGLER_MIDDLEWARE__ = [\n\t\t\t\t\t\n\t\t\t\t\t__MIDDLEWARE_0__.default,__MIDDLEWARE_1__.default\n\t\t\t\t]\n\t\t\t\texport default worker;", "export type Awaitable<T> = T | Promise<T>;\n// TODO: allow dispatching more events?\nexport type Dispatcher = (\n\ttype: \"scheduled\",\n\tinit: { cron?: string }\n) => Awaitable<void>;\n\nexport type IncomingRequest = Request<\n\tunknown,\n\tIncomingRequestCfProperties<unknown>\n>;\n\nexport interface MiddlewareContext {\n\tdispatch: Dispatcher;\n\tnext(request: IncomingRequest, env: any): Awaitable<Response>;\n}\n\nexport type Middleware = (\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tmiddlewareCtx: MiddlewareContext\n) => Awaitable<Response>;\n\nconst __facade_middleware__: Middleware[] = [];\n\n// The register functions allow for the insertion of one or many middleware,\n// We register internal middleware first in the stack, but have no way of controlling\n// the order that addMiddleware is run in service workers so need an internal function.\nexport function __facade_register__(...args: (Middleware | Middleware[])[]) {\n\t__facade_middleware__.push(...args.flat());\n}\nexport function __facade_registerInternal__(\n\t...args: (Middleware | Middleware[])[]\n) {\n\t__facade_middleware__.unshift(...args.flat());\n}\n\nfunction __facade_invokeChain__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tmiddlewareChain: Middleware[]\n): Awaitable<Response> {\n\tconst [head, ...tail] = middlewareChain;\n\tconst middlewareCtx: MiddlewareContext = {\n\t\tdispatch,\n\t\tnext(newRequest, newEnv) {\n\t\t\treturn __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);\n\t\t},\n\t};\n\treturn head(request, env, ctx, middlewareCtx);\n}\n\nexport function __facade_invoke__(\n\trequest: IncomingRequest,\n\tenv: any,\n\tctx: ExecutionContext,\n\tdispatch: Dispatcher,\n\tfinalMiddleware: Middleware\n): Awaitable<Response> {\n\treturn __facade_invokeChain__(request, env, ctx, dispatch, [\n\t\t...__facade_middleware__,\n\t\tfinalMiddleware,\n\t]);\n}\n", "// This loads all middlewares exposed on the middleware object and then starts\n// the invocation chain. The big idea is that we can add these to the middleware\n// export dynamically through wrangler, or we can potentially let users directly\n// add them as a sort of \"plugin\" system.\n\nimport ENTRY, { __INTERNAL_WRANGLER_MIDDLEWARE__ } from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-nXxcai\\\\middleware-insertion-facade.js\";\nimport { __facade_invoke__, __facade_register__, Dispatcher } from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\node_modules\\\\wrangler\\\\templates\\\\middleware\\\\common.ts\";\nimport type { WorkerEntrypointConstructor } from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-nXxcai\\\\middleware-insertion-facade.js\";\n\n// Preserve all the exports from the worker\nexport * from \"D:\\\\PIXEL\\\\pixel2024\\\\backend\\\\.wrangler\\\\tmp\\\\bundle-nXxcai\\\\middleware-insertion-facade.js\";\n\nclass __Facade_ScheduledController__ implements ScheduledController {\n\treadonly #noRetry: ScheduledController[\"noRetry\"];\n\n\tconstructor(\n\t\treadonly scheduledTime: number,\n\t\treadonly cron: string,\n\t\tnoRetry: ScheduledController[\"noRetry\"]\n\t) {\n\t\tthis.#noRetry = noRetry;\n\t}\n\n\tnoRetry() {\n\t\tif (!(this instanceof __Facade_ScheduledController__)) {\n\t\t\tthrow new TypeError(\"Illegal invocation\");\n\t\t}\n\t\t// Need to call native method immediately in case uncaught error thrown\n\t\tthis.#noRetry();\n\t}\n}\n\nfunction wrapExportedHandler(worker: ExportedHandler): ExportedHandler {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn worker;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\tconst fetchDispatcher: ExportedHandlerFetchHandler = function (\n\t\trequest,\n\t\tenv,\n\t\tctx\n\t) {\n\t\tif (worker.fetch === undefined) {\n\t\t\tthrow new Error(\"Handler does not export a fetch() function.\");\n\t\t}\n\t\treturn worker.fetch(request, env, ctx);\n\t};\n\n\treturn {\n\t\t...worker,\n\t\tfetch(request, env, ctx) {\n\t\t\tconst dispatcher: Dispatcher = function (type, init) {\n\t\t\t\tif (type === \"scheduled\" && worker.scheduled !== undefined) {\n\t\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\t\tDate.now(),\n\t\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t\t() => {}\n\t\t\t\t\t);\n\t\t\t\t\treturn worker.scheduled(controller, env, ctx);\n\t\t\t\t}\n\t\t\t};\n\t\t\treturn __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);\n\t\t},\n\t};\n}\n\nfunction wrapWorkerEntrypoint(\n\tklass: WorkerEntrypointConstructor\n): WorkerEntrypointConstructor {\n\t// If we don't have any middleware defined, just return the handler as is\n\tif (\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__ === undefined ||\n\t\t__INTERNAL_WRANGLER_MIDDLEWARE__.length === 0\n\t) {\n\t\treturn klass;\n\t}\n\t// Otherwise, register all middleware once\n\tfor (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {\n\t\t__facade_register__(middleware);\n\t}\n\n\t// `extend`ing `klass` here so other RPC methods remain callable\n\treturn class extends klass {\n\t\t#fetchDispatcher: ExportedHandlerFetchHandler<Record<string, unknown>> = (\n\t\t\trequest,\n\t\t\tenv,\n\t\t\tctx\n\t\t) => {\n\t\t\tthis.env = env;\n\t\t\tthis.ctx = ctx;\n\t\t\tif (super.fetch === undefined) {\n\t\t\t\tthrow new Error(\"Entrypoint class does not define a fetch() function.\");\n\t\t\t}\n\t\t\treturn super.fetch(request);\n\t\t};\n\n\t\t#dispatcher: Dispatcher = (type, init) => {\n\t\t\tif (type === \"scheduled\" && super.scheduled !== undefined) {\n\t\t\t\tconst controller = new __Facade_ScheduledController__(\n\t\t\t\t\tDate.now(),\n\t\t\t\t\tinit.cron ?? \"\",\n\t\t\t\t\t() => {}\n\t\t\t\t);\n\t\t\t\treturn super.scheduled(controller);\n\t\t\t}\n\t\t};\n\n\t\tfetch(request: Request<unknown, IncomingRequestCfProperties>) {\n\t\t\treturn __facade_invoke__(\n\t\t\t\trequest,\n\t\t\t\tthis.env,\n\t\t\t\tthis.ctx,\n\t\t\t\tthis.#dispatcher,\n\t\t\t\tthis.#fetchDispatcher\n\t\t\t);\n\t\t}\n\t};\n}\n\nlet WRAPPED_ENTRY: ExportedHandler | WorkerEntrypointConstructor | undefined;\nif (typeof ENTRY === \"object\") {\n\tWRAPPED_ENTRY = wrapExportedHandler(ENTRY);\n} else if (typeof ENTRY === \"function\") {\n\tWRAPPED_ENTRY = wrapWorkerEntrypoint(ENTRY);\n}\nexport default WRAPPED_ENTRY;\n"], "mappings": ";;;;AAAA,IAAM,OAAO,oBAAI,IAAI;AAErB,SAAS,SAAS,SAAS,MAAM;AAChC,QAAM,MACL,mBAAmB,MAChB,UACA,IAAI;AAAA,KACH,OAAO,YAAY,WACjB,IAAI,QAAQ,SAAS,IAAI,IACzB,SACD;AAAA,EACH;AACH,MAAI,IAAI,QAAQ,IAAI,SAAS,SAAS,IAAI,aAAa,UAAU;AAChE,QAAI,CAAC,KAAK,IAAI,IAAI,SAAS,CAAC,GAAG;AAC9B,WAAK,IAAI,IAAI,SAAS,CAAC;AACvB,cAAQ;AAAA,QACP;AAAA,KACO,IAAI,SAAS,CAAC;AAAA;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AACD;AAnBS;AAqBT,WAAW,QAAQ,IAAI,MAAM,WAAW,OAAO;AAAA,EAC9C,MAAM,QAAQ,SAAS,UAAU;AAChC,UAAM,CAAC,SAAS,IAAI,IAAI;AACxB,aAAS,SAAS,IAAI;AACtB,WAAO,QAAQ,MAAM,QAAQ,SAAS,QAAQ;AAAA,EAC/C;AACD,CAAC;;;AC5BD,IAAI,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,QAAQ;AACV;AACA,IAAI,MAAM,wBAAC,OAAO,cAAc;AAC9B,QAAM,gBAAgB,IAAI,OAAO,KAAK;AACtC,gBAAc,YAAY;AAC1B,gBAAc,YAAY;AAC1B,SAAO;AACT,GALU;AAqEV,IAAI,kBAAkB,8BAAO,KAAK,OAAO,mBAAmB,SAAS,WAAW;AAC9E,QAAM,YAAY,IAAI;AACtB,MAAI,CAAC,WAAW,QAAQ;AACtB,WAAO,QAAQ,QAAQ,GAAG;AAAA,EAC5B;AACA,MAAI,QAAQ;AACV,WAAO,CAAC,KAAK;AAAA,EACf,OAAO;AACL,aAAS,CAAC,GAAG;AAAA,EACf;AACA,QAAM,SAAS,QAAQ,IAAI,UAAU,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE;AAAA,IAC9E,CAAC,QAAQ,QAAQ;AAAA,MACf,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,SAAS,gBAAgB,MAAM,OAAO,OAAO,SAAS,MAAM,CAAC;AAAA,IACxF,EAAE,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,EACxB;AACA,MAAI,mBAAmB;AACrB,WAAO,IAAI,MAAM,QAAQ,SAAS;AAAA,EACpC,OAAO;AACL,WAAO;AAAA,EACT;AACF,GApBsB;;;ACzEtB,IAAI,aAAa;AACjB,IAAI,aAAa,wBAAC,SAAS,MAAM,CAAC,MAAM;AACtC,SAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM,QAAQ,IAAI,KAAK,KAAK,CAAC;AACrE,SAAO;AACT,GAHiB;AAIjB,IAAI,UAAU,MAAM;AAAA,EAPpB,OAOoB;AAAA;AAAA;AAAA,EAClB;AAAA,EACA,MAAM,CAAC;AAAA,EACP,OAAO,CAAC;AAAA,EACR,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,UAAU;AAAA,EACV;AAAA,EACA,WAAW;AAAA,EACX,mBAAmB;AAAA,EACnB;AAAA,EACA,WAAW;AAAA,EACX,SAAS;AAAA,EACT,WAAW,wBAAC,YAAY,KAAK,KAAK,OAAO,GAA9B;AAAA,EACX,kBAAkB,6BAAM,IAAI,SAAS,GAAnB;AAAA,EAClB,YAAY,KAAK,SAAS;AACxB,SAAK,MAAM;AACX,QAAI,SAAS;AACX,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,MAAM,QAAQ;AACnB,UAAI,QAAQ,iBAAiB;AAC3B,aAAK,kBAAkB,QAAQ;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,iBAAiB,iBAAiB,KAAK,eAAe;AAC7D,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,gCAAgC;AAAA,IAC9C;AAAA,EACF;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,eAAe;AACtB,aAAO,KAAK;AAAA,IACd,OAAO;AACL,YAAM,MAAM,sCAAsC;AAAA,IACpD;AAAA,EACF;AAAA,EACA,IAAI,MAAM;AACR,SAAK,WAAW;AAChB,WAAO,KAAK,SAAS,IAAI,SAAS,iBAAiB,EAAE,QAAQ,IAAI,CAAC;AAAA,EACpE;AAAA,EACA,IAAI,IAAI,MAAM;AACZ,SAAK,WAAW;AAChB,QAAI,KAAK,QAAQ,MAAM;AACrB,WAAK,KAAK,QAAQ,OAAO,cAAc;AACvC,iBAAW,CAAC,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ,QAAQ,GAAG;AAChD,YAAI,MAAM,cAAc;AACtB,gBAAM,UAAU,KAAK,KAAK,QAAQ,aAAa;AAC/C,eAAK,QAAQ,OAAO,YAAY;AAChC,qBAAW,UAAU,SAAS;AAC5B,iBAAK,QAAQ,OAAO,cAAc,MAAM;AAAA,UAC1C;AAAA,QACF,OAAO;AACL,eAAK,QAAQ,IAAI,GAAG,CAAC;AAAA,QACvB;AAAA,MACF;AAAA,IACF;AACA,SAAK,OAAO;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,SAAS,2BAAI,SAAS,KAAK,SAAS,GAAG,IAAI,GAAlC;AAAA,EACT,YAAY,wBAAC,WAAW,KAAK,SAAS,QAA1B;AAAA,EACZ,YAAY,6BAAM,KAAK,QAAX;AAAA,EACZ,cAAc,wBAAC,aAAa;AAC1B,SAAK,WAAW;AAAA,EAClB,GAFc;AAAA,EAGd,SAAS,wBAAC,MAAM,OAAO,YAAY;AACjC,QAAI,UAAU,QAAQ;AACpB,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,OAAO,IAAI;AAAA,MAC3B,WAAW,KAAK,kBAAkB;AAChC,eAAO,KAAK,iBAAiB,KAAK,kBAAkB,CAAC;AAAA,MACvD;AACA,UAAI,KAAK,WAAW;AAClB,aAAK,IAAI,QAAQ,OAAO,IAAI;AAAA,MAC9B;AACA;AAAA,IACF;AACA,QAAI,SAAS,QAAQ;AACnB,UAAI,CAAC,KAAK,UAAU;AAClB,aAAK,WAAW;AAChB,aAAK,WAAW,IAAI,QAAQ,KAAK,gBAAgB;AACjD,aAAK,mBAAmB,CAAC;AAAA,MAC3B;AACA,WAAK,SAAS,OAAO,MAAM,KAAK;AAAA,IAClC,OAAO;AACL,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,IAAI,MAAM,KAAK;AAAA,MAC/B,OAAO;AACL,aAAK,qBAAqB,CAAC;AAC3B,aAAK,iBAAiB,KAAK,YAAY,CAAC,IAAI;AAAA,MAC9C;AAAA,IACF;AACA,QAAI,KAAK,WAAW;AAClB,UAAI,SAAS,QAAQ;AACnB,aAAK,IAAI,QAAQ,OAAO,MAAM,KAAK;AAAA,MACrC,OAAO;AACL,aAAK,IAAI,QAAQ,IAAI,MAAM,KAAK;AAAA,MAClC;AAAA,IACF;AAAA,EACF,GAlCS;AAAA,EAmCT,SAAS,wBAAC,WAAW;AACnB,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACjB,GAHS;AAAA,EAIT,MAAM,wBAAC,KAAK,UAAU;AACpB,SAAK,SAAS,CAAC;AACf,SAAK,KAAK,GAAG,IAAI;AAAA,EACnB,GAHM;AAAA,EAIN,MAAM,wBAAC,QAAQ;AACb,WAAO,KAAK,OAAO,KAAK,KAAK,GAAG,IAAI;AAAA,EACtC,GAFM;AAAA,EAGN,IAAI,MAAM;AACR,WAAO,EAAE,GAAG,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,cAAc,wBAAC,MAAM,KAAK,YAAY;AACpC,QAAI,KAAK,YAAY,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,KAAK;AAC7D,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,KAAK;AAAA,MAChB,CAAC;AAAA,IACH;AACA,QAAI,OAAO,OAAO,QAAQ,UAAU;AAClC,YAAM,SAAS,IAAI,QAAQ,IAAI,OAAO;AACtC,UAAI,KAAK,UAAU;AACjB,aAAK,SAAS,QAAQ,CAAC,GAAG,MAAM;AAC9B,cAAI,MAAM,cAAc;AACtB,mBAAO,OAAO,GAAG,CAAC;AAAA,UACpB,OAAO;AACL,mBAAO,IAAI,GAAG,CAAC;AAAA,UACjB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,WAAW,WAAW,QAAQ,KAAK,gBAAgB;AACzD,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS;AAAA,QACT,QAAQ,IAAI,UAAU,KAAK;AAAA,MAC7B,CAAC;AAAA,IACH;AACA,UAAM,SAAS,OAAO,QAAQ,WAAW,MAAM,KAAK;AACpD,SAAK,qBAAqB,CAAC;AAC3B,SAAK,aAAa,IAAI,QAAQ;AAC9B,eAAW,KAAK,UAAU,KAAK,gBAAgB;AAC/C,QAAI,KAAK,MAAM;AACb,WAAK,KAAK,QAAQ,QAAQ,CAAC,GAAG,MAAM;AAClC,YAAI,MAAM,cAAc;AACtB,eAAK,UAAU,OAAO,GAAG,CAAC;AAAA,QAC5B,OAAO;AACL,eAAK,UAAU,IAAI,GAAG,CAAC;AAAA,QACzB;AAAA,MACF,CAAC;AACD,iBAAW,KAAK,UAAU,KAAK,gBAAgB;AAAA,IACjD;AACA,gBAAY,CAAC;AACb,eAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,OAAO,GAAG;AAC5C,UAAI,OAAO,MAAM,UAAU;AACzB,aAAK,SAAS,IAAI,GAAG,CAAC;AAAA,MACxB,OAAO;AACL,aAAK,SAAS,OAAO,CAAC;AACtB,mBAAW,MAAM,GAAG;AAClB,eAAK,SAAS,OAAO,GAAG,EAAE;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,WAAO,IAAI,SAAS,MAAM;AAAA,MACxB;AAAA,MACA,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH,GApDc;AAAA,EAqDd,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,WAAO,OAAO,QAAQ,WAAW,KAAK,YAAY,MAAM,KAAK,OAAO,IAAI,KAAK,YAAY,MAAM,GAAG;AAAA,EACpG,GAFO;AAAA,EAGP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,QAAI,CAAC,KAAK,kBAAkB;AAC1B,UAAI,KAAK,YAAY,CAAC,WAAW,CAAC,KAAK;AACrC,eAAO,IAAI,SAAS,IAAI;AAAA,MAC1B;AACA,WAAK,mBAAmB,CAAC;AAAA,IAC3B;AACA,SAAK,iBAAiB,cAAc,IAAI;AACxC,WAAO,OAAO,QAAQ,WAAW,KAAK,YAAY,MAAM,KAAK,OAAO,IAAI,KAAK,YAAY,MAAM,GAAG;AAAA,EACpG,GATO;AAAA,EAUP,OAAO,wBAAC,QAAQ,KAAK,YAAY;AAC/B,UAAM,OAAO,KAAK,UAAU,MAAM;AAClC,SAAK,qBAAqB,CAAC;AAC3B,SAAK,iBAAiB,cAAc,IAAI;AACxC,WAAO,OAAO,QAAQ,WAAW,KAAK,YAAY,MAAM,KAAK,OAAO,IAAI,KAAK,YAAY,MAAM,GAAG;AAAA,EACpG,GALO;AAAA,EAMP,OAAO,wBAAC,MAAM,KAAK,YAAY;AAC7B,SAAK,qBAAqB,CAAC;AAC3B,SAAK,iBAAiB,cAAc,IAAI;AACxC,QAAI,OAAO,SAAS,UAAU;AAC5B,UAAI,EAAE,gBAAgB,UAAU;AAC9B,eAAO,KAAK,SAAS;AAAA,MACvB;AACA,UAAI,gBAAgB,SAAS;AAC3B,eAAO,KAAK,KAAK,CAAC,UAAU,gBAAgB,OAAO,yBAAyB,WAAW,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU;AACjH,iBAAO,OAAO,QAAQ,WAAW,KAAK,YAAY,OAAO,KAAK,OAAO,IAAI,KAAK,YAAY,OAAO,GAAG;AAAA,QACtG,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,OAAO,QAAQ,WAAW,KAAK,YAAY,MAAM,KAAK,OAAO,IAAI,KAAK,YAAY,MAAM,GAAG;AAAA,EACpG,GAdO;AAAA,EAeP,WAAW,wBAAC,UAAU,SAAS,QAAQ;AACrC,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,SAAS,IAAI,YAAY,QAAQ;AACtC,WAAO,KAAK,YAAY,MAAM,MAAM;AAAA,EACtC,GAJW;AAAA,EAKX,WAAW,6BAAM;AACf,WAAO,KAAK,gBAAgB,IAAI;AAAA,EAClC,GAFW;AAGb;;;ACzNA,IAAI,UAAU,wBAAC,YAAY,SAAS,eAAe;AACjD,SAAO,CAAC,SAAS,SAAS;AACxB,QAAI,QAAQ;AACZ,WAAO,SAAS,CAAC;AACjB,mBAAe,SAAS,GAAG;AACzB,UAAI,KAAK,OAAO;AACd,cAAM,IAAI,MAAM,8BAA8B;AAAA,MAChD;AACA,cAAQ;AACR,UAAI;AACJ,UAAI,UAAU;AACd,UAAI;AACJ,UAAI,WAAW,CAAC,GAAG;AACjB,kBAAU,WAAW,CAAC,EAAE,CAAC,EAAE,CAAC;AAC5B,YAAI,mBAAmB,SAAS;AAC9B,kBAAQ,IAAI,aAAa;AAAA,QAC3B;AAAA,MACF,OAAO;AACL,kBAAU,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC/C;AACA,UAAI,CAAC,SAAS;AACZ,YAAI,mBAAmB,WAAW,QAAQ,cAAc,SAAS,YAAY;AAC3E,gBAAM,MAAM,WAAW,OAAO;AAAA,QAChC;AAAA,MACF,OAAO;AACL,YAAI;AACF,gBAAM,MAAM,QAAQ,SAAS,MAAM;AACjC,mBAAO,SAAS,IAAI,CAAC;AAAA,UACvB,CAAC;AAAA,QACH,SAAS,KAAK;AACZ,cAAI,eAAe,SAAS,mBAAmB,WAAW,SAAS;AACjE,oBAAQ,QAAQ;AAChB,kBAAM,MAAM,QAAQ,KAAK,OAAO;AAChC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,QAAQ,cAAc,SAAS,UAAU;AACnD,gBAAQ,MAAM;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAvCe;AAAA,EAwCjB;AACF,GA7Cc;;;ACDd,IAAI,gBAAgB,cAAc,MAAM;AAAA,EADxC,OACwC;AAAA;AAAA;AAAA,EACtC;AAAA,EACA;AAAA,EACA,YAAY,SAAS,KAAK,SAAS;AACjC,UAAM,SAAS,SAAS,EAAE,OAAO,SAAS,MAAM,CAAC;AACjD,SAAK,MAAM,SAAS;AACpB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,KAAK;AACZ,aAAO,KAAK;AAAA,IACd;AACA,WAAO,IAAI,SAAS,KAAK,SAAS;AAAA,MAChC,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACF;;;ACfA,IAAI,YAAY,8BAAO,SAAS,UAAU,EAAE,KAAK,MAAM,MAAM;AAC3D,QAAM,UAAU,mBAAmB,cAAc,QAAQ,IAAI,UAAU,QAAQ;AAC/E,QAAM,cAAc,QAAQ,IAAI,cAAc;AAC9C,MAAI,gBAAgB,QAAQ,YAAY,WAAW,qBAAqB,KAAK,gBAAgB,QAAQ,YAAY,WAAW,mCAAmC,GAAG;AAChK,WAAO,cAAc,SAAS,OAAO;AAAA,EACvC;AACA,SAAO,CAAC;AACV,GAPgB;AAQhB,eAAe,cAAc,SAAS,SAAS;AAC7C,QAAM,WAAW,MAAM,QAAQ,SAAS;AACxC,MAAI,UAAU;AACZ,WAAO,0BAA0B,UAAU,OAAO;AAAA,EACpD;AACA,SAAO,CAAC;AACV;AANe;AAOf,SAAS,0BAA0B,UAAU,SAAS;AACpD,QAAM,OAAO,CAAC;AACd,WAAS,QAAQ,CAAC,OAAO,QAAQ;AAC/B,UAAM,uBAAuB,QAAQ,OAAO,IAAI,SAAS,IAAI;AAC7D,QAAI,CAAC,sBAAsB;AACzB,WAAK,GAAG,IAAI;AAAA,IACd,OAAO;AACL,6BAAuB,MAAM,KAAK,KAAK;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAXS;AAYT,IAAI,yBAAyB,wBAAC,MAAM,KAAK,UAAU;AACjD,QAAM,UAAU,KAAK,GAAG;AACxB,MAAI,WAAW,MAAM,QAAQ,OAAO,GAAG;AACrC;AACA,SAAK,GAAG,EAAE,KAAK,KAAK;AAAA,EACtB,WAAW,SAAS;AAClB,SAAK,GAAG,IAAI,CAAC,SAAS,KAAK;AAAA,EAC7B,OAAO;AACL,SAAK,GAAG,IAAI;AAAA,EACd;AACF,GAV6B;;;AC5B7B,IAAI,YAAY,wBAAC,SAAS;AACxB,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,MAAI,MAAM,CAAC,MAAM,IAAI;AACnB,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT,GANgB;AAOhB,IAAI,mBAAmB,wBAAC,cAAc;AACpC,QAAM,EAAE,QAAQ,KAAK,IAAI,sBAAsB,SAAS;AACxD,QAAM,QAAQ,UAAU,IAAI;AAC5B,SAAO,kBAAkB,OAAO,MAAM;AACxC,GAJuB;AAKvB,IAAI,wBAAwB,wBAAC,SAAS;AACpC,QAAM,SAAS,CAAC;AAChB,SAAO,KAAK,QAAQ,cAAc,CAAC,OAAO,UAAU;AAClD,UAAM,OAAO,IAAI,KAAK;AACtB,WAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,WAAO;AAAA,EACT,CAAC;AACD,SAAO,EAAE,QAAQ,KAAK;AACxB,GAR4B;AAS5B,IAAI,oBAAoB,wBAAC,OAAO,WAAW;AACzC,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,UAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,aAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC1C,UAAI,MAAM,CAAC,EAAE,SAAS,IAAI,GAAG;AAC3B,cAAM,CAAC,IAAI,MAAM,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAC9C;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT,GAXwB;AAYxB,IAAI,eAAe,CAAC;AACpB,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,UAAU,KAAK;AACjB,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,MAAM,MAAM,6BAA6B;AACvD,MAAI,OAAO;AACT,QAAI,CAAC,aAAa,KAAK,GAAG;AACxB,UAAI,MAAM,CAAC,GAAG;AACZ,qBAAa,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI,OAAO,MAAM,MAAM,CAAC,IAAI,GAAG,CAAC;AAAA,MAC1E,OAAO;AACL,qBAAa,KAAK,IAAI,CAAC,OAAO,MAAM,CAAC,GAAG,IAAI;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,aAAa,KAAK;AAAA,EAC3B;AACA,SAAO;AACT,GAhBiB;AAiBjB,IAAI,UAAU,wBAAC,YAAY;AACzB,QAAM,MAAM,QAAQ;AACpB,QAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAO,IAAI,MAAM,IAAI,QAAQ,KAAK,CAAC,GAAG,eAAe,KAAK,SAAS,UAAU;AAC/E,GAJc;AAKd,IAAI,kBAAkB,wBAAC,QAAQ;AAC7B,QAAM,aAAa,IAAI,QAAQ,KAAK,CAAC;AACrC,SAAO,eAAe,KAAK,KAAK,MAAM,IAAI,MAAM,aAAa,CAAC;AAChE,GAHsB;AAItB,IAAI,kBAAkB,wBAAC,YAAY;AACjC,QAAM,SAAS,QAAQ,OAAO;AAC9B,SAAO,OAAO,SAAS,KAAK,OAAO,OAAO,SAAS,CAAC,MAAM,MAAM,OAAO,MAAM,GAAG,EAAE,IAAI;AACxF,GAHsB;AAItB,IAAI,YAAY,2BAAI,UAAU;AAC5B,MAAI,IAAI;AACR,MAAI,gBAAgB;AACpB,WAAS,QAAQ,OAAO;AACtB,QAAI,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK;AAC3B,UAAI,EAAE,MAAM,GAAG,EAAE;AACjB,sBAAgB;AAAA,IAClB;AACA,QAAI,KAAK,CAAC,MAAM,KAAK;AACnB,aAAO,IAAI,IAAI;AAAA,IACjB;AACA,QAAI,SAAS,OAAO,eAAe;AACjC,UAAI,GAAG,CAAC;AAAA,IACV,WAAW,SAAS,KAAK;AACvB,UAAI,GAAG,CAAC,GAAG,IAAI;AAAA,IACjB;AACA,QAAI,SAAS,OAAO,MAAM,IAAI;AAC5B,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AACT,GArBgB;AAsBhB,IAAI,yBAAyB,wBAAC,SAAS;AACrC,MAAI,CAAC,KAAK,MAAM,SAAS,GAAG;AAC1B,WAAO;AAAA,EACT;AACA,QAAM,WAAW,KAAK,MAAM,GAAG;AAC/B,QAAM,UAAU,CAAC;AACjB,MAAI,WAAW;AACf,WAAS,QAAQ,CAAC,YAAY;AAC5B,QAAI,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,GAAG;AACzC,kBAAY,MAAM;AAAA,IACpB,WAAW,KAAK,KAAK,OAAO,GAAG;AAC7B,UAAI,KAAK,KAAK,OAAO,GAAG;AACtB,YAAI,QAAQ,WAAW,KAAK,aAAa,IAAI;AAC3C,kBAAQ,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,kBAAQ,KAAK,QAAQ;AAAA,QACvB;AACA,cAAM,kBAAkB,QAAQ,QAAQ,KAAK,EAAE;AAC/C,oBAAY,MAAM;AAClB,gBAAQ,KAAK,QAAQ;AAAA,MACvB,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,QAAQ,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AACvD,GA1B6B;AA2B7B,IAAI,aAAa,wBAAC,UAAU;AAC1B,MAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,QAAQ,GAAG,MAAM,IAAI;AAC7B,YAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,EAClC;AACA,SAAO,IAAI,KAAK,KAAK,IAAI,oBAAoB,KAAK,IAAI;AACxD,GARiB;AASjB,IAAI,iBAAiB,wBAAC,KAAK,KAAK,aAAa;AAC3C,MAAI;AACJ,MAAI,CAAC,YAAY,OAAO,CAAC,OAAO,KAAK,GAAG,GAAG;AACzC,QAAI,YAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AACxC,QAAI,cAAc,IAAI;AACpB,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,CAAC;AAAA,IACtC;AACA,WAAO,cAAc,IAAI;AACvB,YAAM,kBAAkB,IAAI,WAAW,YAAY,IAAI,SAAS,CAAC;AACjE,UAAI,oBAAoB,IAAI;AAC1B,cAAM,aAAa,YAAY,IAAI,SAAS;AAC5C,cAAM,WAAW,IAAI,QAAQ,KAAK,UAAU;AAC5C,eAAO,WAAW,IAAI,MAAM,YAAY,aAAa,KAAK,SAAS,QAAQ,CAAC;AAAA,MAC9E,WAAW,mBAAmB,MAAM,MAAM,eAAe,GAAG;AAC1D,eAAO;AAAA,MACT;AACA,kBAAY,IAAI,QAAQ,IAAI,GAAG,IAAI,YAAY,CAAC;AAAA,IAClD;AACA,cAAU,OAAO,KAAK,GAAG;AACzB,QAAI,CAAC,SAAS;AACZ,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,UAAU,CAAC;AACjB,cAAY,OAAO,KAAK,GAAG;AAC3B,MAAI,WAAW,IAAI,QAAQ,KAAK,CAAC;AACjC,SAAO,aAAa,IAAI;AACtB,UAAM,eAAe,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClD,QAAI,aAAa,IAAI,QAAQ,KAAK,QAAQ;AAC1C,QAAI,aAAa,gBAAgB,iBAAiB,IAAI;AACpD,mBAAa;AAAA,IACf;AACA,QAAI,OAAO,IAAI;AAAA,MACb,WAAW;AAAA,MACX,eAAe,KAAK,iBAAiB,KAAK,SAAS,eAAe;AAAA,IACpE;AACA,QAAI,SAAS;AACX,aAAO,WAAW,IAAI;AAAA,IACxB;AACA,eAAW;AACX,QAAI,SAAS,IAAI;AACf;AAAA,IACF;AACA,QAAI;AACJ,QAAI,eAAe,IAAI;AACrB,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,IAAI,MAAM,aAAa,GAAG,iBAAiB,KAAK,SAAS,YAAY;AAC7E,UAAI,SAAS;AACX,gBAAQ,WAAW,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,UAAU;AACZ,UAAI,EAAE,QAAQ,IAAI,KAAK,MAAM,QAAQ,QAAQ,IAAI,CAAC,IAAI;AACpD,gBAAQ,IAAI,IAAI,CAAC;AAAA,MACnB;AACA;AACA,cAAQ,IAAI,EAAE,KAAK,KAAK;AAAA,IAC1B,OAAO;AACL,cAAQ,IAAI,MAAM;AAAA,IACpB;AAAA,EACF;AACA,SAAO,MAAM,QAAQ,GAAG,IAAI;AAC9B,GA/DqB;AAgErB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB,wBAAC,KAAK,QAAQ;AACjC,SAAO,eAAe,KAAK,KAAK,IAAI;AACtC,GAFqB;AAGrB,IAAI,sBAAsB;;;AC5L1B,IAAI,cAAc,MAAM;AAAA,EAHxB,OAGwB;AAAA;AAAA;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AAAA,EACA,aAAa;AAAA,EACb;AAAA,EACA,YAAY,CAAC;AAAA,EACb,YAAY,SAAS,OAAO,KAAK,cAAc,CAAC,CAAC,CAAC,GAAG;AACnD,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB,CAAC;AAAA,EACzB;AAAA,EACA,MAAM,KAAK;AACT,WAAO,MAAM,KAAK,gBAAgB,GAAG,IAAI,KAAK,oBAAoB;AAAA,EACpE;AAAA,EACA,gBAAgB,KAAK;AACnB,UAAM,WAAW,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG;AAC7D,UAAM,QAAQ,KAAK,cAAc,QAAQ;AACzC,WAAO,QAAQ,KAAK,KAAK,KAAK,IAAI,oBAAoB,KAAK,IAAI,QAAQ;AAAA,EACzE;AAAA,EACA,sBAAsB;AACpB,UAAM,UAAU,CAAC;AACjB,UAAM,OAAO,OAAO,KAAK,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,CAAC;AACjE,eAAW,OAAO,MAAM;AACtB,YAAM,QAAQ,KAAK,cAAc,KAAK,aAAa,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC,EAAE,GAAG,CAAC;AAC9E,UAAI,SAAS,OAAO,UAAU,UAAU;AACtC,gBAAQ,GAAG,IAAI,KAAK,KAAK,KAAK,IAAI,oBAAoB,KAAK,IAAI;AAAA,MACjE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,UAAU;AACtB,WAAO,KAAK,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,EAAE,QAAQ,IAAI;AAAA,EACjE;AAAA,EACA,MAAM,KAAK;AACT,WAAO,cAAc,KAAK,KAAK,GAAG;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,eAAe,KAAK,KAAK,GAAG;AAAA,EACrC;AAAA,EACA,OAAO,MAAM;AACX,QAAI,MAAM;AACR,aAAO,KAAK,IAAI,QAAQ,IAAI,KAAK,YAAY,CAAC,KAAK;AAAA,IACrD;AACA,UAAM,aAAa,CAAC;AACpB,SAAK,IAAI,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACvC,iBAAW,GAAG,IAAI;AAAA,IACpB,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,UAAU,SAAS;AACvB,QAAI,KAAK,UAAU,YAAY;AAC7B,aAAO,KAAK,UAAU;AAAA,IACxB;AACA,UAAM,aAAa,MAAM,UAAU,MAAM,OAAO;AAChD,SAAK,UAAU,aAAa;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,aAAa,wBAAC,QAAQ;AACpB,UAAM,EAAE,WAAW,KAAAA,KAAI,IAAI;AAC3B,UAAM,aAAa,UAAU,GAAG;AAChC,QAAI,YAAY;AACd,aAAO;AAAA,IACT;AACA,QAAI,CAAC,UAAU,GAAG,GAAG;AACnB,iBAAW,kBAAkB,OAAO,KAAK,SAAS,GAAG;AACnD,YAAI,mBAAmB,cAAc;AACnC;AAAA,QACF;AACA,gBAAQ,YAAY;AAClB,cAAI,OAAO,MAAM,UAAU,cAAc;AACzC,cAAI,mBAAmB,QAAQ;AAC7B,mBAAO,KAAK,UAAU,IAAI;AAAA,UAC5B;AACA,iBAAO,MAAM,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAAA,QACvC,GAAG;AAAA,MACL;AAAA,IACF;AACA,WAAO,UAAU,GAAG,IAAIA,KAAI,GAAG,EAAE;AAAA,EACnC,GArBa;AAAA,EAsBb,OAAO;AACL,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B;AAAA,EACA,OAAO;AACL,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B;AAAA,EACA,cAAc;AACZ,WAAO,KAAK,WAAW,aAAa;AAAA,EACtC;AAAA,EACA,OAAO;AACL,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B;AAAA,EACA,WAAW;AACT,WAAO,KAAK,WAAW,UAAU;AAAA,EACnC;AAAA,EACA,iBAAiB,QAAQ,MAAM;AAC7B,SAAK,eAAe,MAAM,IAAI;AAAA,EAChC;AAAA,EACA,MAAM,QAAQ;AACZ,WAAO,KAAK,eAAe,MAAM;AAAA,EACnC;AAAA,EACA,IAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACX,WAAO,KAAK,IAAI;AAAA,EAClB;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAEC,MAAK,CAAC,MAAMA,MAAK;AAAA,EACxD;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,aAAa,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAEA,MAAK,CAAC,MAAMA,MAAK,EAAE,KAAK,UAAU,EAAE;AAAA,EAC3E;AACF;;;ACpHA,IAAI,kBAAkB;AACtB,IAAI,4BAA4B;AAChC,IAAI,UAAU,CAAC,OAAO,QAAQ,OAAO,UAAU,WAAW,OAAO;AACjE,IAAI,mCAAmC;AACvC,IAAI,uBAAuB,cAAc,MAAM;AAAA,EAL/C,OAK+C;AAAA;AAAA;AAC/C;;;ACCA,IAAI,mBAAmB,OAAO,iBAAiB;AAC/C,SAAS,qBAAqB;AAC5B,SAAO,MAAM;AAAA,EACb;AACF;AAHS;AAIT,IAAI,kBAAkB,wBAAC,MAAM;AAC3B,SAAO,EAAE,KAAK,iBAAiB,GAAG;AACpC,GAFsB;AAGtB,IAAI,eAAe,wBAAC,KAAK,MAAM;AAC7B,MAAI,eAAe,eAAe;AAChC,WAAO,IAAI,YAAY;AAAA,EACzB;AACA,UAAQ,MAAM,GAAG;AACjB,SAAO,EAAE,KAAK,yBAAyB,GAAG;AAC5C,GANmB;AAOnB,IAAI,OAAO,cAAc,mBAAmB,EAAE;AAAA,EAtB9C,OAsB8C;AAAA;AAAA;AAAA,EAC5C;AAAA,EACA;AAAA,EACA,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,SAAS,CAAC;AAAA,EACV,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM;AACN,UAAM,aAAa,CAAC,GAAG,SAAS,yBAAyB;AACzD,eAAW,QAAQ,CAAC,WAAW;AAC7B,WAAK,MAAM,IAAI,CAAC,UAAU,SAAS;AACjC,YAAI,OAAO,UAAU,UAAU;AAC7B,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,SAAS,QAAQ,KAAK,OAAO,KAAK;AAAA,QACzC;AACA,aAAK,QAAQ,CAAC,YAAY;AACxB,cAAI,OAAO,YAAY,UAAU;AAC/B,iBAAK,SAAS,QAAQ,KAAK,OAAO,OAAO;AAAA,UAC3C;AAAA,QACF,CAAC;AACD,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,SAAK,KAAK,CAAC,QAAQ,SAAS,aAAa;AACvC,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,iBAAW,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG;AAC7B,aAAK,QAAQ;AACb,mBAAW,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG;AAC/B,mBAAS,IAAI,CAAC,YAAY;AACxB,iBAAK,SAAS,EAAE,YAAY,GAAG,KAAK,OAAO,OAAO;AAAA,UACpD,CAAC;AAAA,QACH;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,SAAK,MAAM,CAAC,SAAS,aAAa;AAChC,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,QAAQ;AAAA,MACf,OAAO;AACL,aAAK,QAAQ;AACb,iBAAS,QAAQ,IAAI;AAAA,MACvB;AACA,eAAS,QAAQ,CAAC,YAAY;AAC5B,aAAK,SAAS,iBAAiB,KAAK,OAAO,OAAO;AAAA,MACpD,CAAC;AACD,aAAO;AAAA,IACT;AACA,UAAM,SAAS,QAAQ,UAAU;AACjC,WAAO,QAAQ;AACf,WAAO,OAAO,MAAM,OAAO;AAC3B,SAAK,UAAU,SAAS,QAAQ,WAAW,UAAU;AAAA,EACvD;AAAA,EACA,QAAQ;AACN,UAAM,QAAQ,IAAI,KAAK;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IAChB,CAAC;AACD,UAAM,SAAS,KAAK;AACpB,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,MAAM,MAAMC,MAAK;AACf,UAAM,SAAS,KAAK,SAAS,IAAI;AACjC,QAAI,CAACA,MAAK;AACR,aAAO;AAAA,IACT;AACA,IAAAA,KAAI,OAAO,IAAI,CAAC,MAAM;AACpB,UAAI;AACJ,UAAIA,KAAI,iBAAiB,cAAc;AACrC,kBAAU,EAAE;AAAA,MACd,OAAO;AACL,kBAAU,8BAAO,GAAG,UAAU,MAAM,QAAQ,CAAC,GAAGA,KAAI,YAAY,EAAE,GAAG,MAAM,EAAE,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAtF;AACV,gBAAQ,gBAAgB,IAAI,EAAE;AAAA,MAChC;AACA,aAAO,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO;AAAA,IAC3C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,SAAS,MAAM;AACb,UAAM,SAAS,KAAK,MAAM;AAC1B,WAAO,YAAY,UAAU,KAAK,WAAW,IAAI;AACjD,WAAO;AAAA,EACT;AAAA,EACA,UAAU,wBAAC,YAAY;AACrB,SAAK,eAAe;AACpB,WAAO;AAAA,EACT,GAHU;AAAA,EAIV,WAAW,wBAAC,YAAY;AACtB,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACT,GAHW;AAAA,EAIX,MAAM,MAAM,oBAAoB,eAAe;AAC7C,UAAM,aAAa,UAAU,KAAK,WAAW,IAAI;AACjD,UAAM,mBAAmB,eAAe,MAAM,IAAI,WAAW;AAC7D,UAAM,UAAU,8BAAO,GAAG,SAAS;AACjC,UAAI,mBAAmB;AACvB,UAAI;AACF,2BAAmB,EAAE;AAAA,MACvB,QAAQ;AAAA,MACR;AACA,YAAM,UAAU,gBAAgB,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,gBAAgB;AAC3E,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAChE,YAAM,eAAe,gBAAgB,EAAE,IAAI,GAAG;AAC9C,YAAM,MAAM,MAAM;AAAA,QAChB,IAAI;AAAA,UACF,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM,gBAAgB,KAAK,OAAO,cAAc,EAAE,IAAI,GAAG;AAAA,UAC7E,EAAE,IAAI;AAAA,QACR;AAAA,QACA,GAAG;AAAA,MACL;AACA,UAAI,KAAK;AACP,eAAO;AAAA,MACT;AACA,YAAM,KAAK;AAAA,IACb,GApBgB;AAqBhB,SAAK,SAAS,iBAAiB,UAAU,MAAM,GAAG,GAAG,OAAO;AAC5D,WAAO;AAAA,EACT;AAAA,EACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,aAAS,OAAO,YAAY;AAC5B,WAAO,UAAU,KAAK,WAAW,IAAI;AACrC,UAAM,IAAI,EAAE,MAAM,QAAQ,QAAQ;AAClC,SAAK,OAAO,IAAI,QAAQ,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1C,SAAK,OAAO,KAAK,CAAC;AAAA,EACpB;AAAA,EACA,WAAW,QAAQ,MAAM;AACvB,WAAO,KAAK,OAAO,MAAM,QAAQ,IAAI;AAAA,EACvC;AAAA,EACA,YAAY,KAAK,GAAG;AAClB,QAAI,eAAe,OAAO;AACxB,aAAO,KAAK,aAAa,KAAK,CAAC;AAAA,IACjC;AACA,UAAM;AAAA,EACR;AAAA,EACA,SAAS,SAAS,cAAc,KAAK,QAAQ;AAC3C,QAAI,WAAW,QAAQ;AACrB,cAAQ,YAAY,IAAI,SAAS,MAAM,MAAM,KAAK,SAAS,SAAS,cAAc,KAAK,KAAK,CAAC,GAAG;AAAA,IAClG;AACA,UAAM,OAAO,KAAK,QAAQ,SAAS,EAAE,IAAI,CAAC;AAC1C,UAAM,cAAc,KAAK,WAAW,QAAQ,IAAI;AAChD,UAAM,IAAI,IAAI,QAAQ,IAAI,YAAY,SAAS,MAAM,WAAW,GAAG;AAAA,MACjE;AAAA,MACA;AAAA,MACA,iBAAiB,KAAK;AAAA,IACxB,CAAC;AACD,QAAI,YAAY,CAAC,EAAE,WAAW,GAAG;AAC/B,UAAI;AACJ,UAAI;AACF,cAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,YAAY;AAC3C,YAAE,MAAM,MAAM,KAAK,gBAAgB,CAAC;AAAA,QACtC,CAAC;AAAA,MACH,SAAS,KAAK;AACZ,eAAO,KAAK,YAAY,KAAK,CAAC;AAAA,MAChC;AACA,aAAO,eAAe,UAAU,IAAI;AAAA,QAClC,CAAC,aAAa,aAAa,EAAE,YAAY,EAAE,MAAM,KAAK,gBAAgB,CAAC;AAAA,MACzE,EAAE,MAAM,CAAC,QAAQ,KAAK,YAAY,KAAK,CAAC,CAAC,IAAI;AAAA,IAC/C;AACA,UAAM,WAAW,QAAQ,YAAY,CAAC,GAAG,KAAK,cAAc,KAAK,eAAe;AAChF,YAAQ,YAAY;AAClB,UAAI;AACF,cAAM,UAAU,MAAM,SAAS,CAAC;AAChC,YAAI,CAAC,QAAQ,WAAW;AACtB,gBAAM,IAAI;AAAA,YACR;AAAA,UACF;AAAA,QACF;AACA,eAAO,QAAQ;AAAA,MACjB,SAAS,KAAK;AACZ,eAAO,KAAK,YAAY,KAAK,CAAC;AAAA,MAChC;AAAA,IACF,GAAG;AAAA,EACL;AAAA,EACA,QAAQ,wBAAC,YAAY,SAAS;AAC5B,WAAO,KAAK,SAAS,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,MAAM;AAAA,EAChE,GAFQ;AAAA,EAGR,UAAU,wBAAC,OAAO,aAAa,KAAK,iBAAiB;AACnD,QAAI,iBAAiB,SAAS;AAC5B,UAAI,gBAAgB,QAAQ;AAC1B,gBAAQ,IAAI,QAAQ,OAAO,WAAW;AAAA,MACxC;AACA,aAAO,KAAK,MAAM,OAAO,KAAK,YAAY;AAAA,IAC5C;AACA,YAAQ,MAAM,SAAS;AACvB,UAAM,OAAO,eAAe,KAAK,KAAK,IAAI,QAAQ,mBAAmB,UAAU,KAAK,KAAK,CAAC;AAC1F,UAAM,MAAM,IAAI,QAAQ,MAAM,WAAW;AACzC,WAAO,KAAK,MAAM,KAAK,KAAK,YAAY;AAAA,EAC1C,GAXU;AAAA,EAYV,OAAO,6BAAM;AACX,qBAAiB,SAAS,CAAC,UAAU;AACnC,YAAM,YAAY,KAAK,SAAS,MAAM,SAAS,OAAO,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAAA,IACrF,CAAC;AAAA,EACH,GAJO;AAKT;;;AC1NA,IAAI,oBAAoB;AACxB,IAAI,4BAA4B;AAChC,IAAI,4BAA4B;AAChC,IAAI,aAAa,OAAO;AACxB,IAAI,kBAAkB,IAAI,IAAI,aAAa;AAC3C,SAAS,WAAW,GAAG,GAAG;AACxB,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO,EAAE,WAAW,IAAI,IAAI,IAAI,KAAK,IAAI;AAAA,EAC3C;AACA,MAAI,EAAE,WAAW,GAAG;AAClB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,6BAA6B,MAAM,2BAA2B;AACtE,WAAO;AAAA,EACT,WAAW,MAAM,6BAA6B,MAAM,2BAA2B;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,MAAM,mBAAmB;AAC3B,WAAO;AAAA,EACT,WAAW,MAAM,mBAAmB;AAClC,WAAO;AAAA,EACT;AACA,SAAO,EAAE,WAAW,EAAE,SAAS,IAAI,IAAI,KAAK,IAAI,EAAE,SAAS,EAAE;AAC/D;AAlBS;AAmBT,IAAI,OAAO,MAAM;AAAA,EAzBjB,OAyBiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA,WAA2B,uBAAO,OAAO,IAAI;AAAA,EAC7C,OAAO,QAAQ,OAAO,UAAU,SAAS,oBAAoB;AAC3D,QAAI,OAAO,WAAW,GAAG;AACvB,UAAI,KAAK,UAAU,QAAQ;AACzB,cAAM;AAAA,MACR;AACA,UAAI,oBAAoB;AACtB;AAAA,MACF;AACA,WAAK,QAAQ;AACb;AAAA,IACF;AACA,UAAM,CAAC,OAAO,GAAG,UAAU,IAAI;AAC/B,UAAM,UAAU,UAAU,MAAM,WAAW,WAAW,IAAI,CAAC,IAAI,IAAI,yBAAyB,IAAI,CAAC,IAAI,IAAI,iBAAiB,IAAI,UAAU,OAAO,CAAC,IAAI,IAAI,yBAAyB,IAAI,MAAM,MAAM,6BAA6B;AAC9N,QAAI;AACJ,QAAI,SAAS;AACX,YAAM,OAAO,QAAQ,CAAC;AACtB,UAAI,YAAY,QAAQ,CAAC,KAAK;AAC9B,UAAI,QAAQ,QAAQ,CAAC,GAAG;AACtB,oBAAY,UAAU,QAAQ,0BAA0B,KAAK;AAC7D,YAAI,YAAY,KAAK,SAAS,GAAG;AAC/B,gBAAM;AAAA,QACR;AAAA,MACF;AACA,aAAO,KAAK,SAAS,SAAS;AAC9B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAAA,UAC7B,CAAC,MAAM,MAAM,6BAA6B,MAAM;AAAA,QAClD,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,SAAS,SAAS,IAAI,IAAI,KAAK;AAC3C,YAAI,SAAS,IAAI;AACf,eAAK,WAAW,QAAQ;AAAA,QAC1B;AAAA,MACF;AACA,UAAI,CAAC,sBAAsB,SAAS,IAAI;AACtC,iBAAS,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC;AAAA,MACrC;AAAA,IACF,OAAO;AACL,aAAO,KAAK,SAAS,KAAK;AAC1B,UAAI,CAAC,MAAM;AACT,YAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAAA,UAC7B,CAAC,MAAM,EAAE,SAAS,KAAK,MAAM,6BAA6B,MAAM;AAAA,QAClE,GAAG;AACD,gBAAM;AAAA,QACR;AACA,YAAI,oBAAoB;AACtB;AAAA,QACF;AACA,eAAO,KAAK,SAAS,KAAK,IAAI,IAAI,KAAK;AAAA,MACzC;AAAA,IACF;AACA,SAAK,OAAO,YAAY,OAAO,UAAU,SAAS,kBAAkB;AAAA,EACtE;AAAA,EACA,iBAAiB;AACf,UAAM,YAAY,OAAO,KAAK,KAAK,QAAQ,EAAE,KAAK,UAAU;AAC5D,UAAM,UAAU,UAAU,IAAI,CAAC,MAAM;AACnC,YAAM,IAAI,KAAK,SAAS,CAAC;AACzB,cAAQ,OAAO,EAAE,aAAa,WAAW,IAAI,CAAC,KAAK,EAAE,QAAQ,KAAK,gBAAgB,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,KAAK,EAAE,eAAe;AAAA,IAC9H,CAAC;AACD,QAAI,OAAO,KAAK,UAAU,UAAU;AAClC,cAAQ,QAAQ,IAAI,KAAK,KAAK,EAAE;AAAA,IAClC;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,aAAO,QAAQ,CAAC;AAAA,IAClB;AACA,WAAO,QAAQ,QAAQ,KAAK,GAAG,IAAI;AAAA,EACrC;AACF;;;ACrGA,IAAI,OAAO,MAAM;AAAA,EAFjB,OAEiB;AAAA;AAAA;AAAA,EACf,UAAU,EAAE,UAAU,EAAE;AAAA,EACxB,OAAO,IAAI,KAAK;AAAA,EAChB,OAAO,MAAM,OAAO,oBAAoB;AACtC,UAAM,aAAa,CAAC;AACpB,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,OAAO;AAClB,UAAI,WAAW;AACf,aAAO,KAAK,QAAQ,cAAc,CAAC,MAAM;AACvC,cAAM,OAAO,MAAM,CAAC;AACpB,eAAO,CAAC,IAAI,CAAC,MAAM,CAAC;AACpB;AACA,mBAAW;AACX,eAAO;AAAA,MACT,CAAC;AACD,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,KAAK,MAAM,0BAA0B,KAAK,CAAC;AAC1D,aAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACvB,eAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,YAAI,OAAO,CAAC,EAAE,QAAQ,IAAI,MAAM,IAAI;AAClC,iBAAO,CAAC,IAAI,OAAO,CAAC,EAAE,QAAQ,MAAM,OAAO,CAAC,EAAE,CAAC,CAAC;AAChD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,SAAK,KAAK,OAAO,QAAQ,OAAO,YAAY,KAAK,SAAS,kBAAkB;AAC5E,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,QAAI,SAAS,KAAK,KAAK,eAAe;AACtC,QAAI,WAAW,IAAI;AACjB,aAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAAA,IACtB;AACA,QAAI,eAAe;AACnB,UAAM,sBAAsB,CAAC;AAC7B,UAAM,sBAAsB,CAAC;AAC7B,aAAS,OAAO,QAAQ,yBAAyB,CAAC,GAAG,cAAc,eAAe;AAChF,UAAI,OAAO,iBAAiB,aAAa;AACvC,4BAAoB,EAAE,YAAY,IAAI,OAAO,YAAY;AACzD,eAAO;AAAA,MACT;AACA,UAAI,OAAO,eAAe,aAAa;AACrC,4BAAoB,OAAO,UAAU,CAAC,IAAI,EAAE;AAC5C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC;AACD,WAAO,CAAC,IAAI,OAAO,IAAI,MAAM,EAAE,GAAG,qBAAqB,mBAAmB;AAAA,EAC5E;AACF;;;AC9CA,IAAI,aAAa,CAAC;AAClB,IAAI,cAAc,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC;AAChE,IAAI,sBAAsC,uBAAO,OAAO,IAAI;AAC5D,SAAS,oBAAoB,MAAM;AACjC,SAAO,oBAAoB,IAAI,MAAM,IAAI;AAAA,IACvC,SAAS,MAAM,KAAK,IAAI,KAAK;AAAA,MAC3B;AAAA,MACA,CAAC,GAAG,aAAa,WAAW,KAAK,QAAQ,KAAK;AAAA,IAChD,CAAC;AAAA,EACH;AACF;AAPS;AAQT,SAAS,2BAA2B;AAClC,wBAAsC,uBAAO,OAAO,IAAI;AAC1D;AAFS;AAGT,SAAS,mCAAmCC,SAAQ;AAClD,QAAM,OAAO,IAAI,KAAK;AACtB,QAAM,cAAc,CAAC;AACrB,MAAIA,QAAO,WAAW,GAAG;AACvB,WAAO;AAAA,EACT;AACA,QAAM,2BAA2BA,QAAO;AAAA,IACtC,CAACC,WAAU,CAAC,CAAC,SAAS,KAAKA,OAAM,CAAC,CAAC,GAAG,GAAGA,MAAK;AAAA,EAChD,EAAE;AAAA,IACA,CAAC,CAAC,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,MAAM,YAAY,IAAI,YAAY,KAAK,MAAM,SAAS,MAAM;AAAA,EACpG;AACA,QAAM,YAA4B,uBAAO,OAAO,IAAI;AACpD,WAAS,IAAI,GAAG,IAAI,IAAI,MAAM,yBAAyB,QAAQ,IAAI,KAAK,KAAK;AAC3E,UAAM,CAAC,oBAAoB,MAAM,QAAQ,IAAI,yBAAyB,CAAC;AACvE,QAAI,oBAAoB;AACtB,gBAAU,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,GAAmB,uBAAO,OAAO,IAAI,CAAC,CAAC,GAAG,UAAU;AAAA,IAChG,OAAO;AACL;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,mBAAa,KAAK,OAAO,MAAM,GAAG,kBAAkB;AAAA,IACtD,SAAS,GAAG;AACV,YAAM,MAAM,aAAa,IAAI,qBAAqB,IAAI,IAAI;AAAA,IAC5D;AACA,QAAI,oBAAoB;AACtB;AAAA,IACF;AACA,gBAAY,CAAC,IAAI,SAAS,IAAI,CAAC,CAAC,GAAG,UAAU,MAAM;AACjD,YAAM,gBAAgC,uBAAO,OAAO,IAAI;AACxD,oBAAc;AACd,aAAO,cAAc,GAAG,cAAc;AACpC,cAAM,CAAC,KAAK,KAAK,IAAI,WAAW,UAAU;AAC1C,sBAAc,GAAG,IAAI;AAAA,MACvB;AACA,aAAO,CAAC,GAAG,aAAa;AAAA,IAC1B,CAAC;AAAA,EACH;AACA,QAAM,CAAC,QAAQ,qBAAqB,mBAAmB,IAAI,KAAK,YAAY;AAC5E,WAAS,IAAI,GAAG,MAAM,YAAY,QAAQ,IAAI,KAAK,KAAK;AACtD,aAAS,IAAI,GAAG,OAAO,YAAY,CAAC,EAAE,QAAQ,IAAI,MAAM,KAAK;AAC3D,YAAM,MAAM,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;AACjC,UAAI,CAAC,KAAK;AACR;AAAA,MACF;AACA,YAAM,OAAO,OAAO,KAAK,GAAG;AAC5B,eAAS,IAAI,GAAG,OAAO,KAAK,QAAQ,IAAI,MAAM,KAAK;AACjD,YAAI,KAAK,CAAC,CAAC,IAAI,oBAAoB,IAAI,KAAK,CAAC,CAAC,CAAC;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,CAAC;AACpB,aAAW,KAAK,qBAAqB;AACnC,eAAW,CAAC,IAAI,YAAY,oBAAoB,CAAC,CAAC;AAAA,EACpD;AACA,SAAO,CAAC,QAAQ,YAAY,SAAS;AACvC;AAxDS;AAyDT,SAAS,eAAe,YAAY,MAAM;AACxC,MAAI,CAAC,YAAY;AACf,WAAO;AAAA,EACT;AACA,aAAW,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG;AAC3E,QAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,GAAG;AACrC,aAAO,CAAC,GAAG,WAAW,CAAC,CAAC;AAAA,IAC1B;AAAA,EACF;AACA,SAAO;AACT;AAVS;AAWT,IAAI,eAAe,MAAM;AAAA,EA3FzB,OA2FyB;AAAA;AAAA;AAAA,EACvB,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA,cAAc;AACZ,SAAK,aAAa,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAC3E,SAAK,SAAS,EAAE,CAAC,eAAe,GAAmB,uBAAO,OAAO,IAAI,EAAE;AAAA,EACzE;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,EAAE,YAAY,QAAAD,QAAO,IAAI;AAC/B,QAAI,CAAC,cAAc,CAACA,SAAQ;AAC1B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,CAAC,WAAW,MAAM,GAAG;AACvB;AACA,OAAC,YAAYA,OAAM,EAAE,QAAQ,CAAC,eAAe;AAC3C,mBAAW,MAAM,IAAoB,uBAAO,OAAO,IAAI;AACvD,eAAO,KAAK,WAAW,eAAe,CAAC,EAAE,QAAQ,CAAC,MAAM;AACtD,qBAAW,MAAM,EAAE,CAAC,IAAI,CAAC,GAAG,WAAW,eAAe,EAAE,CAAC,CAAC;AAAA,QAC5D,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,SAAS,MAAM;AACjB,aAAO;AAAA,IACT;AACA,UAAM,cAAc,KAAK,MAAM,MAAM,KAAK,CAAC,GAAG;AAC9C,QAAI,MAAM,KAAK,IAAI,GAAG;AACpB,YAAM,KAAK,oBAAoB,IAAI;AACnC,UAAI,WAAW,iBAAiB;AAC9B,eAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,qBAAW,CAAC,EAAE,IAAI,MAAM,eAAe,WAAW,CAAC,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,QACvH,CAAC;AAAA,MACH,OAAO;AACL,mBAAW,MAAM,EAAE,IAAI,MAAM,eAAe,WAAW,MAAM,GAAG,IAAI,KAAK,eAAe,WAAW,eAAe,GAAG,IAAI,KAAK,CAAC;AAAA,MACjI;AACA,aAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,MAAM;AACrC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAK,WAAW,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM;AACxC,eAAG,KAAK,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC3D,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AACD,aAAO,KAAKA,OAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,iBAAO,KAAKA,QAAO,CAAC,CAAC,EAAE;AAAA,YACrB,CAAC,MAAM,GAAG,KAAK,CAAC,KAAKA,QAAO,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,SAAS,UAAU,CAAC;AAAA,UAC9D;AAAA,QACF;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,UAAM,QAAQ,uBAAuB,IAAI,KAAK,CAAC,IAAI;AACnD,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,QAAQ,MAAM,CAAC;AACrB,aAAO,KAAKA,OAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,YAAI,WAAW,mBAAmB,WAAW,GAAG;AAC9C,UAAAA,QAAO,CAAC,EAAE,KAAK,MAAM;AAAA,YACnB,GAAG,eAAe,WAAW,CAAC,GAAG,KAAK,KAAK,eAAe,WAAW,eAAe,GAAG,KAAK,KAAK,CAAC;AAAA,UACpG;AACA,UAAAA,QAAO,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,aAAa,MAAM,IAAI,CAAC,CAAC;AAAA,QAC3D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,6BAAyB;AACzB,UAAM,WAAW,KAAK,iBAAiB;AACvC,SAAK,QAAQ,CAAC,SAAS,UAAU;AAC/B,YAAM,UAAU,SAAS,OAAO,KAAK,SAAS,eAAe;AAC7D,YAAM,cAAc,QAAQ,CAAC,EAAE,KAAK;AACpC,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,YAAM,QAAQ,MAAM,MAAM,QAAQ,CAAC,CAAC;AACpC,UAAI,CAAC,OAAO;AACV,eAAO,CAAC,CAAC,GAAG,UAAU;AAAA,MACxB;AACA,YAAM,QAAQ,MAAM,QAAQ,IAAI,CAAC;AACjC,aAAO,CAAC,QAAQ,CAAC,EAAE,KAAK,GAAG,KAAK;AAAA,IAClC;AACA,WAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,EAChC;AAAA,EACA,mBAAmB;AACjB,UAAM,WAA2B,uBAAO,OAAO,IAAI;AACnD,KAAC,GAAG,OAAO,KAAK,KAAK,MAAM,GAAG,GAAG,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE,QAAQ,CAAC,WAAW;AACjF,eAAS,MAAM,MAAM,KAAK,aAAa,MAAM;AAAA,IAC/C,CAAC;AACD,SAAK,aAAa,KAAK,SAAS;AAChC,WAAO;AAAA,EACT;AAAA,EACA,aAAa,QAAQ;AACnB,UAAMA,UAAS,CAAC;AAChB,QAAI,cAAc,WAAW;AAC7B,KAAC,KAAK,YAAY,KAAK,MAAM,EAAE,QAAQ,CAAC,MAAM;AAC5C,YAAM,WAAW,EAAE,MAAM,IAAI,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9F,UAAI,SAAS,WAAW,GAAG;AACzB,wBAAgB;AAChB,QAAAA,QAAO,KAAK,GAAG,QAAQ;AAAA,MACzB,WAAW,WAAW,iBAAiB;AACrC,QAAAA,QAAO;AAAA,UACL,GAAG,OAAO,KAAK,EAAE,eAAe,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,CAAC,aAAa;AAChB,aAAO;AAAA,IACT,OAAO;AACL,aAAO,mCAAmCA,OAAM;AAAA,IAClD;AAAA,EACF;AACF;;;ACvMA,IAAI,cAAc,MAAM;AAAA,EAFxB,OAEwB;AAAA;AAAA;AAAA,EACtB,OAAO;AAAA,EACP,UAAU,CAAC;AAAA,EACX,SAAS,CAAC;AAAA,EACV,YAAY,MAAM;AAChB,WAAO,OAAO,MAAM,IAAI;AAAA,EAC1B;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,SAAK,OAAO,KAAK,CAAC,QAAQ,MAAM,OAAO,CAAC;AAAA,EAC1C;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,KAAK,QAAQ;AAChB,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,UAAM,EAAE,SAAS,QAAAE,QAAO,IAAI;AAC5B,UAAM,MAAM,QAAQ;AACpB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,IAAI,KAAK,KAAK;AACnB,YAAM,SAAS,QAAQ,CAAC;AACxB,UAAI;AACF,QAAAA,QAAO,QAAQ,CAAC,SAAS;AACvB,iBAAO,IAAI,GAAG,IAAI;AAAA,QACpB,CAAC;AACD,cAAM,OAAO,MAAM,QAAQ,IAAI;AAAA,MACjC,SAAS,GAAG;AACV,YAAI,aAAa,sBAAsB;AACrC;AAAA,QACF;AACA,cAAM;AAAA,MACR;AACA,WAAK,QAAQ,OAAO,MAAM,KAAK,MAAM;AACrC,WAAK,UAAU,CAAC,MAAM;AACtB,WAAK,SAAS;AACd;AAAA,IACF;AACA,QAAI,MAAM,KAAK;AACb,YAAM,IAAI,MAAM,aAAa;AAAA,IAC/B;AACA,SAAK,OAAO,iBAAiB,KAAK,aAAa,IAAI;AACnD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,eAAe;AACjB,QAAI,KAAK,UAAU,KAAK,QAAQ,WAAW,GAAG;AAC5C,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC7D;AACA,WAAO,KAAK,QAAQ,CAAC;AAAA,EACvB;AACF;;;AClDA,IAAIC,QAAO,MAAM;AAAA,EAHjB,OAGiB;AAAA;AAAA;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,SAAyB,uBAAO,OAAO,IAAI;AAAA,EAC3C,YAAY,QAAQ,SAAS,UAAU;AACrC,SAAK,WAAW,YAA4B,uBAAO,OAAO,IAAI;AAC9D,SAAK,UAAU,CAAC;AAChB,SAAK,OAAO;AACZ,QAAI,UAAU,SAAS;AACrB,YAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,QAAE,MAAM,IAAI,EAAE,SAAS,cAAc,CAAC,GAAG,OAAO,GAAG,MAAM,KAAK,KAAK;AACnE,WAAK,UAAU,CAAC,CAAC;AAAA,IACnB;AACA,SAAK,WAAW,CAAC;AAAA,EACnB;AAAA,EACA,OAAO,QAAQ,MAAM,SAAS;AAC5B,SAAK,OAAO,GAAG,MAAM,IAAI,IAAI;AAC7B,SAAK,QAAQ,EAAE,KAAK;AACpB,QAAI,UAAU;AACd,UAAM,QAAQ,iBAAiB,IAAI;AACnC,UAAM,eAAe,CAAC;AACtB,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,IAAI,MAAM,CAAC;AACjB,UAAI,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAAS,CAAC,GAAG;AAC7C,kBAAU,QAAQ,SAAS,CAAC;AAC5B,cAAM,WAAW,WAAW,CAAC;AAC7B,YAAI,UAAU;AACZ,uBAAa,KAAK,SAAS,CAAC,CAAC;AAAA,QAC/B;AACA;AAAA,MACF;AACA,cAAQ,SAAS,CAAC,IAAI,IAAIA,MAAK;AAC/B,YAAM,UAAU,WAAW,CAAC;AAC5B,UAAI,SAAS;AACX,gBAAQ,SAAS,KAAK,OAAO;AAC7B,qBAAa,KAAK,QAAQ,CAAC,CAAC;AAAA,MAC9B;AACA,gBAAU,QAAQ,SAAS,CAAC;AAAA,IAC9B;AACA,QAAI,CAAC,QAAQ,QAAQ,QAAQ;AAC3B,cAAQ,UAAU,CAAC;AAAA,IACrB;AACA,UAAM,IAAoB,uBAAO,OAAO,IAAI;AAC5C,UAAM,aAAa;AAAA,MACjB;AAAA,MACA,cAAc,aAAa,OAAO,CAAC,GAAG,GAAG,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;AAAA,MACjE,MAAM,KAAK;AAAA,MACX,OAAO,KAAK;AAAA,IACd;AACA,MAAE,MAAM,IAAI;AACZ,YAAQ,QAAQ,KAAK,CAAC;AACtB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM,QAAQ,YAAY,QAAQ;AACvC,UAAM,cAAc,CAAC;AACrB,aAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,QAAQ,IAAI,KAAK,KAAK;AACvD,YAAM,IAAI,KAAK,QAAQ,CAAC;AACxB,YAAM,aAAa,EAAE,MAAM,KAAK,EAAE,eAAe;AACjD,YAAM,eAA+B,uBAAO,OAAO,IAAI;AACvD,UAAI,eAAe,QAAQ;AACzB,mBAAW,SAAyB,uBAAO,OAAO,IAAI;AACtD,mBAAW,aAAa,QAAQ,CAAC,QAAQ;AACvC,gBAAM,YAAY,aAAa,WAAW,IAAI;AAC9C,qBAAW,OAAO,GAAG,IAAI,OAAO,GAAG,KAAK,CAAC,YAAY,OAAO,GAAG,IAAI,WAAW,GAAG,KAAK,OAAO,GAAG;AAChG,uBAAa,WAAW,IAAI,IAAI;AAAA,QAClC,CAAC;AACD,oBAAY,KAAK,UAAU;AAAA,MAC7B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,QAAQ,MAAM;AACnB,UAAM,cAAc,CAAC;AACrB,SAAK,SAAyB,uBAAO,OAAO,IAAI;AAChD,UAAM,UAAU;AAChB,QAAI,WAAW,CAAC,OAAO;AACvB,UAAM,QAAQ,UAAU,IAAI;AAC5B,aAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAChD,YAAM,OAAO,MAAM,CAAC;AACpB,YAAM,SAAS,MAAM,MAAM;AAC3B,YAAM,YAAY,CAAC;AACnB,eAAS,IAAI,GAAG,OAAO,SAAS,QAAQ,IAAI,MAAM,KAAK;AACrD,cAAM,OAAO,SAAS,CAAC;AACvB,cAAM,WAAW,KAAK,SAAS,IAAI;AACnC,YAAI,UAAU;AACZ,mBAAS,SAAS,KAAK;AACvB,cAAI,WAAW,MAAM;AACnB,gBAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,0BAAY;AAAA,gBACV,GAAG,KAAK,OAAO,SAAS,SAAS,GAAG,GAAG,QAAQ,KAAK,QAAwB,uBAAO,OAAO,IAAI,CAAC;AAAA,cACjG;AAAA,YACF;AACA,wBAAY,KAAK,GAAG,KAAK,OAAO,UAAU,QAAQ,KAAK,QAAwB,uBAAO,OAAO,IAAI,CAAC,CAAC;AAAA,UACrG,OAAO;AACL,sBAAU,KAAK,QAAQ;AAAA,UACzB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,OAAO,KAAK,SAAS,QAAQ,IAAI,MAAM,KAAK;AAC1D,gBAAM,UAAU,KAAK,SAAS,CAAC;AAC/B,gBAAM,SAAS,EAAE,GAAG,KAAK,OAAO;AAChC,cAAI,YAAY,KAAK;AACnB,kBAAM,UAAU,KAAK,SAAS,GAAG;AACjC,gBAAI,SAAS;AACX,0BAAY,KAAK,GAAG,KAAK,OAAO,SAAS,QAAQ,KAAK,QAAwB,uBAAO,OAAO,IAAI,CAAC,CAAC;AAClG,wBAAU,KAAK,OAAO;AAAA,YACxB;AACA;AAAA,UACF;AACA,cAAI,SAAS,IAAI;AACf;AAAA,UACF;AACA,gBAAM,CAAC,KAAK,MAAM,OAAO,IAAI;AAC7B,gBAAM,QAAQ,KAAK,SAAS,GAAG;AAC/B,gBAAM,iBAAiB,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG;AAC9C,cAAI,mBAAmB,UAAU,QAAQ,KAAK,cAAc,GAAG;AAC7D,mBAAO,IAAI,IAAI;AACf,wBAAY,KAAK,GAAG,KAAK,OAAO,OAAO,QAAQ,KAAK,QAAQ,MAAM,CAAC;AACnE;AAAA,UACF;AACA,cAAI,YAAY,QAAQ,mBAAmB,UAAU,QAAQ,KAAK,IAAI,GAAG;AACvE,gBAAI,OAAO,QAAQ,UAAU;AAC3B,qBAAO,IAAI,IAAI;AACf,kBAAI,WAAW,MAAM;AACnB,4BAAY,KAAK,GAAG,KAAK,OAAO,OAAO,QAAQ,QAAQ,KAAK,MAAM,CAAC;AACnE,oBAAI,MAAM,SAAS,GAAG,GAAG;AACvB,8BAAY,KAAK,GAAG,KAAK,OAAO,MAAM,SAAS,GAAG,GAAG,QAAQ,QAAQ,KAAK,MAAM,CAAC;AAAA,gBACnF;AAAA,cACF,OAAO;AACL,sBAAM,SAAS;AACf,0BAAU,KAAK,KAAK;AAAA,cACtB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,iBAAW;AAAA,IACb;AACA,UAAM,UAAU,YAAY,KAAK,CAAC,GAAG,MAAM;AACzC,aAAO,EAAE,QAAQ,EAAE;AAAA,IACrB,CAAC;AACD,WAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,SAAS,OAAO,MAAM,CAAC,SAAS,MAAM,CAAC,CAAC;AAAA,EACjE;AACF;;;ACjJA,IAAI,aAAa,MAAM;AAAA,EAHvB,OAGuB;AAAA;AAAA;AAAA,EACrB,OAAO;AAAA,EACP;AAAA,EACA,cAAc;AACZ,SAAK,OAAO,IAAIC,MAAK;AAAA,EACvB;AAAA,EACA,IAAI,QAAQ,MAAM,SAAS;AACzB,UAAM,UAAU,uBAAuB,IAAI;AAC3C,QAAI,SAAS;AACX,iBAAW,KAAK,SAAS;AACvB,aAAK,KAAK,OAAO,QAAQ,GAAG,OAAO;AAAA,MACrC;AACA;AAAA,IACF;AACA,SAAK,KAAK,OAAO,QAAQ,MAAM,OAAO;AAAA,EACxC;AAAA,EACA,MAAM,QAAQ,MAAM;AAClB,WAAO,KAAK,KAAK,OAAO,QAAQ,IAAI;AAAA,EACtC;AACF;;;ACjBA,IAAIC,QAAO,cAAc,KAAS;AAAA,EALlC,OAKkC;AAAA;AAAA;AAAA,EAChC,YAAY,UAAU,CAAC,GAAG;AACxB,UAAM,OAAO;AACb,SAAK,SAAS,QAAQ,UAAU,IAAI,YAAY;AAAA,MAC9C,SAAS,CAAC,IAAI,aAAa,GAAG,IAAI,WAAW,CAAC;AAAA,IAChD,CAAC;AAAA,EACH;AACF;;;ACXA,IAAI,OAAO,wBAAC,YAAY;AACtB,QAAM,WAAW;AAAA,IACf,QAAQ;AAAA,IACR,cAAc,CAAC,OAAO,QAAQ,OAAO,QAAQ,UAAU,OAAO;AAAA,IAC9D,cAAc,CAAC;AAAA,IACf,eAAe,CAAC;AAAA,EAClB;AACA,QAAM,OAAO;AAAA,IACX,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,mBAAmB,CAAC,eAAe;AACvC,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO,MAAM;AAAA,IACf,WAAW,OAAO,eAAe,YAAY;AAC3C,aAAO;AAAA,IACT,OAAO;AACL,aAAO,CAAC,WAAW,WAAW,SAAS,MAAM,IAAI,SAAS,WAAW,CAAC;AAAA,IACxE;AAAA,EACF,GAAG,KAAK,MAAM;AACd,SAAO,sCAAe,MAAM,GAAG,MAAM;AACnC,aAAS,IAAI,KAAK,OAAO;AACvB,QAAE,IAAI,QAAQ,IAAI,KAAK,KAAK;AAAA,IAC9B;AAFS;AAGT,UAAM,cAAc,gBAAgB,EAAE,IAAI,OAAO,QAAQ,KAAK,IAAI,CAAC;AACnE,QAAI,aAAa;AACf,UAAI,+BAA+B,WAAW;AAAA,IAChD;AACA,QAAI,KAAK,WAAW,KAAK;AACvB,UAAI,QAAQ,QAAQ;AAAA,IACtB;AACA,QAAI,KAAK,aAAa;AACpB,UAAI,oCAAoC,MAAM;AAAA,IAChD;AACA,QAAI,KAAK,eAAe,QAAQ;AAC9B,UAAI,iCAAiC,KAAK,cAAc,KAAK,GAAG,CAAC;AAAA,IACnE;AACA,QAAI,EAAE,IAAI,WAAW,WAAW;AAC9B,UAAI,KAAK,UAAU,MAAM;AACvB,YAAI,0BAA0B,KAAK,OAAO,SAAS,CAAC;AAAA,MACtD;AACA,UAAI,KAAK,cAAc,QAAQ;AAC7B,YAAI,gCAAgC,KAAK,aAAa,KAAK,GAAG,CAAC;AAAA,MACjE;AACA,UAAI,UAAU,KAAK;AACnB,UAAI,CAAC,SAAS,QAAQ;AACpB,cAAM,iBAAiB,EAAE,IAAI,OAAO,gCAAgC;AACpE,YAAI,gBAAgB;AAClB,oBAAU,eAAe,MAAM,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,SAAS,QAAQ;AACnB,YAAI,gCAAgC,QAAQ,KAAK,GAAG,CAAC;AACrD,UAAE,IAAI,QAAQ,OAAO,QAAQ,gCAAgC;AAAA,MAC/D;AACA,QAAE,IAAI,QAAQ,OAAO,gBAAgB;AACrC,QAAE,IAAI,QAAQ,OAAO,cAAc;AACnC,aAAO,IAAI,SAAS,MAAM;AAAA,QACxB,SAAS,EAAE,IAAI;AAAA,QACf,QAAQ;AAAA,QACR,YAAY,EAAE,IAAI;AAAA,MACpB,CAAC;AAAA,IACH;AACA,UAAM,KAAK;AAAA,EACb,GA5CO;AA6CT,GAjEW;;;ACDJ,IAAM,mBAAmB;AAAA;AAAA,EAE/B,KAAK;AAAA,IACJ,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AAAA;AAAA,EAEA,MAAM;AAAA,IACL,uBAAuB;AAAA,IACvB,sBAAsB;AAAA,IACtB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,cAAc;AAAA,IACd,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,aAAa;AAAA,EACd;AACD;AAGO,IAAM,qBAAqB;AAAA,EACjC;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA,IAEzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA,IAGzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA,IAEzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA,IAEzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,IAKzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,IAKzB;AAAA,EACD;AAAA,EACA;AAAA,IACC,OAAO;AAAA,IACP,gBAAgB;AAAA,MACf,EAAE,QAAQ,MAAM,MAAM,EAAE;AAAA;AAAA,IAEzB;AAAA,EACD;AACD;;;AC9GA,IAAM,SAAN,MAAa;AAAA,EAFb,OAEa;AAAA;AAAA;AAAA,EACJ,YAAY;AAAA,EAEZ,QAAQ,WAAW;AAC1B,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,UAAI,WAAW,UAAU,WAAW,CAAC,IAAI,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,MAAM;AAC5F,oBAAc,OAAO,aAAa,QAAQ;AAAA,IAC3C;AACA,WAAO,mBAAmB,UAAU;AAAA,EACrC;AAAA,EAEA,QAAQ,YAAY;AACnB,iBAAa,mBAAmB,UAAU;AAC1C,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,UAAI,WAAW,WAAW,WAAW,CAAC,IAAI,KAAK,UAAU,WAAW,IAAI,KAAK,UAAU,MAAM;AAC7F,mBAAa,OAAO,aAAa,QAAQ;AAAA,IAC1C;AACA,WAAO;AAAA,EACR;AAAA,EAEA,cAAc,MAAW,OAAe;AACvC,UAAM,WAAW,mBAAmB,KAAK,CAAC,WAAW,OAAO,UAAU,KAAK;AAE3E,QAAI,CAAC,SAAU,QAAO;AACtB,QAAI,CAAC,KAAM,QAAO;AAElB,eAAW,SAAS,SAAS,gBAAgB;AAC5C,YAAM,EAAE,QAAQ,KAAK,IAAI;AAGzB,UAAI,KAAK,eAAe,MAAM,GAAG;AAChC,gBAAQ,MAAM;AAAA,UACb,KAAK;AAIJ,gBAAI,OAAO,KAAK,MAAM,MAAM,UAAU;AACrC,mBAAK,MAAM,IAAI,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,YACzC,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD,KAAK;AAIJ,gBAAI,MAAM,QAAQ,KAAK,MAAM,CAAC,GAAG;AAChC,mBAAK,MAAM,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,SAAiB,KAAK,QAAQ,IAAI,CAAC;AAAA,YACrE,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD,KAAK;AAIJ,gBAAI,MAAM,QAAQ,KAAK,MAAM,CAAC,GAAG;AAChC,mBAAK,MAAM,IAAI,KAAK,MAAM,EAAE,IAAI,CAAC,SAAc;AAC9C,oBAAI,KAAK,IAAI;AACZ,uBAAK,KAAK,KAAK,QAAQ,KAAK,EAAE;AAAA,gBAC/B;AACA,uBAAO;AAAA,cACR,CAAC;AAAA,YACF,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD;AACC,kBAAM,IAAI,MAAM,mCAAmC,MAAM,EAAE;AAAA,QAC7D;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,cAAc,MAAW,OAAe;AACvC,UAAM,WAAW,mBAAmB,KAAK,CAAC,WAAW,OAAO,UAAU,KAAK;AAC3E,QAAI,CAAC,SAAU,QAAO;AAEtB,QAAI,OAAO,KAAK,KAAK,MAAM,EAAE,UAAU,GAAG;AACzC,aAAO;AAAA,IACR;AAEA,eAAW,SAAS,SAAS,gBAAgB;AAC5C,YAAM,EAAE,QAAQ,KAAK,IAAI;AAEzB,UAAI,KAAK,OAAO,eAAe,MAAM,GAAG;AACvC,gBAAQ,MAAM;AAAA,UACb,KAAK;AAEJ,gBAAI,OAAO,KAAK,OAAO,MAAM,MAAM,UAAU;AAC5C,mBAAK,OAAO,MAAM,IAAI,KAAK,QAAQ,KAAK,OAAO,MAAM,CAAC;AAAA,YACvD,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD,KAAK;AAEJ,gBAAI,MAAM,QAAQ,KAAK,OAAO,MAAM,CAAC,GAAG;AACvC,mBAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,SAAiB,KAAK,QAAQ,IAAI,CAAC;AAAA,YACnF,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD,KAAK;AAEJ,gBAAI,MAAM,QAAQ,KAAK,OAAO,MAAM,CAAC,GAAG;AACvC,mBAAK,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,EAAE,IAAI,CAAC,SAAc;AAC5D,oBAAI,KAAK,IAAI;AACZ,uBAAK,KAAK,KAAK,QAAQ,KAAK,EAAE;AAAA,gBAC/B;AACA,uBAAO;AAAA,cACR,CAAC;AAAA,YACF,OAAO;AACN,sBAAQ,MAAM,yCAAyC,MAAM,EAAE;AAAA,YAChE;AACA;AAAA,UACD;AACC,kBAAM,IAAI,MAAM,mCAAmC,MAAM,EAAE;AAAA,QAC7D;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AACD;AAEA,IAAO,iBAAQ,IAAI,OAAO;;;AC1H1B,IAAM,MAAN,MAAU;AAAA,EAVV,OAUU;AAAA;AAAA;AAAA,EACD;AAAA,EACA;AAAA,EACA;AAAA,EAEA;AAAA,EAEA;AAAA,EAKR,YAAY,GAAQ;AACnB,UAAM,UAAU,EAAE,IAAI,SAAS;AAC/B,QAAI,WAAW,EAAE,IAAI,UAAU;AAC/B,QAAI,EAAE,IAAI,gBAAiB,YAAW,eAAO,QAAQ,QAAQ;AAC7D,SAAK,kBAAkB,EAAE,IAAI;AAE7B,UAAM,QAAQ,EAAE,IAAI,OAAO;AAE3B,SAAK,YAAY,EAAE,IAAI,WAAW;AAClC,SAAK,QAAQ,EAAE,IAAI,OAAO;AAG1B,QAAI;AACJ,QAAI;AACJ,UAAM,OAAO,EAAE,IAAI,MAAM;AACzB,YAAQ,MAAM;AAAA;AAAA,MAEb,KAAK;AACJ,0BAAkB,EAAE,IAAI;AACxB,yBAAiB;AACjB;AAAA;AAAA,MAED,KAAK;AACJ,0BAAkB,EAAE,IAAI;AACxB,yBAAiB;AACjB;AAAA,MACD;AACC,cAAM,IAAI,MAAM,cAAc;AAAA,IAChC;AAEA,SAAK,WAAW,+BAA+B,cAAc,IAAI,OAAO,GAAG,WAAW,MAAM,WAAW,KAAK;AAE5G,SAAK,UAAU;AAAA,MACd,eAAe,UAAU,eAAe;AAAA,MACxC,gBAAgB;AAAA,IACjB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKQ,aAAa,SAAiB,eAA2B,MAAW,QAA+B;AAC1G,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAc,cAAc,UAAkC;AAC7D,UAAM,OAAO,MAAM,SAAS,KAAK;AAEjC,QAAI,CAAC,CAAC,KAAK,SAAS;AACnB,YAAM,kBAAkB,KAAK,QAAQ,IAAI,CAAC,WAAW;AAEpD,eAAO;AAAA,UACN,IAAI,OAAO;AAAA,UACX,GAAG,OAAO;AAAA,QACX;AAAA,MACD,CAAC;AAGD,aAAO,KAAK,SAAS,EAAE,iBAAiB,QAAQ,KAAK,OAAO,IAAI;AAAA,IACjE,OAAO;AACN,aAAO,EAAE,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO;AAAA,IACtC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAA6B;AAClC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,MACf,CAAC;AACD,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,+BAA+B,SAAS,KAAK,MAAS;AAAA,MAChF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAE9C,UAAI,KAAK,UAAU,QAAW;AAC7B,eAAO,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA,KAAK,kBAAkB,KAAK,gBAAgB,IAAI,CAAC,SAAc,eAAO,cAAc,MAAM,KAAK,SAAS,CAAC,IAAI,KAAK;AAAA,UAClH,KAAK;AAAA,QACN;AAAA,MACD,OAAO;AACN,eAAO,KAAK;AAAA,UACX;AAAA,UACA;AAAA,UACA,KAAK,kBAAkB,KAAK,IAAI,CAAC,SAAc,eAAO,cAAc,MAAM,KAAK,SAAS,CAAC,IAAI;AAAA,QAC9F;AAAA,MACD;AAAA,IACD,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,+BAA+B,OAAO,KAAK,IAAI;AAAA,IACzE;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,QAA+B;AACpC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,MACf,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,iCAAiC,SAAS,KAAK,MAAS;AAAA,MAClF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAC9C,aAAO,KAAK,aAAa,MAAM,KAAK,KAAK,kBAAkB,eAAO,cAAc,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,IAC7G,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,iCAAiC,OAAO,KAAK,IAAI;AAAA,IAC3E;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAA8B;AACnC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,KAAK,KAAK;AAAA,MAChC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,gCAAgC,SAAS,KAAK,MAAS;AAAA,MACjF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAC9C,aAAO,KAAK,aAAa,MAAM,KAAK,KAAK,kBAAkB,eAAO,cAAc,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,IAC7G,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,gCAAgC,OAAO,KAAK,IAAI;AAAA,IAC1E;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,MAA6B;AAClC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,KAAK,KAAK;AAAA,MAChC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,+BAA+B,SAAS,KAAK,MAAS;AAAA,MAChF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAC9C,aAAO,KAAK,aAAa,MAAM,KAAK,KAAK,kBAAkB,eAAO,cAAc,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,IAC7G,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,+BAA+B,OAAO,KAAK,IAAI;AAAA,IACzE;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,MAAM,QAA+B;AACpC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,QACd,MAAM,KAAK,UAAU,KAAK,KAAK;AAAA,MAChC,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,iCAAiC,SAAS,KAAK,MAAS;AAAA,MAClF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAC9C,aAAO,KAAK,aAAa,MAAM,KAAK,KAAK,kBAAkB,eAAO,cAAc,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,IAC7G,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,iCAAiC,OAAO,KAAK,IAAI;AAAA,IAC3E;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,MAAM,SAAgC;AACrC,QAAI;AACH,YAAM,WAAW,MAAM,MAAM,KAAK,UAAU;AAAA,QAC3C,QAAQ;AAAA,QACR,SAAS,KAAK;AAAA,MACf,CAAC;AAED,UAAI,CAAC,SAAS,IAAI;AACjB,cAAM,UAAU,MAAM,SAAS,KAAK;AACpC,eAAO,KAAK,aAAa,kCAAkC,SAAS,KAAK,MAAS;AAAA,MACnF;AAEA,YAAM,OAAO,MAAM,KAAK,cAAc,QAAQ;AAC9C,aAAO,KAAK,aAAa,MAAM,KAAK,KAAK,kBAAkB,eAAO,cAAc,MAAM,KAAK,SAAS,IAAI,IAAI;AAAA,IAC7G,SAAS,OAAO;AACf,aAAO,KAAK,aAAa,kCAAkC,OAAO,KAAK,IAAI;AAAA,IAC5E;AAAA,EACD;AACD;AAEA,IAAO,cAAQ;;;AClQf,IAAM,QAAQ,IAAIC,MAAK;AAGvB,MAAM,IAAI,KAAK,OAAO,MAAM;AAC3B,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,eAAe,MAAM,OAAO,IAAI,MAAM,IAAI,IAAI;AAC/D,WAAO,EAAE,KAAK,EAAE,MAAM,SAAS,OAAO,GAAG,aAAa;AAAA,EACvD,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,yBAAyB,GAAG,GAAG;AAAA,EACrE;AACD,CAAC;AAGD,MAAM,IAAI,QAAQ,OAAO,MAAM;AAC9B,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,eAAe,KAAK,IAAI,MAAM,IAAI,MAAM;AACzD,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,4BAA4B,GAAG,GAAG;AAAA,EACxE;AACD,CAAC;AAGD,MAAM,KAAK,KAAK,OAAO,MAAM;AAC5B,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,eAAe,KAAK,IAAI,MAAM,IAAI,KAAK;AACxD,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,0BAA0B,GAAG,GAAG;AAAA,EACtE;AACD,CAAC;AAGD,MAAM,IAAI,QAAQ,OAAO,MAAM;AAC9B,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,MAAM,cAAc,IAAI,MAAM,IAAI,IAAI;AACvD,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,yBAAyB,GAAG,GAAG;AAAA,EACrE;AACD,CAAC;AAED,MAAM,MAAM,QAAQ,OAAO,MAAM;AAChC,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,MAAM,cAAc,IAAI,MAAM,IAAI,MAAM;AACzD,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,8BAA8B,GAAG,GAAG;AAAA,EAC1E;AACD,CAAC;AAED,MAAM,MAAM,KAAK,OAAO,MAAM;AAC7B,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,MAAM,cAAc,IAAI,MAAM,IAAI,MAAM;AACzD,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,2BAA2B,GAAG,GAAG;AAAA,EACvE;AACD,CAAC;AAGD,MAAM,OAAO,QAAQ,OAAO,MAAM;AACjC,QAAM,MAAM,IAAI,YAAI,CAAC;AAErB,MAAI;AACH,UAAM,EAAE,SAAS,eAAe,KAAK,IAAI,MAAM,IAAI,OAAO;AAC1D,WAAO,EAAE,KAAK,EAAE,MAAM,QAAQ,GAAG,aAAa;AAAA,EAC/C,SAAS,OAAO;AACf,WAAO,EAAE,KAAK,EAAE,MAAM,MAAM,SAAS,+BAA+B,GAAG,GAAG;AAAA,EAC3E;AACD,CAAC;;;AClFD,IAAM,aAAa,8BAAO,GAAG,SAAS;AACrC,QAAM,MAAM,IAAI,IAAI,EAAE,IAAI,GAAG;AAC7B,QAAM,OAAO,IAAI,SAAS,MAAM,GAAG,EAAE,CAAC;AACtC,QAAM,QAAQ,IAAI,SAAS,MAAM,GAAG,EAAE,CAAC;AAGvC,IAAE,IAAI,QAAQ,IAAI;AAClB,IAAE,IAAI,WAAW,iBAAiB,IAAI,EAAE,KAAK,CAAC;AAC9C,IAAE,IAAI,aAAa,KAAK;AAExB,IAAE,IAAI,YAAY,IAAI,SAAS,MAAM,GAAG,EAAE,CAAC,CAAC;AAC5C,IAAE,IAAI,SAAS,IAAI,UAAU,EAAE;AAE/B,QAAM,SAAS,EAAE,IAAI;AACrB,MAAI,WAAW,UAAU,WAAW,SAAS,WAAW,SAAS;AAChE,UAAM,OAAO,MAAM,EAAE,IAAI,KAAK;AAC9B,QAAI;AACJ,QAAI,EAAE,IAAI,iBAAiB;AAC1B,cAAQ,eAAO,cAAc,MAAM,KAAK;AAAA,IACzC,OAAO;AACN,cAAQ;AAAA,IACT;AAEA,MAAE,IAAI,SAAS,KAAK;AAAA,EACrB;AACA,QAAM,KAAK;AACZ,GA1BmB;;;ACFnB,IAAI,QAAQ,wBAAC,YAAY;AACvB,MAAI,CAAC,WAAW,QAAQ;AACtB,YAAQ,IAAI,gEAAgE;AAC5E,WAAO,OAAO,IAAI,SAAS,MAAM,KAAK;AAAA,EACxC;AACA,MAAI,QAAQ,SAAS,QAAQ;AAC3B,YAAQ,OAAO;AAAA,EACjB;AACA,QAAM,yBAAyB,QAAQ,cAAc,MAAM,GAAG,EAAE,IAAI,CAAC,cAAc,UAAU,YAAY,CAAC;AAC1G,QAAM,iBAAiB,MAAM,QAAQ,QAAQ,IAAI,IAAI,QAAQ,OAAO,QAAQ,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,cAAc,UAAU,KAAK,CAAC;AAChI,MAAI,QAAQ,MAAM,SAAS,GAAG,GAAG;AAC/B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,QAAM,YAAY,wBAAC,MAAM;AACvB,QAAI,wBAAwB;AAC1B,YAAM,qBAAqB,EAAE,IAAI,QAAQ,IAAI,eAAe,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;AACpH,iBAAW,aAAa,wBAAwB;AAC9C,YAAI,CAAC,MAAM,KAAK,IAAI,UAAU,KAAK,EAAE,MAAM,KAAK,CAAC;AACjD,eAAO,KAAK,YAAY;AACxB,YAAI,CAAC,mBAAmB,SAAS,IAAI,GAAG;AACtC,YAAE,OAAO,iBAAiB,GAAG,IAAI,GAAG,QAAQ,IAAI,KAAK,KAAK,EAAE,IAAI,EAAE,QAAQ,KAAK,CAAC;AAAA,QAClF;AAAA,MACF;AAAA,IACF;AACA,QAAI,gBAAgB;AAClB,YAAM,qBAAqB,EAAE,IAAI,QAAQ,IAAI,MAAM,GAAG,MAAM,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC;AAC1F,YAAM,OAAO,MAAM;AAAA,QACjB,IAAI;AAAA,UACF,CAAC,GAAG,oBAAoB,GAAG,cAAc,EAAE,IAAI,CAAC,cAAc,UAAU,YAAY,CAAC;AAAA,QACvF;AAAA,MACF,EAAE,KAAK;AACP,UAAI,KAAK,SAAS,GAAG,GAAG;AACtB,UAAE,OAAO,QAAQ,GAAG;AAAA,MACtB,OAAO;AACL,UAAE,OAAO,QAAQ,KAAK,KAAK,IAAI,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF,GAxBkB;AAyBlB,SAAO,sCAAe,OAAO,GAAG,MAAM;AACpC,QAAI,MAAM,EAAE,IAAI;AAChB,QAAI,QAAQ,cAAc;AACxB,YAAM,MAAM,QAAQ,aAAa,CAAC;AAAA,IACpC;AACA,UAAM,YAAY,OAAO,QAAQ,cAAc,aAAa,MAAM,QAAQ,UAAU,CAAC,IAAI,QAAQ;AACjG,UAAM,SAAS,MAAM,OAAO,KAAK,SAAS;AAC1C,UAAM,WAAW,MAAM,OAAO,MAAM,GAAG;AACvC,QAAI,UAAU;AACZ,aAAO,IAAI,SAAS,SAAS,MAAM,QAAQ;AAAA,IAC7C;AACA,UAAM,KAAK;AACX,QAAI,CAAC,EAAE,IAAI,IAAI;AACb;AAAA,IACF;AACA,cAAU,CAAC;AACX,UAAM,MAAM,EAAE,IAAI,MAAM;AACxB,QAAI,QAAQ,MAAM;AAChB,YAAM,OAAO,IAAI,KAAK,GAAG;AAAA,IAC3B,OAAO;AACL,QAAE,aAAa,UAAU,OAAO,IAAI,KAAK,GAAG,CAAC;AAAA,IAC/C;AAAA,EACF,GAtBO;AAuBT,GA/DY;;;ACAZ,SAAS,kBAAkB;AACzB,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAM,YAAY,OAAO,YAAY,cAAc,cAAc,SAAS,MAAM,OAAO,MAAM,YAAY,YAAY,KAAK,UAAU;AACpI,SAAO,CAAC;AACV;AAJS;;;ACET,IAAI,WAAW,wBAAC,UAAU;AACxB,QAAM,CAAC,WAAW,SAAS,IAAI,CAAC,KAAK,GAAG;AACxC,QAAM,aAAa,MAAM,IAAI,CAAC,MAAM,EAAE,QAAQ,4BAA4B,OAAO,SAAS,CAAC;AAC3F,SAAO,WAAW,KAAK,SAAS;AAClC,GAJe;AAKf,IAAI,OAAO,wBAAC,UAAU;AACpB,QAAM,QAAQ,KAAK,IAAI,IAAI;AAC3B,SAAO,SAAS,CAAC,QAAQ,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC;AAC9E,GAHW;AAIX,IAAI,cAAc,wBAAC,WAAW;AAC5B,QAAM,eAAe,gBAAgB;AACrC,QAAM,MAAM;AAAA,IACV,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,IACxD,GAAG,eAAe,WAAW,MAAM,YAAY,GAAG,MAAM;AAAA,EAC1D;AACA,QAAM,kBAAkB,SAAS,MAAM;AACvC,SAAO,IAAI,eAAe;AAC5B,GAbkB;AAclB,SAAS,IAAI,IAAI,QAAQ,QAAQ,MAAM,SAAS,GAAG,SAAS;AAC1D,QAAM,MAAM,WAAW,QAAuB,KAAK,MAAM,IAAI,MAAM,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,MAAM,IAAI,IAAI,IAAI,YAAY,MAAM,CAAC,IAAI,OAAO;AAC/I,KAAG,GAAG;AACR;AAHS;AAIT,IAAI,SAAS,wBAAC,KAAK,QAAQ,QAAQ;AACjC,SAAO,sCAAe,QAAQ,GAAG,MAAM;AACrC,UAAM,EAAE,OAAO,IAAI,EAAE;AACrB,UAAM,OAAO,QAAQ,EAAE,IAAI,GAAG;AAC9B,QAAI,IAAI,OAAsB,QAAQ,IAAI;AAC1C,UAAM,QAAQ,KAAK,IAAI;AACvB,UAAM,KAAK;AACX,QAAI,IAAI,OAAsB,QAAQ,MAAM,EAAE,IAAI,QAAQ,KAAK,KAAK,CAAC;AAAA,EACvE,GAPO;AAQT,GATa;;;ACvBb,IAAM,MAAM,IAAIC,MAAK;AACrB,IAAM,UAAU,CAAC,yBAAyB,sBAAsB;AAGhE,IAAM,gBAAgB;AAEtB,IAAM,aAAa;AAAA,EAClB,QAAQ,wBAAC,WAAmB;AAC3B,WAAO,QAAQ,SAAS,MAAM,KAAK,cAAc,KAAK,MAAM,IAAI,SAAS;AAAA,EAC1E,GAFQ;AAAA,EAGR,cAAc,CAAC,mBAAmB,6BAA6B,gBAAgB,UAAU,cAAc;AAAA,EACvG,cAAc,CAAC,QAAQ,OAAO,OAAO,UAAU,OAAO;AAAA,EACtD,eAAe,CAAC,kBAAkB,mBAAmB,QAAQ;AAAA,EAC7D,QAAQ;AAAA,EACR,aAAa;AACd;AAEA,IAAI,IAAI,KAAK,KAAK,UAAU,CAAC;AAC7B,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,KAAK,2CAA2C,CAAC;AACvE,IAAI,IAAI,gBAAgB,CAAC,MAAM,EAAE,KAAK,4BAA4B,CAAC;AAEnE,IAAI,IAAI,UAAU,OAAO,GAAG,SAAS,MAAM,WAAW,GAAG,IAAI,CAAC;AAC9D,IAAI,IAAI,WAAW,OAAO,GAAG,SAAS,MAAM,WAAW,GAAG,IAAI,CAAC;AAE/D,IAAM,cAAc;AAAA,EACnB,WAAW;AAAA,EACX,cAAc;AAAA;AACf;AAEA,IAAM,SAAS;AAAA,EACd,EAAE,MAAM,uBAAuB,OAAO,KAAK;AAAA,EAC3C,EAAE,MAAM,sBAAsB,OAAO,KAAK;AAAA,EAC1C,EAAE,MAAM,YAAY,OAAO,KAAK;AAAA,EAChC,EAAE,MAAM,YAAY,OAAO,KAAK;AAAA,EAChC,EAAE,MAAM,gBAAgB,OAAO,MAAM;AAAA;AAAA,EACrC,EAAE,MAAM,YAAY,OAAO,MAAM;AAAA;AAAA,EACjC,EAAE,MAAM,UAAU,OAAO,MAAM;AAAA,EAC/B,EAAE,MAAM,eAAe,OAAO,MAAM;AAAA,EACpC,EAAE,MAAM,eAAe,OAAO,KAAK;AACpC;AAEA,OAAO,QAAQ,CAACC,WAAU;AAEzB,MAAIA,OAAM,OAAO;AAChB,QAAI,IAAI,QAAQA,OAAM,IAAI,IAAI,MAAM,WAAW,CAAC;AAChD,QAAI,IAAI,SAASA,OAAM,IAAI,IAAI,MAAM,WAAW,CAAC;AAAA,EAClD;AACA,MAAI,MAAM,QAAQA,OAAM,IAAI,IAAI,KAAM;AACtC,MAAI,MAAM,SAASA,OAAM,IAAI,IAAI,KAAM;AACxC,CAAC;AAED,IAAO,cAAQ;;;ACzDf,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,UAAE;AACD,QAAI;AACH,UAAI,QAAQ,SAAS,QAAQ,CAAC,QAAQ,UAAU;AAC/C,cAAM,SAAS,QAAQ,KAAK,UAAU;AACtC,eAAO,EAAE,MAAM,OAAO,KAAK,GAAG,MAAM;AAAA,QAAC;AAAA,MACtC;AAAA,IACD,SAAS,GAAG;AACX,cAAQ,MAAM,4CAA4C,CAAC;AAAA,IAC5D;AAAA,EACD;AACD,GAb8B;AAe9B,IAAO,6CAAQ;;;ACRf,SAAS,YAAY,GAAmB;AACvC,SAAO;AAAA,IACN,MAAM,GAAG;AAAA,IACT,SAAS,GAAG,WAAW,OAAO,CAAC;AAAA,IAC/B,OAAO,GAAG;AAAA,IACV,OAAO,GAAG,UAAU,SAAY,SAAY,YAAY,EAAE,KAAK;AAAA,EAChE;AACD;AAPS;AAUT,IAAM,YAAwB,8BAAO,SAAS,KAAK,MAAM,kBAAkB;AAC1E,MAAI;AACH,WAAO,MAAM,cAAc,KAAK,SAAS,GAAG;AAAA,EAC7C,SAAS,GAAQ;AAChB,UAAM,QAAQ,YAAY,CAAC;AAC3B,WAAO,SAAS,KAAK,OAAO;AAAA,MAC3B,QAAQ;AAAA,MACR,SAAS,EAAE,+BAA+B,OAAO;AAAA,IAClD,CAAC;AAAA,EACF;AACD,GAV8B;AAY9B,IAAO,2CAAQ;;;ACzBJ,IAAM,mCAAmC;AAAA,EAE9B;AAAA,EAAyB;AAC3C;AACA,IAAO,sCAAQ;;;ACcnB,IAAM,wBAAsC,CAAC;AAKtC,SAAS,uBAAuB,MAAqC;AAC3E,wBAAsB,KAAK,GAAG,KAAK,KAAK,CAAC;AAC1C;AAFgB;AAShB,SAAS,uBACR,SACA,KACA,KACA,UACA,iBACsB;AACtB,QAAM,CAAC,MAAM,GAAG,IAAI,IAAI;AACxB,QAAM,gBAAmC;AAAA,IACxC;AAAA,IACA,KAAK,YAAY,QAAQ;AACxB,aAAO,uBAAuB,YAAY,QAAQ,KAAK,UAAU,IAAI;AAAA,IACtE;AAAA,EACD;AACA,SAAO,KAAK,SAAS,KAAK,KAAK,aAAa;AAC7C;AAfS;AAiBF,SAAS,kBACf,SACA,KACA,KACA,UACA,iBACsB;AACtB,SAAO,uBAAuB,SAAS,KAAK,KAAK,UAAU;AAAA,IAC1D,GAAG;AAAA,IACH;AAAA,EACD,CAAC;AACF;AAXgB;;;AC3ChB,IAAM,iCAAN,MAAM,gCAA8D;AAAA,EAGnE,YACU,eACA,MACT,SACC;AAHQ;AACA;AAGT,SAAK,WAAW;AAAA,EACjB;AAAA,EArBD,OAYoE;AAAA;AAAA;AAAA,EAC1D;AAAA,EAUT,UAAU;AACT,QAAI,EAAE,gBAAgB,kCAAiC;AACtD,YAAM,IAAI,UAAU,oBAAoB;AAAA,IACzC;AAEA,SAAK,SAAS;AAAA,EACf;AACD;AAEA,SAAS,oBAAoB,QAA0C;AAEtE,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAEA,QAAM,kBAA+C,gCACpD,SACA,KACA,KACC;AACD,QAAI,OAAO,UAAU,QAAW;AAC/B,YAAM,IAAI,MAAM,6CAA6C;AAAA,IAC9D;AACA,WAAO,OAAO,MAAM,SAAS,KAAK,GAAG;AAAA,EACtC,GATqD;AAWrD,SAAO;AAAA,IACN,GAAG;AAAA,IACH,MAAM,SAAS,KAAK,KAAK;AACxB,YAAM,aAAyB,gCAAU,MAAM,MAAM;AACpD,YAAI,SAAS,eAAe,OAAO,cAAc,QAAW;AAC3D,gBAAM,aAAa,IAAI;AAAA,YACtB,KAAK,IAAI;AAAA,YACT,KAAK,QAAQ;AAAA,YACb,MAAM;AAAA,YAAC;AAAA,UACR;AACA,iBAAO,OAAO,UAAU,YAAY,KAAK,GAAG;AAAA,QAC7C;AAAA,MACD,GAT+B;AAU/B,aAAO,kBAAkB,SAAS,KAAK,KAAK,YAAY,eAAe;AAAA,IACxE;AAAA,EACD;AACD;AAxCS;AA0CT,SAAS,qBACR,OAC8B;AAE9B,MACC,qCAAqC,UACrC,iCAAiC,WAAW,GAC3C;AACD,WAAO;AAAA,EACR;AAEA,aAAW,cAAc,kCAAkC;AAC1D,wBAAoB,UAAU;AAAA,EAC/B;AAGA,SAAO,cAAc,MAAM;AAAA,IAC1B,mBAAyE,wBACxE,SACA,KACA,QACI;AACJ,WAAK,MAAM;AACX,WAAK,MAAM;AACX,UAAI,MAAM,UAAU,QAAW;AAC9B,cAAM,IAAI,MAAM,sDAAsD;AAAA,MACvE;AACA,aAAO,MAAM,MAAM,OAAO;AAAA,IAC3B,GAXyE;AAAA,IAazE,cAA0B,wBAAC,MAAM,SAAS;AACzC,UAAI,SAAS,eAAe,MAAM,cAAc,QAAW;AAC1D,cAAM,aAAa,IAAI;AAAA,UACtB,KAAK,IAAI;AAAA,UACT,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UAAC;AAAA,QACR;AACA,eAAO,MAAM,UAAU,UAAU;AAAA,MAClC;AAAA,IACD,GAT0B;AAAA,IAW1B,MAAM,SAAwD;AAC7D,aAAO;AAAA,QACN;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACN;AAAA,IACD;AAAA,EACD;AACD;AAnDS;AAqDT,IAAI;AACJ,IAAI,OAAO,wCAAU,UAAU;AAC9B,kBAAgB,oBAAoB,mCAAK;AAC1C,WAAW,OAAO,wCAAU,YAAY;AACvC,kBAAgB,qBAAqB,mCAAK;AAC3C;AACA,IAAO,kCAAQ;", "names": ["raw", "route", "app", "routes", "route", "routes", "Node", "Node", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "route"]}