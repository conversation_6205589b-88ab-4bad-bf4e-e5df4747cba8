// src/modules/judge/types.ts

export type JudgeData = {
  email: string;
  // assigned_teams: {
  //   team_id: string;
  //   team_name: string;
  //   project_name: string;
  // }[];
  assigned_projects_id: string[];

  evaluated_projects_id: string[];
  name: string;
  company: string;
  judge_field: string;
  salutation: string;
  position: string;
  job_field: string;
  phone_number: string;
  photo: string;
};

export type Rating = {
  [key: string]: number;
};

export type EvaluationData = {
  id?: string;
  judge_id?: string[];
  judge_salutation?: string;
  judge_name?: string;
  project_id?: string[];
  project_name?: string;
  project_major?: string;
  sdg?: string;
  comments: string;
  

  problem_statement: number;
  solution: number;
  innovativeness: number;
  impact_benefit: number;
  practicality: number;
  content_clarity: number;
  presentation: number;
  innovation_creativity: number;
  entrepreneurship: number;

  created_at?: string;
  udpated_at?: string;
};