// Main function
function updateFYPStudentsSheet(IS_PRODUCTION) {
  /**
   *
   * @desc
   * This function updates based on IS_PRODUCTION value.
   * If no IS_PRODUCTION value found, use PUBLIC_PIXEL_IS_PRODUCTION value.
   * 
   * It fetches the data from airtable,
   * based on the stat from IS_PRODUCTION or PUBLIC_PIXEL_IS_PRODUCTION,
   * and updates the fyp_students sheet in PIXEL 2025 Website Data
   * 
   * @param
   * IS_PRODUCTION = true or false
   * used by google sheet action buttons
   * 
   */
  const stat = (IS_PRODUCTION ?? PUBLIC_PIXEL_IS_PRODUCTION) ? "prod" : "dev";
  const baseUrl = `${PUBLIC_PIXEL_BACKEND_ENDPOINT}${stat}/participants`;

  Logger.log(`Start overwriting cells with airtable ${stat} data ...`);

  // Get the sheet
  const sheetName = 'fyp_students';
  const sheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName(sheetName);

  // Define the headers you want to show in the sheet
  const headers = [
    'id',               // string etc. "recR2B6gLMgXrVZAB"
    'student_name',     // string etc. "Lim <PERSON> Hao"
    'display_name',     // string etc. "Wen Hao"
    'matric_no',        // string etc. "123456"
    'student_major',    // string - "Intelligent Computing" / "Software Engineering" / "Computing Infrastructure"
    'ic',               // ic / passport string etc. "010203040506"
    'desc',             // string etc. "Hi, I'm Farzana"
    'photo',            // string etc. "https://campusonline2.usm.my/photo/StudentPic/010203040506.jpg"
    'student_email',    // string etc. "<EMAIL>"
    'personal_email',   // string etc. "<EMAIL>"
    'github',           // string etc. "https://github.com/WenHao1223"
    'linkedin',         // string etc. "https://www.linkedin.com/in/wenhao1223/"
    'portfolio',        // string etc. "https://wenhao1223.github.io/WenHao1223" / null
    'tel',              // string etc. "01158606808"
    'discord_id',       // string etc. "wenhao1223"
    'created_at',       // string - ISO DateTime etc. "2024-06-18T08:55:36.000Z"
    'resume',           // string etc. "https://drive.google.com/file/d/19ESorR9tpp8FZ338UcP93OVTCwOdwTqm/view?usp=drivesdk"
    'resume_applied',   // array etc. "url1;url2"
    'job_ids_applied',  // array etc. "1;2"
    'project_id',       // array[0] - string
    'project_name',     // array[0] - string
    'project_major',    // array[0] - string
    'sdg',              // array[0] - string
  ];

  // Fields that contain array values, but only the first item should be extracted.
  // These typically represent single selections (e.g. project name, project major).
  const extractFirstOnlyFields = [
    'project_id',
    'project_name',
    'project_major',
    'sdg'
  ];

  // Map sheet header to actual field name in the API
  const fieldMapping = {
    'job_ids_applied': 'job_application_id',
  };

  // Clear data rows, but keep header row
  clearSheetDataOnly(sheetName);

  // 🌀 Fetch all paginated data
  let allParticipants = [];
  let offset = null;

  do {
    const url = offset ? `${baseUrl}?offset=${encodeURIComponent(offset)}` : baseUrl;
    const response = UrlFetchApp.fetch(url);
    const json = JSON.parse(response.getContentText());

    if (json.data && Array.isArray(json.data)) {
      allParticipants = allParticipants.concat(json.data);
    }

    offset = json.offset || null;
  } while (offset);

  Logger.log(`✅ Total participants fetched: ${allParticipants.length}`);

  // Start writing from row 2
  let rowIndex = 2;
  allParticipants.forEach(participant => {
    // Only include participants from the target year
    if (participant.year !== PUBLIC_PIXEL_YEAR) return;

    const row = headers.map(header => {
      const actualField = fieldMapping[header] || header;
      const value = participant[actualField];

      // If the field is an array, extract either:
      // - the first element (for specific fields), or
      // - all elements joined with a semicolon
      if (Array.isArray(value)) {
        if (extractFirstOnlyFields.includes(actualField)) {
          return value[0] || '';
        } else {
          return value.join(';');
        }
      }

      return value || '';
    });

    sheet.getRange(rowIndex, 1, 1, row.length).setValues([row]);
    rowIndex++;
  });

  Logger.log("📤 FYP students data updated without overwriting headers.");
}
