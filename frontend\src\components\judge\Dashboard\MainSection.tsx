import { useEffect, useState, useContext } from "react";
import { judgeFactory } from "@/modules";
import type { Judge, Project } from "@/modules";
import { JudgeContext } from "./Context";
import { widget } from "@utils/widget";
import ProjectEvaluationCard from "./ProjectEvaluationCard";
import { LoadingSkeleton as ProjectLoadingSkeleton } from "./ProjectEvaluationCard";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

export const MainSection = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [projects, setProjects] = useState<Project[]>([]);
  const judge = useContext(JudgeContext) as Judge;

  const numberProjectsEvaluated =
    judge.evaluated_projects_year &&
    Array.isArray(judge.evaluated_projects_year)
      ? judge.evaluated_projects_year.filter(
          (year: string) => year === currentYear.toString()
        ).length
      : 0;

  const numberProjectsAssigned = judge.assigned_projects_id
    ? judge.assigned_projects_year.filter(
        (year: string) => year === currentYear.toString()
      ).length
    : 0;

  useEffect(() => {
    if (!judge.assigned_projects_id) {
      return;
    }

    (async () => {
      try {
        let assignedProjects = await judgeFactory().getAssignedProjects(
          judge.firebase_uid,
          currentYear
        );
        // console.log("Assigned Projects:", assignedProjects);

        // show the unevaluated projects on top of the list
        const unevaluatedProjects = assignedProjects.filter(
          (project) => !judge.evaluated_projects_id.includes(project.id)
        );
        const evaluatedProjects = assignedProjects.filter((project) =>
          judge.evaluated_projects_id.includes(project.id)
        );

        const sortedProjects = unevaluatedProjects.concat(evaluatedProjects);

        setProjects(sortedProjects);

        setIsLoading(false);
      } catch (error) {
        widget.alertError("Error", "Failed to fetch assigned projects", error);
      }
    })();
  }, [judge.assigned_projects_id]);

  return (
    <section className="bg-white flex flex-col w-full p-5 relative overflow-x-hidedn  overflow-y-auto h-full">
      <div className="space-y-2 ">
        <div className="flex max-sm:flex-col mb-4">
          <div className="flex flex-grow gap-3 ">
            <div className="bg-blue-300 w-4 rounded-md" />
            <p className="font-semibold">Project Evaluation</p>
          </div>

          {/* legend */}
          <div
            id="info-evaluation"
            className="flex gap-4 max-sm:mt-4 max-sm:justify-center"
          >
            <div className="flex items-center">
              <span className="bg-yellow-200 font-medium mr-2 px-2.5 py-0.5 rounded-full text-center">
                <i className="fa-solid fa-gavel"></i>
              </span>
              <p className="text-xs md:text-sm">- Pending</p>
            </div>
            <div className="flex items-center">
              <span className="bg-green-200 font-medium mr-2 px-2.5 py-0.5 rounded-full text-center whitespace-nowrap">
                <i className="fa-solid fa-circle-check"></i>
              </span>
              <p className="text-xs md:text-sm whitespace-nowrap">
                - Evaluated
              </p>
            </div>
          </div>
        </div>

        <div className="daisy-alert flex max-lg:flex-col px-7 py-7 justify-between drop-shadow-lg bg-white text-black border-0 w-full">
          <div className="border-none flex gap-3 items-center">
            <i className="fa-solid fa-circle-info"></i>
            <span className="text-left">
              To evaluate a project, click on the project card. Also, you can
              update your evaluation anytime before the deadline.
            </span>
          </div>
        </div>

        {/* evaluation progress bar */}
        <div className="mt-4 flex flex-col" id="evaluate-progressbar">
          <p className="whitespace-nowrap font-medium">Evaluation Status</p>
          <div className="flex gap-4 items-center">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-blue-500 h-2.5 rounded-full"
                style={{
                  width:
                    numberProjectsAssigned !== 0
                      ? (numberProjectsEvaluated / numberProjectsAssigned) *
                          100 +
                        "%"
                      : "0%",
                }}
              ></div>
            </div>
            <p className="font-bold">
              {numberProjectsEvaluated}/{numberProjectsAssigned}
            </p>
          </div>
        </div>

        {isLoading ? (
          <ProjectLoadingSkeleton />
        ) : numberProjectsAssigned === 0 ? (
          <p className="w-full bg-[#213f4a] text-[#fafafa] px-5 py-2">
            No projects assigned yet
          </p>
        ) : (
          <div className="flex flex-col !mt-4 gap-3">
            {projects?.map((project, i) => (
              <ProjectEvaluationCard key={i} project={project} />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};
