# PIXEL USM

## Important Links
### Official Website
- [PIXEL >= 2024 Official Website](https://pixelusm.com/)
- [PIXEL 2024 Website](http://pixel2024.pixel2024.pages.dev/)
- [PIXEL 2023 Official Website](https://pixel.usm.my/)

### Planning
#### Setup & Architecture
- [PIXEL 2024 Technical Sheet](https://docs.google.com/spreadsheets/d/1biJM2QN8aG8RA2O1qGEyKTYd81xIBSmH/edit)
- [PIXEL 2024 Conceptual View](https://app.eraser.io/workspace/********************)
#### Figma
- [PIXEL 2024 Figma](https://www.figma.com/design/z2sqiSWxQndQAbwdufWRqs/PIXEL-2024-Official-Website)
#### Documentation
- [PIXEL 2024 Documentation](https://whimsical-magic-402.notion.site/PIXEL-2024-Documentation-ddaa3077a8964b9c890dc7b99890d292)
- [PIXEL 2023 Documentation](https://cssdocumentations.pages.dev/PIXEL2023/)
#### Guideline
- [PIXEL 2024 Participants' Submission Guideline](https://scribehow.com/shared/Participants_Submission_Guidelines__ckx_ZYQpQiyHJSxVPzuqcA)
- [PIXEL 2024 Judge Dashboard Guideline](https://scribehow.com/shared/Judges_Evaluation_Guidelines__pdgRW5L_SWWTDZ3YTke9Ew)

### Database
#### Firebase
- [PIXEL 2024 Firebase (Production)](https://console.firebase.google.com/u/0/project/pixel-2024-13d72)
- [PIXEL 2024 Firebase (Development)](https://console.firebase.google.com/u/0/project/test-pixel-2024-b287c)
#### Airtable
- [PIXEL 2024 Airtable (Production)](https://airtable.com/appWRc72X48HlS5JZ)
- [PIXEL 2024 Airtable (Development)](https://airtable.com/appX3SKijXuWC2zpD)
- [PIXEL 2024 Airtable Interface](https://airtable.com/appWRc72X48HlS5JZ/pagEHaITjQ1KZJxpH)
- [PIXEL 2024 Airtable API Documentation](https://airtable.com/appCiE1tok3WBkUOq/api/docs)
- [PIXEL 2023 Airtable (Production)](https://airtable.com/app1NmHQvAOsIxcm4)
- [Airtable API Encoder](https://codepen.io/airtable/full/MeXqOg)

### Cloud Technologies
#### Google Cloud
- [PIXEL 2024 Google Cloud (Production)](https://console.cloud.google.com/welcome?hl=en&project=pixel-2024-13d72)
- [PIXEL 2024 Google Cloud (Development)](https://console.cloud.google.com/welcome?hl=en&project=test-pixel-2024-424517)
#### reCAPTCHA
- [PIXEL 2024 Google reCAPTCHA (Production)](https://www.google.com/u/?/recaptcha/admin/site/703223480)
- [PIXEL 2024 Google reCAPTCHA (Development)](https://www.google.com/u/?/recaptcha/admin/site/703222609)


### Backend
- [PIXEL 2024 Worker API (Production)](https://pixelpandaapi.pixelusm.com/)

## How to Run?

### Run Google Apps Script

#### Install tools
```bash
npm install -g @google/clasp
```

#### Test installation
```bash
clasp -v
```

#### Authenticate clasp
```bash
clasp login
```

#### Clone Apps Script
(Git push should automatically done this step)
```bash
cd appsScripts
clasp clone "<Google Apps Script ID>" --rootDir scripts
```
> **Note:** You can check the \<Google Apps Script ID\> under _Project Settings_ in Apps Script. For PIXEL 2025, project ID is `1ztKvrAld7h9st42dgc1sdiqM688lFHERfAwzb-nP834SqqaUXhRf0MTj`.

#### Pull code
```bash
cd appsScripts
clasp pull
```
> **Note:** Do this every time Apps Script is updated to keep the latest version.

#### Push code
```bash
cd appsScripts
clasp push
```
> **Note:** Remember to push to git after pushing the code to Apps Script.
> **Note:** Make sure you have enabled the Apps Script API to avoid this error:<br>⚠️ *User has not enabled the Apps Script API. Enable it by visiting https://script.google.com/home/<USER>

### Run backend code
```bash
cd backend & wrangler dev
```
> **Note:** Install wrangler in your local machine first -> [Install Wrangler](https://developers.cloudflare.com/workers/wrangler/install-and-update/)

### Publish backend code
```bash
cd backend && npm run deploy
```
> ⚠️ **Warning:** DO NOT PUBLISH ANY BACKEND CODE WITHOUT ACKNOLWEDGEMENT IN GROUP

### Use emulator
```bash
cd frontend && firebase emulators:start
```

### Create an `.env` file at path `./frontend`

```
# Crypto-JS
PUBLIC_ENCRYPTION_KEY = pixel2024usm
PUBLIC_PROJECT_KEY = 4

# block page
PUBLIC_BLOCKPATH = /careers
PUBLIC_BLOCKMODE = true

# team min and max limit for each major
# 1 means solo
PUBLIC_SE_TEAM_MAX_LIMIT = 1
PUBLIC_SE_TEAM_MIN_LIMIT = 1
PUBLIC_IC_TEAM_MAX_LIMIT = 1
PUBLIC_IC_TEAM_MIN_LIMIT = 1
PUBLIC_CI_TEAM_MAX_LIMIT = 1
PUBLIC_CI_TEAM_MIN_LIMIT = 1

# general
PUBLIC_PIXEL_YEAR = 2025
PUBLIC_PIXEL_NEED_RECAPTCHA = false # false to skip recaptcha login during development
PUBLIC_PIXEL_PASSWORD = boss # password for official_launch=false
PUBLIC_PIXEL_VERSION = 1.1.0 # update the major and minor version when there is a major change (will clear session storage), update the patch version when there is a minor change

# app script web app url for resume.gs
PUBLIC_GDRIVE_URL = https://script.google.com/macros/s/AKfycbxqgTL4uw7HjzzngxR99hlA25rB2rTATnujyCKDrwy79saDE1ZVqrQ1QiE0939ryNNt/exec

# deadlines
PUBLIC_JUDGING_DEADLINE = 2025-06-27T23:59:59
PUBLIC_SUBMISSION_DEADLINE = 2025-06-23T01:00:00

# --------------------------------------------------------------------- #

## DEVELOPMENT
# ReCAPTCHA v2
PUBLIC_RECAPTCHA_SITEKEY = 6LdRU-opAAAAAANKnPtebjhF-ghhG9mTJ3Btq7eF
PUBLIC_RECAPTCHA_KEY = 6LdRU-opAAAAAMivX0x-g2EFTpXmF4HwAIompTyf

# firebase
PUBLIC_FIREBASE_APIKEY = AIzaSyC9-CzAlGeGa8Wx9SyI1LSMHQeSD6OC3Eo
PUBLIC_FIREBASE_AUTHDOMAIN = test-pixel-2024-b287c.firebaseapp.com
PUBLIC_FIREBASE_PROJECTID = test-pixel-2024-b287c
PUBLIC_FIREBASE_STORAGEBUCKET = test-pixel-2024-b287c.appspot.com
PUBLIC_FIREBASE_MESSAGINGSENDERID = 833217124720
PUBLIC_FIREBASE_APPID = 1:833217124720:web:fd4110a78a44f53938d7b5
PUBLIC_FIREBASE_MEASUREMENTID = G-VXRBW6Z9EJ

# app script web app url for resume.gs
PUBLIC_GDRIVE_RESUME_FOLDER = 1Fkg9ew0Ns8hosonJFPXfpOmNtX9F6-2P

# phase
PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH = true  # (show 2023 winners + sponsors + open registration + project submission)
PUBLIC_PIXEL_IS_REGISTRATION = true  # (true on official launch, false on submission closed)
PUBLIC_PIXEL_IS_JUDGING = false # (true on judging starts, false on judging ends)
PUBLIC_PIXEL_IS_POST_SUBMISSION = false # (true on 19 June 2024, false on post event)
PUBLIC_PIXEL_IS_POST_EVENT = false # (true on post event)
PUBLIC_PIXEL_IS_PRODUCTION = false # false (use TEST-PIXEL-2024 airtable, firebase and prod resume folder), true (use PIXEL-2024 airtable, firebase and resume folder)

# --------------------------------------------------------------------- #

# ## PRODUCTION
# # ReCAPTCHA v2
# PUBLIC_RECAPTCHA_SITEKEY = 6Le4VuopAAAAAOxAHDhVfzYdUJBRDZrutE-SNLmQ
# PUBLIC_RECAPTCHA_KEY = 6Le4VuopAAAAAIN5A8qMsPhLSo8lGSxwCe6QRxw5

# # firebase
# PUBLIC_FIREBASE_APIKEY = AIzaSyD0petcPuMqEe-2JGUJAJTn0LxO19XCINc
# PUBLIC_FIREBASE_AUTHDOMAIN = pixel-2024-13d72.firebaseapp.com
# PUBLIC_FIREBASE_PROJECTID = pixel-2024-13d72
# PUBLIC_FIREBASE_STORAGEBUCKET = pixel-2024-13d72.appspot.com
# PUBLIC_FIREBASE_MESSAGINGSENDERID = 676776472540
# PUBLIC_FIREBASE_APPID = 1:676776472540:web:4e3148b1ce4c8f0f3ea919
# PUBLIC_FIREBASE_MEASUREMENTID = G-31P60RJC7J

# # app script web app url for resume.gs
# PUBLIC_GDRIVE_RESUME_FOLDER = 2024_2025/PIXEL2025/resume

# # phase
# PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH = true
# PUBLIC_PIXEL_IS_REGISTRATION = true
# PUBLIC_PIXEL_IS_JUDGING = false
# PUBLIC_PIXEL_IS_POST_SUBMISSION = false
# PUBLIC_PIXEL_IS_POST_EVENT = false
# PUBLIC_PIXEL_IS_PRODUCTION = true
```

### Update `wrangler.toml` at path `./backend`

```
#:schema node_modules/wrangler/config-schema.json
name = "laughingpixelpanda"
main = "src/index.ts"
compatibility_date = "2024-04-19"
compatibility_flags = ["nodejs_compat"]

[vars]
# PIXEL2023 Airtable
# PROD_AIRTABLETOKEN = **********************************************************************************

# TEST-PIXEL-2024 Airtable (CSTECH)
DEV_AIRTABLETOKEN = "**********************************************************************************"

# TEST-PIXEL-2024 (CSSOCIETY ACC)
# DEV_AIRTABLETOKEN = "**********************************************************************************"

# PIXEL-2024 (CSSOCIETY ACC)
PROD_AIRTABLETOKEN = "**********************************************************************************"
```

### Notes on .env file

- `SECRET_BLOCKPATH` includes the pages path may hide from reaching by users. Multiple pages can separate by comma (`,`)
- `PUBLIC_BLOCKMDOE`has value of `true` or `false` to redirect pages in `SECRET_BLOCKPATH` to `/404`
- `PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH` has values of `true` or `false`
- `PUBLIC_PIXEL_IS_JUDGING` has values of `true` or `false` to toggle judging view on submission page
- `PUBLIC_PIXEL_IS_REGISTRATION` has values of `true` or `false` because registration and preliminary happen at the same time

### Git commits semantic

- `feat`: new feature
- `fix`: bug fix
- `docs`: changes to documentation
- `style`: formatting, missing semi colons, etc.; no code change
- `refactor`: refactoring production code
- `wip`: work in progress

### To test backend APIs,

```bash
curl "https://api.airtable.com/v0/appX3SKijXuWC2zpD/participants" -H "Authorization: Bearer **********************************************************************************"
```
