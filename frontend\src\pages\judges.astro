---
import SideLayout from "@layouts/sideLayout.astro";

import { proxyApi } from "@/utils";
import Filter from "@components/global/Filter";
import JudgesCardList from "@components/judges/JudgeCardList";

export const prerender = true;

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const data = await proxyApi.get(
  "judges",
  {
    fields: "major, salutation, name, position, organization, photo",
    sort: "major:asc,name:asc",
    filterByFormula: `FIND("${currentYear}", ARRAYJOIN(year))`,
  },
  prerender
);

const filters = [
  { filterName: "All", shortName: "All" },
  { filterName: "Software Engineering", shortName: "SE" },
  { filterName: "Intelligent Computing", shortName: "IC" },
  { filterName: "Computing Infrastructure", shortName: "CI" },
];

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "Judges", path: "/judges" },
];
---

<SideLayout
  title="Judges"
  pageDesc="The elite panel of industry experts ready to evaluate submitted projects."
  breadcrumbs={breadcrumbs}
>
  <div
    class="flex max-md:flex-wrap max-md:gap-y-2 gap-x-1 *:leading-tight md:*:leading-none *:align-text-bottom justify-center items-center *:text-center [&>button]:font-medium [&>button]:text-mobile-14 [&>button]:py-1 [&>button]:px-2 [&>button]:rounded-[4px]"
    slot="menu"
  >
    <Filter filters={filters} client:load />
  </div>
  <!-- Gallery -->
  <section
    class="mt-16 lg:mt-24 flex flex-wrap md:grid md:grid-cols-4 xl:grid-cols-5 gap-x-4 gap-y-10 sm:gap-x-8 sm:gap-y-16 md:gap-x-8 md:gap-y-16 lg:gap-x-16 lg:gap-y-20 xl:gap-x-16 xl:gap-y-24 w-full justify-between sm:justify-around lg:justify-items-center xl:justify-items-stretch"
  >
    <JudgesCardList client:load data={data} />
  </section>
</SideLayout>
