// Main function
function updateEvaluationsSheet(IS_PRODUCTION) {
  const stat = (IS_PRODUCTION ?? PUBLIC_PIXEL_IS_PRODUCTION) ? "prod" : "dev";
  const baseUrl = `${PUBLIC_PIXEL_BACKEND_ENDPOINT}${stat}/evaluations`;
  const sheetName = 'evaluations';

  Logger.log(`Fetching evaluation data from ${baseUrl} ...`);

  const sheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName(sheetName);

  // Clear all but header
  clearSheetDataOnly(sheetName);

  Logger.log("Writing data to sheet and calculating marks ...");

  // Define the headers to show in the sheet
  const headers = [
    'evaluation_id',          // int etc. 228
    'judge_id',               // array[0] etc. "recWw746PwdKMvlr1",
    'judge_salutation',       // array[0] etc. "Mr."
    'judge_name',             // array[0] etc. "Karam Qub<PERSON>"
    'project_id',             // array[0] - string etc. "recrOYwjuOk1GGEV4"
    'project_name',           // array[0] - string etc. "Oral Healthcare for Special Needs Children's Portal"
    'project_major',          // array[0] - string
    'sdg',                    // array[0] - string
    'team_members',           // derived from /projects/<project_id>
    'comments',               // string
    'problem_statement',      // int - 1 to 10
    'solution',               // int - 1 to 35
    'innovativeness',         // int - 1 to 10
    'impact_benefit',         // int - 1 to 10
    'practicality',           // int - 1 to 5
    'content_clarity',        // int - 1 to 10
    'presentation',           // int - 1 to 5
    'innovation_creativity',  // int - 1 to 5
    'entrepreneurship',       // int - 1 to 10
    'mark',                   // computed
    'created_at',             // ISO string
    'updated_at'              // ISO string
  ];

  // Fields that contain array values, but only the first item should be extracted.
  // These typically represent single selections (e.g. project name, project major).
  const extractFirstOnlyFields = [
    'judge_id',
    'judge_salutation',
    'judge_name',
    'project_id',
    'project_name',
    'project_major',
    'sdg'
  ];

  // List of evaluation criteria used to calculate the total 'mark' score.
  // Each criterion is rated individually and summed to produce a final score
  const criteriaFields = [
    'problem_statement',
    'solution',
    'innovativeness',
    'impact_benefit',
    'practicality',
    'content_clarity',
    'presentation',
    'innovation_creativity',
    'entrepreneurship'
  ];

  // ✅ Pre-fetch all project data to avoid multiple API calls per row
  const projectMap = getAllProjectsMap(stat);

  // 🌀 Fetch all evaluation records via pagination
  let allEvaluations = [];
  let offset = null;

  do {
    const url = offset ? `${baseUrl}?offset=${encodeURIComponent(offset)}` : baseUrl;
    const response = UrlFetchApp.fetch(url);
    const json = JSON.parse(response.getContentText());

    if (json.data && Array.isArray(json.data)) {
      allEvaluations = allEvaluations.concat(json.data);
    }

    offset = json.offset || null;
  } while (offset);

  Logger.log(`✅ Total evaluations fetched: ${allEvaluations.length}`);

  // Write data row by row
  let rowIndex = 2;
  allEvaluations.forEach(evaluation => {
    const row = headers.map(header => {
      const value = evaluation[header];

      // Calculate total mark by summing all criterion fields
      if (header === 'mark') {
        return criteriaFields.reduce((sum, field) => {
          const val = evaluation[field];
          return sum + (typeof val === 'number' ? val : 0);
        }, 0);
      }

      // To derive the team_members column in the format:
      // name1,matric1,email1;name2,matric2,email2;...
      // Fetch from preloaded projectMap by project_id
      if (header === 'team_members') {
        const projectIdArray = evaluation['project_id'];
        const projectId = Array.isArray(projectIdArray) ? projectIdArray[0] : '';
        return projectMap[projectId] || '';
      }

      // If the field is an array, extract either:
      // - the first element (for specific fields), or
      // - all elements joined with a semicolon
      if (Array.isArray(value)) {
        if (extractFirstOnlyFields.includes(header)) {
          return value[0] || '';
        } else {
          return value.join(';');
        }
      }

      return value ?? ''; // handle null or undefined
    });

    sheet.getRange(rowIndex, 1, 1, row.length).setValues([row]);
    rowIndex++;
  });

  Logger.log("✅ Evaluations data and marks written successfully.");
}

/**
 * Fetches all projects (paginated) and builds a projectId → team_members string map.
 *
 * @param {string} stat - "prod" | "dev"
 * @returns {Object} Map of project_id → "name,matric,email;..."
 */
function getAllProjectsMap(stat) {
  const baseUrl = `${PUBLIC_PIXEL_BACKEND_ENDPOINT}${stat}/projects`;
  const projectMap = {};
  let offset = null;

  Logger.log(`Fetching project data from ${baseUrl} ...`)

  try {
    do {
      // Build the full URL with offset if it exists
      const url = offset ? `${baseUrl}?offset=${encodeURIComponent(offset)}` : baseUrl;

      const response = UrlFetchApp.fetch(url);
      const json = JSON.parse(response.getContentText());

      // Loop through projects and format team_members
      json.data.forEach(project => {
        // Only include projects from the specified year (e.g., "2024")
        // Skip any project that doesn't match PUBLIC_PIXEL_YEAR
        if (project.year !== PUBLIC_PIXEL_YEAR) return;

        const names = project.members_name || [];
        const matrics = project.members_matric_no || [];
        const emails = project.members_email || [];

        const members = names.map((name, i) => {
          const matric = matrics[i] || '';
          const email = emails[i] || '';
          return `${name},${matric},${email}`;
        });

        projectMap[project.id] = members.join(';');
      });

      // If there's an offset, continue fetching
      offset = json.offset;

    } while (offset);

    Logger.log("✅ Project data done fetching.");

    return projectMap;
  } catch (err) {
    Logger.log(`❌ Failed to fetch all paginated projects: ${err}`);
    return {};
  }
}