---
import AuthLayout from "@layouts/AuthLayout.astro";
import SignUpComponent from "@components/auth/SignUp";

const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";

if (!isRegistration)
  return Astro.redirect("/sign-in")
---

<AuthLayout title="Sign Up">
  <SignUpComponent client:only="react" />
</AuthLayout>

<style is:inline>
  /* for input */
  .input-container {
    position: relative;
  }

  .input-container span {
    position: absolute;
    z-index: 0;
    left: 0;
    opacity: 0.5;
    transition: 0.2s ease;
  }

  .input-container input:valid ~ span,
  .input-container input:focus ~ span,
  .input-container input:-webkit-autofill ~ span {
    opacity: 0.8;
    font-size: 12px;
    transform: translateY(-20px);
  }

  textarea {
    border: none;
    overflow: auto;
    outline: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;

    resize: none;
  }

  textarea:focus {
    --tw-ring-offset-shadow: none;
  }

  select {
    background-color: #efefef;
  }

  input[type="text"],
  input[type="password"] {
    border: none;
    box-shadow: none;
    outline: none;
    transition: 0.2s ease-in-out;
    position: relative;
    z-index: 1;
    background: transparent;
    border-bottom: 2px solid #0000003b;
  }

  input[type="text"]:focus,
  input[type="password"]:focus {
    outline: none;
    border-bottom: 2px solid #EFC15B;
    --tw-ring-shadow: 0 0 #000 !important;
    --tw-ring-offset-shadow: 0 0 #000 !important;
  }

  /* input autofill */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    transition: background-color 5000s ease-in-out 0s;
    -webkit-text-fill-color: black !important;
    -webkit-animation-name: autofill;
    -webkit-animation-fill-mode: both;
    -webkit-animation-delay: 1s;
    caret-color: #fff;
  }

  @keyframes autofill {
    to {
      color: black;
    }
  }
</style>