import { useState, useEffect, useRef } from "react";
import {
  Project,
  ParticipantSession,
  projectFactory,
  teamFactory,
} from "@/modules";
import {
  actionSessionStorage,
  compressedImage,
  widget,
  proxyApi,
  firebaseStorage,
  mapSdg,
} from "@/utils";
import { SelectAutoComplete } from "./SelectAutoComplete";
import ReactDOM from "react-dom/client";
import { CustomInput, CustomLabel, CustomFileInput } from "./Inputs";
import RegulationCheckbox from "./RegulationCheckbox";
import Loading from "@components/general/Loading";
import * as Yup from "yup";
import ProjectNavbar from "./ProjectNavbar";
import ReactMarkdown from "react-markdown";
import DraftCheckbox from "./DraftCheckbox";

// TODO: the thumbnails will dissapear when the user upload the slide

const TextInfo = ({ color, children }: any) => {
  return (
    <p
      className={`${color} text-white w-full text-center my-2 text-sm py-1 px-2 md:text-base`}
    >
      {children}
    </p>
  );
};

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isOfficialLaunch =
  import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";

function ProjectSubmissionForm({ pageMode, project_id }: any) {
  const slideRef = useRef<HTMLInputElement>(null);

  const [user, setUser] = useState({} as ParticipantSession); // user data from session storage
  const [project, setProject] = useState({} as any);
  const [team, setTeam] = useState({} as any);
  const [projectName, setProjectName] = useState("");
  const [supervisors, setSupervisors] = useState([] as any); // supervisors data from airtable
  const [supervisorsName, setSupervisorsName] = useState([] as any);
  const [slideFileName, setSlideFileName] = useState("");

  const [descTextCount, setDescTextCount] = useState(0);
  const [titleCharCount, setTitleCharCount] = useState(0);
  const [loading, setLoading] = useState(pageMode === "edit" ? true : false);

  const [submitAction, setSubmitAction] = useState<"submit" | "draft">(
    "submit"
  );

  if (!isOfficialLaunch) {
    widget.alertError(
      "PIXEL is not officially launched yet",
      "Please wait for the official launch."
    );
    setTimeout(() => {
      window.location.href = "/sign-in";
    }, 3000);
  }

  // if (!isRegistration) {
  //   widget.alertError(
  //     "Sorry",
  //     "Project submission is closed. Please contact us if you have any questions.."
  //   );
  //   setTimeout(() => {
  //     window.location.href = "/participant";
  //   }, 3000);
  // }

  async function fetchProjectData() {
    try {
      const projectDoc = (await projectFactory().getProject(
        project_id
      )) as Project;

      // console.log("projectDoc", projectDoc);

      // check if user airtable id in projectDoc member id
      const userData = await actionSessionStorage().get("userData");
      // console.log(userData);
      try {
        await setUser(userData);
      } catch (error) {
        await widget.alertError("Error", "User data not found: " + error);
      }

      if (userData.year != currentYear) {
        await widget.alertError(
          "ERROR",
          "You are not allowed to edit or update this project"
        );
        window.location.href = "/participant";
      }

      if (
        !projectDoc?.team?.members_id?.includes(userData.id) &&
        !projectDoc?.isDraft
      ) {
        await widget.alertError(
          "ERROR",
          "You are not allowed to access this project"
        );
        window.location.href = "/participant";
      }

      const projectRecord = await proxyApi.first("projects", project_id);

      setProject({
        ...projectDoc,
        // ...projectRecord,
        thumbnail: projectDoc.thumbnail,
        highlights: projectDoc.highlights,
        slide: projectDoc.slide,
      });

      setProjectName(project.project_name);
      setDescTextCount(projectDoc.project_desc?.split(/\s+/).length);
      setTitleCharCount(projectDoc.project_short_name?.length);

      if (projectDoc.slide) {
        const slideType = await firebaseStorage.getAttachmentFirebaseType(
          projectDoc.slide
        );
        setSlideFileName(
          projectDoc.project_name + "_slide." + slideType.split("/")[1]
        );
      }

      if (projectDoc.slide)
        slideRef.current?.setAttribute("data-src", projectDoc.slide);

      setLoading(false);
    } catch (error: any) {
      await widget.alertError("Error", "Project not found: " + error);
    }
  }

  async function fetchSupervisorsData(usermajor: string) {
    let data = await proxyApi.get("supervisors", {
      filterByFormula: `{major} = "${usermajor}"`,
    });

    const supervisorsData = data.map((supervisor: any) => {
      return {
        id: supervisor.id,
        name: supervisor.name,
        profile_link: supervisor.profile_link,
      };
    });

    // Sort supervisors alphabetically by name
    supervisorsData.sort((a: any, b: any) => a.name.localeCompare(b.name));

    setSupervisors(supervisorsData);
    const supervisorsNameData = supervisorsData.map((supervisor: any) => {
      return supervisor.name;
    });
    setSupervisorsName(supervisorsNameData);
  }

  async function handleCancel(e: any) {
    e.preventDefault();

    const confirm = await widget.confirm(
      "Discard Changes?",
      "This will permanently delete all unsaved or draft data. If you previously submitted the project and then clicked 'Save Draft', it has been converted to a draft and will also be discarded. This action cannot be undone."
    );

    // delete airtable project data if exists
    if (project_id && project.isDraft) {
      try {
        await projectFactory().deleteProject(project_id, user);
      } catch (error: any) {
        console.error("Error deleting project:", error);
      }
    }
    // delete session storage project data
    actionSessionStorage().destroy("projectData");

    if (confirm.isConfirmed) {
      window.location.href = "/participant";
    }
  }

  async function handleSubmit(e: any) {
    e.preventDefault();
    const form = e.target;

    if (submitAction === "draft") {
      return await handleSaveDraft(form);
    } else if (submitAction === "submit") {
      // console.log("Submitting project...");
      return await handleSubmitProject(form);
    } else {
      return await widget.alertError(
        "Error",
        "Invalid submission action. Please try again."
      );
    }
  }

  async function handleSaveDraft(form: any) {
    // if (!isRegistration) {
    //   return await widget.alertError(
    //     "Sorry",
    //     "Project submission is closed. Please contact us if you have any questions.."
    //   );
    // }

    // get slide from to form
    const slideFile = [];
    // check if the slide is uploaded
    if (!!form.slide) {
      if (form.slide.files.length > 0) {
        // check if there is slide from the previous submission, if so, then take the attribute data-src
        if (form.slide.getAttribute("data-src")) {
          slideFile.push(form.slide.getAttribute("data-src"));
        } else if (form.slide.files[0]) {
          slideFile.push(form.slide.files[0]);
        }
      } else {
        // if the slide is not uploaded then check if the slide is from the previous submission
        if (form.slide.getAttribute("data-src")) {
          slideFile.push(form.slide.getAttribute("data-src"));
        }
      }
    }

    // get thumbnail from the form
    const thumbnail = [];
    // If no thumbnail uploaded, skip validation and leave thumbnail empty
    if (
      form.thumbnail &&
      form.thumbnail.files &&
      form.thumbnail.files.length > 0
    ) {
      if (form.thumbnail.files.length > 1) {
        return await widget.alertError(
          "Error",
          "Please upload a thumbnail file"
        );
      } else {
        // check if there is thumbnail from the previous submission, if so, then take the attribute data-src
        if (form.thumbnail.getAttribute("data-src")) {
          thumbnail.push(form.thumbnail.getAttribute("data-src"));
        } else if (form.thumbnail.files[0]) {
          thumbnail.push(form.thumbnail.files[0]);
        }
      }
    } else if (
      form.thumbnail &&
      form.thumbnail.getAttribute &&
      form.thumbnail.getAttribute("data-src")
    ) {
      // If there is a previous submission thumbnail, use it
      thumbnail.push(form.thumbnail.getAttribute("data-src"));
    }

    // get all highlights from the form
    const highlights = [];
    let hasHighlight = false;
    for (let i = 1; i < 5; i++) {
      //if the highlight input is empty then skip
      if (form["highlight" + i]?.length > 1) continue;

      const highlight = form["highlight" + i];
      // check if there is highlight from the previous submission, if so, then take the attribute data-src
      if (highlight?.getAttribute("data-src")) {
        highlights.push(highlight.getAttribute("data-src"));
        hasHighlight = true;
      } else if (highlight?.files[0]) {
        highlights.push(highlight.files[0]);
        hasHighlight = true;
      }
    }

    // get all the tags from the form
    let tech_stack = [];
    //if the tags is not empty
    if (!!form.tech_stack) {
      // has one tag only so not an array
      if (
        form.tech_stack &&
        form.tech_stack.length === undefined &&
        form.tech_stack.value != ""
      ) {
        tech_stack = [form.tech_stack.value];
      } else {
        // has multiple tags so can be push into a csv string
        for (let i = 0; i < form.tech_stack.length; i++) {
          //if the tag value is empty then skip
          if (form.tech_stack[i].value === "") continue;
          // tech_stack += form.tech_stack[i].value;
          tech_stack.push(form.tech_stack[i].value);
        }
      }
    }

    if (
      form.project_name?.value === "" ||
      form.project_name?.value === undefined
    ) {
      form.project_name.value = "Untitled Title";
    }

    // compile into a project data in object
    const projectData = {
      project_name: form.project_name.value,
      project_short_name: form.project_short_name?.value ?? "",
      project_desc:
        form.project_desc?.value?.replace(/(\r\n|\n|\r){3,}/gm, "\n") ?? "",
      project_major: user.student_major ?? "",
      sdg: mapSdg(form.sdg?.value) ?? "",
      supervisor_id: (() => {
        const idx = supervisorsName.indexOf(form.supervisor?.value);
        return idx !== -1 && supervisors[idx]?.id ? [supervisors[idx].id] : [];
      })(),
      slide: slideFile[0] || "",
      tech_stack: tech_stack.length > 0 ? tech_stack : [],
      thumbnail: thumbnail[0] || "",
      highlights: highlights.length > 0 ? highlights : [],
      video: form.video?.value,
      updated_by: user.id,
      year: currentYear.toString(),
      // members_firebaseId: team.members_id.join(","),
      members_id: team.members_id,
      isDraft: true, // mark as draft
    };

    // Modal for draft save confirmation
    const confirm = await widget.alert({
      title: "Save Project as Draft",
      html: `
        <div id="Draft"></div>
      `,
      confirmButtonText: "Agree and Save Draft",
      width: "40rem",

      didOpen: () => {
        ReactDOM.createRoot(
          document.getElementById("Draft") as HTMLElement
        ).render(<DraftCheckbox />);
      },
      focusConfirm: false,
      allowOutsideClick: true,
      showCloseButton: true,
      preConfirm: () => {
        const draftack = document.getElementById(
          "draftack"
        ) as HTMLInputElement;
        if (draftack.value === "false") {
          if (document.getElementById("error")?.classList.contains("hidden")) {
            document.getElementById("error")?.classList.remove("hidden");
          }
          return false;
        }
        return true;
      },
    });

    if (confirm.isConfirmed === false) return;

    // Create a project submission
    try {
      widget.loading();

      // convert original images to webp for smaller size if the image is a file and not a link from existed submission
      if (typeof projectData.thumbnail !== "string")
        projectData.thumbnail = await compressedImage(projectData.thumbnail);

      const compressedHighlights = [];
      for (let i = 0; i < projectData.highlights.length; i++) {
        if (typeof projectData.highlights[i] !== "string")
          compressedHighlights.push(
            await compressedImage(projectData.highlights[i])
          );
        else compressedHighlights.push(projectData.highlights[i]);
      }

      projectData.highlights = compressedHighlights;

      // console.log(actionSessionStorage().get("userData").project_id);

      await projectFactory().submitProject(
        user.id,
        projectData,
        pageMode,
        actionSessionStorage().get("userData").project_id
      );
      setProject({
        ...project,
        project_id: actionSessionStorage().get("userData").project_id,
        isDraft: true, // mark as draft
      });

      const nextAction = await widget.alert({
        icon: "success",
        title: "Success",
        html: "Project saved as draft successfully!<br><br>What would you like to do next?",
        showCancelButton: true,
        confirmButtonText: "Return to Dashboard",
        cancelButtonText: "Continue Editing",
        allowOutsideClick: false,
        width: "30rem",
      });
      if (nextAction.isConfirmed) {
        window.location.href = "/participant";
      }
      // If they choose "Continue Editing", do nothing (stay on page)
    } catch (error: any) {
      await widget.alertError(
        "ERROR",
        "Something went wrong, please try again later",
        error
      );
    }
  }

  // handle submission: create/update
  async function handleSubmitProject(form: any) {
    // if (!isRegistration) {
    //   return await widget.alertError(
    //     "Sorry",
    //     "Project submission is closed. Please contact us if you have any questions.."
    //   );
    // }

    if (form.sdg.value === "DEFAULT")
      return await widget.alertError("Error", "Please select a SDG");

    // check supervisor
    let supervisor = null;
    if (form.supervisor.value) {
      if (supervisorsName.includes(form.supervisor.value)) {
        supervisor = {
          name: form.supervisor.value,
          profile_link:
            supervisors[supervisorsName.indexOf(form.supervisor.value)]
              .profile_link,
          id: supervisors[supervisorsName.indexOf(form.supervisor.value)].id,
        };
      } else {
        await widget.alertError(
          "Error",
          "Please select a supervisor from the list"
        );
        return;
      }
    }

    // get slide from to form
    const slideFile = [];
    // check if the slide is uploaded
    if (form.slide.files.length > 0) {
      // check if there is slide from the previous submission, if so, then take the attribute data-src
      if (form.slide.getAttribute("data-src")) {
        slideFile.push(form.slide.getAttribute("data-src"));
      } else if (form.slide.files[0]) {
        slideFile.push(form.slide.files[0]);
      }
    } else {
      // if the slide is not uploaded then check if the slide is from the previous submission
      if (form.slide.getAttribute("data-src")) {
        slideFile.push(form.slide.getAttribute("data-src"));
      }
    }

    // get thumbnail from the form
    const thumbnail = [];
    if (form.thumbnail.length > 1) {
      return await widget.alertError("Error", "Please upload a thumbnail file");
    } else {
      // check if there is thumbnail from the previous submission, if so, then take the attribute data-src
      if (form.thumbnail.getAttribute("data-src")) {
        thumbnail.push(form.thumbnail.getAttribute("data-src"));
      } else if (form.thumbnail.files[0]) {
        thumbnail.push(form.thumbnail.files[0]);
      }
    }

    // get all highlights from the form
    const highlights = [];
    let hasHighlight = false;
    for (let i = 1; i < 5; i++) {
      //if the highlight input is empty then skip
      if (form["highlight" + i].length > 1) continue;

      const highlight = form["highlight" + i];
      // check if there is highlight from the previous submission, if so, then take the attribute data-src
      if (highlight.getAttribute("data-src")) {
        highlights.push(highlight.getAttribute("data-src"));
        hasHighlight = true;
      } else if (highlight.files[0]) {
        highlights.push(highlight.files[0]);
        hasHighlight = true;
      }
    }

    if (!hasHighlight) {
      return await widget.alertError(
        "Error",
        "Please upload at least one highlight image"
      );
    }

    // get all the tags from the form
    let tech_stack = [];
    //if the tags is not empty
    if (!!form.tech_stack) {
      // has one tag only so not an array
      if (
        form.tech_stack &&
        form.tech_stack.length === undefined &&
        form.tech_stack.value != ""
      ) {
        tech_stack = [form.tech_stack.value];
      } else {
        // has multiple tags so can be push into a csv string
        for (let i = 0; i < form.tech_stack.length; i++) {
          //if the tag value is empty then skip
          if (form.tech_stack[i].value === "") continue;
          // tech_stack += form.tech_stack[i].value;
          tech_stack.push(form.tech_stack[i].value);
        }
      }
    }

    // compile into a project data in object
    const projectData = {
      project_name: form.project_name.value,
      project_short_name: form.project_short_name.value,
      project_desc: form.project_desc.value?.replace(
        /(\r\n|\n|\r){3,}/gm,
        "\n"
      ),
      project_major: user.student_major,
      sdg: mapSdg(form.sdg.value),
      supervisor_id: [
        supervisors[supervisorsName.indexOf(form.supervisor.value)].id,
      ],
      slide: slideFile[0],
      tech_stack: tech_stack,
      thumbnail: thumbnail[0],
      highlights: highlights,
      video: form.video.value,
      updated_by: user.id,
      year: currentYear.toString(),
      // members_firebaseId: team.members_id.join(","),
      members_id: team.members_id,
      isDraft: false, // mark as not draft
    };

    // Accept both YouTube and Google Drive links for the video
    // Prevent double-embedding if already an embed link
    const youtubeEmbedRegex = /youtube\.com\/embed\//i;
    const driveEmbedRegex = /drive\.google\.com\/file\/d\/.*\/preview/i;

    const youtubeMatch = !youtubeEmbedRegex.test(projectData.video)
      ? projectData.video.match(
          /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=)?([A-Za-z0-9_\-]+)/i
        )
      : null;
    const driveMatch = !driveEmbedRegex.test(projectData.video)
      ? projectData.video.match(
          /(?:https?:\/\/)?(?:drive\.google\.com\/file\/d\/)([A-Za-z0-9_\-]+)(?:\/view)?/i
        )
      : null;

    console.log(projectData.video, youtubeMatch, driveMatch);

    if (youtubeMatch) {
      projectData.video =
        "https://youtube.com/embed/" + youtubeMatch[1].split("/").pop();
    } else if (driveMatch) {
      projectData.video =
        "https://drive.google.com/file/d/" + driveMatch[1] + "/preview";
    } else {
      // youtubeMatch and driveMatch are both null, meaning it is already a valid embed link
      if (
        !youtubeEmbedRegex.test(projectData.video) &&
        !driveEmbedRegex.test(projectData.video)
      ) {
        // If the link is neither a valid YouTube nor Google Drive link, show an error
        return await widget.alertError(
          "Error",
          "Please enter a valid YouTube or Google Drive link"
        );
      }
    }

    const projectSchema = Yup.object().shape({
      project_name: Yup.string().trim().required("Please enter a project name"),
      project_short_name: Yup.string()
        .trim()
        .required("Please enter a short project name"),
      project_desc: Yup.string()
        .trim()
        .required("Please tell us about your project"),
      video: Yup.string()
        .trim()
        .required("Please provide a presentation video link")
        .test(
          "is-valid-video-link",
          "Please enter a valid YouTube or Google Drive link",
          (value) => {
            if (!value) return false;
            const youtubeRegex =
              /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([A-Za-z0-9_\-]+)/i;
            const driveRegex =
              /(?:https?:\/\/)?(?:drive\.google\.com\/file\/d\/)([A-Za-z0-9_\-]+)(?:\/view)?/i;
            return youtubeRegex.test(value) || driveRegex.test(value);
          }
        ),
      slide: Yup.mixed().nullable(),
      tech_stack: Yup.array()
        .min(1, "Please add at least one tag")
        .max(5, "You can only add up to 5 tags"),
    });

    try {
      await projectSchema.validate(projectData);
    } catch (error: any) {
      return await widget.alertError("Sorry", error.message);
    }

    // Modal for regulations checkbox agreement
    const confirm = await widget.alert({
      title: "Project Submission Regulations",
      html: '<div id="Regulations"></div>',
      confirmButtonText: "Agree and Submit",
      width: "40rem",

      didOpen: () => {
        ReactDOM.createRoot(
          document.getElementById("Regulations") as HTMLElement
        ).render(<RegulationCheckbox />);
      },
      focusConfirm: false,
      allowOutsideClick: false,
      showCloseButton: true,
      preConfirm: () => {
        const agreeall = document.getElementById(
          "agreeall"
        ) as HTMLInputElement;
        if (agreeall.value === "false") {
          if (document.getElementById("error")?.classList.contains("hidden")) {
            document.getElementById("error")?.classList.remove("hidden");
          }
          return false;
        }
        return true;
      },
    });

    if (confirm.isConfirmed === false) return;

    // Create a project submission
    try {
      widget.loading();

      // convert original images to webp for smaller size if the image is a file and not a link from existed submission
      if (typeof projectData.thumbnail !== "string")
        projectData.thumbnail = await compressedImage(projectData.thumbnail);

      const compressedHighlights = [];
      for (let i = 0; i < projectData.highlights.length; i++) {
        if (typeof projectData.highlights[i] !== "string")
          compressedHighlights.push(
            await compressedImage(projectData.highlights[i])
          );
        else compressedHighlights.push(projectData.highlights[i]);
      }

      projectData.highlights = compressedHighlights;

      await projectFactory().submitProject(
        user.id,
        projectData,
        pageMode,
        user.project_id ?? ""
      );

      await widget.alertSuccess(
        "Success",
        "Project submitted successfully! Returning to dashboard ...",
        3000
      );
      // actionSessionStorage().destroy("projectData");
      window.location.href = "/participant";
    } catch (error: any) {
      await widget.alertError(
        "ERROR",
        "Something went wrong, please try again later",
        error
      );
    }
  }

  async function handleSlideChange(e: any) {
    //if file size is more than 5mb
    if (e.files[0].size > 5000000) {
      e.value = "";
      return await widget.alertError("Error", "File size is too large!");
    }

    const formatAccepted = [
      "application/pdf",
      "application/ppt",
      "application/pptx",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    ];

    if (!formatAccepted.includes(e.files[0].type)) {
      e.value = "";
      return await widget.alertError("Sorry", "Please upload a pdf/ppt file!");
    }

    if (e.getAttribute("data-src")) {
      e.removeAttribute("data-src");
    }
    setSlideFileName(e.files[0].name);

    // hide button
    document.getElementById("download-slide")?.classList.add("hidden");

    await widget.alert({
      icon: "success",
      text: "File uploaded successfully!",
    });
  }

  function handleTextCountChange(e: any) {
    const wordCount = e.target.value.trim().split(/\s+/).length;

    if (wordCount > 200) {
      e.preventDefault();
      // remove the words that exceed the limit
      e.target.value = e.target.value
        .trim()
        .split(/\s+/)
        .slice(0, 200)
        .join(" ");

      // set the word count to 200
      setDescTextCount(200);

      widget.alert({
        position: "top",
        text: "Maximum word count reached!",
        timer: 1500,
        color: "#e63946",
        timerProgressBar: true,
        showConfirmButton: false,
        backdrop: false,
        width: "20rem",
      });
      return;
    }

    setDescTextCount(wordCount);
  }

  function handleCharCountChange(e: any) {
    const charCount = e.target.value.length;
    setTitleCharCount(charCount);
  }

  // starting point
  useEffect(() => {
    (async () => {
      //set user data from session storage
      const participant: ParticipantSession =
        actionSessionStorage().get("userData");

      if (
        (pageMode === "create" || pageMode === "edit") &&
        import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "false" &&
        !participant.is_special_access_granted
      ) {
        return (window.location.href = "/404");
      }

      if (participant) {
        setUser(participant);

        if (participant.is_team) {
          // if the user is in a team then get the team data
          setUser(participant);
          const teamDoc = await teamFactory().getTeam(participant.project_id);
          if (!teamDoc) {
            await widget.alertError("ERROR", "No project data found");
            window.location.href = "/participant";
          }
        }

        // if the project id is not the same as the user project id then redirect to dashboard page
        if (
          project_id &&
          participant.project_id !== project_id &&
          !project.isDraft
        ) {
          await widget.alertError(
            "ERROR",
            "You are not allowed to access this project"
          );
          window.location.href = "/participant";
        }

        // if the user team has a project and want to create a new project then redirect to edit page
        if (
          pageMode === "create" &&
          project?.project_id &&
          project?.project_name
        ) {
          window.location.href =
            "/participant/submission/edit/" + project?.project_id;
        }
      } else {
        // if no user data found then redirect to dashboard page
        await widget.alertError("ERROR", "No user or team data found");
        window.location.href = "/participant";
      }

      // @WenHao1223 TBC by topcom
      await fetchSupervisorsData(participant.student_major);

      // if the pageMode is edit then fetch the project data from airtable
      if (pageMode === "edit") {
        if (!project_id) {
          await widget.alertError("ERROR", "No project id found");
          window.location.href = "/participant";
        } else await fetchProjectData();
      }
    })();
  }, []);

  return (
    <div className="md:rounded-b-md">
      {loading && (
        <div className="h-screen flex justify-center items-center">
          <Loading />
        </div>
      )}
      <ProjectNavbar project={project} loading={loading} pageMode={pageMode} />
      <div className="md:px-10 w-full pt-10 pb-5" id="meow">
        <form
          onSubmit={handleSubmit}
          className={`${loading && "hidden"}   flex flex-col gap-12 px-10`}
        >
          <p className="text-center py-1 bg-primary-6 text-white">
            Selected Project Major:{" "}
            <b className="max-sm:block">{user.student_major}</b>
          </p>

          {/* First: SDG */}
          <div>
            <CustomLabel
              labelFor="Target Sustainable Development Goal (SDG)"
              number="1"
            />

            <select
              name="sdg"
              defaultValue={
                pageMode === "create"
                  ? "DEFAULT"
                  : pageMode === "edit"
                    ? project.sdg
                    : undefined
              }
              value={project?.sdg && parseInt(project.sdg)}
              className="daisy-select w-full max-h-[200px] overflow-y-auto"
              onChange={(e) =>
                setProject({ ...project, sdg: mapSdg(e.target.value) })
              }
              required
            >
              <option
                value={pageMode === "edit" && project.sdg}
                disabled
                hidden
              >
                {project?.sdg ? project?.sdg : "Select SDG"}
              </option>
              {[...Array(17)].map((_, i) => (
                <option key={i + 1} value={i + 1} className="text-lg m-2">
                  {mapSdg(i + 1)}
                </option>
              ))}
            </select>
          </div>

          {/* Second: Supervisor Name */}
          <div>
            <CustomLabel labelFor={"Supervisor Name"} number="2" />
            <SelectAutoComplete
              options={supervisorsName}
              name="supervisor"
              value={
                pageMode === "edit" &&
                project.supervisor &&
                project.supervisor.name
              }
              onChange={(e) =>
                setProject({ ...project, supervisor: { name: e.target.value } })
              }
              className="max-h-[200px] overflow-y-auto"
              autoComplete="off"
              autoCorrect="off"
              spellCheck={false}
            />
          </div>

          {/* Third: Project Title */}
          <div>
            <CustomLabel labelFor={"Project Title"} number="3" />
            <CustomInput
              name="project_name"
              placeholder="Enter Project Title"
              value={project.project_name}
              onChange={(e: any) => {
                setProjectName(e.target.value);
                setProject({ ...project, project_name: e.target.value });
              }}
            />
          </div>

          {/* Third: Short Project Title */}
          <div>
            <CustomLabel labelFor={"Short Project Title"} number="4" />
            <CustomInput
              name="project_short_name"
              maxLength={80}
              placeholder="Enter Short Project Title"
              value={project.project_short_name}
              onChange={(e: any) => {
                handleCharCountChange(e);
                setProject({ ...project, project_short_name: e.target.value });
              }}
            />
            <p className="text-right text-gray-400 mt-1">
              {titleCharCount > 0 ? titleCharCount : 0} / 80
            </p>
          </div>

          {/* Fourth: Project Description */}
          <div>
            <CustomLabel labelFor={"Project Description"} number="5" />
            <div className="flex flex-col gap-4">
              <div>
                <textarea
                  name="project_desc"
                  rows={10}
                  className="block w-full daisy-textarea whitespace-pre-wrap"
                  placeholder="Describe your project (Markdown supported)"
                  defaultValue={project.project_desc}
                  onChange={(e: any) => {
                    handleTextCountChange(e);
                    setProject({ ...project, project_desc: e.target.value });
                  }}
                  style={{ whiteSpace: "pre-wrap", wordWrap: "break-word" }}
                ></textarea>
                <p className="text-right text-gray-400 mt-1">
                  {descTextCount > 0 ? descTextCount : 0} / 200
                </p>
              </div>
              <div className="border rounded-lg p-4 bg-gray-50">
                <h3 className="text-sm font-medium mb-2">Preview:</h3>
                <div className="prose prose-sm max-w-none">
                  <ReactMarkdown>
                    {project.project_desc || "Preview will appear here"}
                  </ReactMarkdown>
                </div>
              </div>
            </div>
          </div>

          {/* Fifth: Presentation Video Link */}
          <div>
            <CustomLabel labelFor={"Presentation Video Link"} number="6" />
            <TextInfo color="bg-red-ic-2">
              Video link must be a valid YouTube or Google Drive link and can be
              publicly accessible.
            </TextInfo>
            <CustomInput
              name="video"
              placeholder="Enter Presentation Video Link (YouTube or Google Drive)"
              value={project.video}
              onChange={(e: any) =>
                setProject({ ...project, video: e.target.value })
              }
            />
          </div>

          {/* Sixth: Presentation Slides File */}
          <div>
            <CustomLabel
              labelFor={"Presentation Slides (optional)"}
              number="7"
            />
            <TextInfo color="bg-red-ic-2">
              File must be in PDF or PPT format and must not exceed 5 MB.
            </TextInfo>
            <div className="flex items-center justify-center w-full">
              <label
                htmlFor="dropzone-file"
                className="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded cursor-pointer bg-white dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6 overflow-hidden">
                  <svg
                    aria-hidden="true"
                    className="w-10 h-10 mb-3 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                    ></path>
                  </svg>
                  <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span className="font-semibold">Click to upload</span>
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    PPTX or PDF
                  </p>
                  {slideFileName && (
                    <>
                      File Name:
                      <p className="text-center">{slideFileName}</p>
                    </>
                  )}
                </div>
                <input
                  id="dropzone-file"
                  type="file"
                  name="slide"
                  className="hidden"
                  ref={slideRef!}
                  onChange={(e: any) => {
                    handleSlideChange(e.target);
                    setProject({ ...project, slide: e.target });
                  }}
                />
              </label>
            </div>
            {project.slide && slideFileName && pageMode !== "create" && (
              <a
                //onclick download file
                href={project.slide}
                download
                target="_blank"
                className="font-Roboto bg-[#383838] hover:bg-[#1d1d1d] transition duration-100 py-2 px-8 mt-3 mx-auto text-center text-white rounded mb-2 flex items-center w-fit gap-2 justify-center"
                id="download-slide"
              >
                <i className="fa-solid fa-download"></i> Download Slide
              </a>
            )}
          </div>

          {/* Seventh: Project Thumbnail */}
          <div>
            <CustomLabel labelFor={"Project Thumbnail"} number="8" />
            <TextInfo color="bg-blue-700">
              <b className="max-md:block">Hint:</b> Project thumbnail should
              offer a quick, visual representation of a project. <br />
              Use a 16:9 aspect ratio to enhance display quality on the viewing
              page.
            </TextInfo>
            <TextInfo color="bg-red-ic-2">
              Minimum image resolution is 1280x720 and image file must not
              exceed 5 MB
            </TextInfo>
            <CustomFileInput name={"thumbnail"} src={project.thumbnail} />
          </div>

          {/* Eighth: Project Highlights */}
          <div>
            <CustomLabel labelFor={"Project Highlights"} number="9" />
            <TextInfo color="bg-blue-700">
              <b className="max-md:block">Hint:</b> Proper and relevant images
              help emphasize key features
            </TextInfo>
            <TextInfo color="bg-red-ic-2">
              Minimum image resolution is 1280x720 and image file must not
              exceed 3 MB
            </TextInfo>

            <div className="grid md:grid-cols-2 gap-5 mt-5">
              <CustomFileInput
                name={"highlight1"}
                src={project.highlights && project.highlights[0]}
              />
              <CustomFileInput
                name={"highlight2"}
                src={project.highlights && project.highlights[1]}
              />
              <CustomFileInput
                name={"highlight3"}
                src={project.highlights && project.highlights[2]}
              />
              <CustomFileInput
                name={"highlight4"}
                src={project.highlights && project.highlights[3]}
              />
            </div>
          </div>

          {/* Ninth: Technologies and Stacks */}
          <div>
            <CustomLabel labelFor={"Technologies and Stacks"} number="10" />
            <TextInfo color="bg-blue-700">
              <b className="max-md:block">Hint:</b> Relevant tags help boost
              project exposure. Click the '+ Add tag' button to add tag
            </TextInfo>
            <div
              className="w-full h-full flex flex-wrap items-center gap-3"
              id="tagsinput"
            >
              {project.tech_stack &&
                project.tech_stack
                  // .split(",")
                  .map((tag: string, index: number) => (
                    <input
                      type="text"
                      size={15}
                      defaultValue={tag}
                      key={index}
                      name="tech_stack"
                      className="bg-white border border-grey-2 hover:border-primary-2 duration-100 transition rounded-full focus:border-primary-6 text-center"
                      maxLength={20}
                      //check if user press space the space key then delete the space and show alert
                      onKeyDown={(e: any) => {
                        if (e.key === " " || e.key === ",") {
                          e.preventDefault();
                          widget.alert({
                            position: "top",
                            text: "Tag cannot contain space or comma",
                            timer: 1500,
                            color: "#e63946",
                            timerProgressBar: true,
                            showConfirmButton: false,
                            backdrop: false,
                            width: "25rem",
                          });
                        }
                        // if user press backspace again when the input value is empty then remove instance
                        if (e.key === "Backspace" && e.target.value === "") {
                          e.preventDefault();
                          e.target.remove();
                        }
                      }}
                    />
                  ))}
              <button
                type="button"
                className="max-sm:w-full h-fit rounded-full bg-primary-2 text-primary-7 hover:bg-primary-6 hover:text-white transition  px-4 py-2"
                onClick={() => {
                  if (
                    (document.getElementById("tagsinput")
                      ?.childElementCount as number) > 5
                  ) {
                    widget.alert({
                      position: "top",
                      text: "You can only add up to 5 tags",
                      timer: 1500,
                      color: "#e63946",
                      timerProgressBar: true,
                      showConfirmButton: false,
                      backdrop: false,
                      width: "25rem",
                    });
                    return;
                  }
                  var input = document.createElement("input");
                  input.type = "text";
                  input.size = 15;
                  input.name = "tech_stack";
                  input.className =
                    "daisy-input  h-[41.6px] max-sm:w-full rounded-full text-center";
                  input.maxLength = 20;
                  input.onkeydown = (e: any) => {
                    //check if user press space the space key then delete the space and show alert
                    if (e.key === " " || e.key === ",") {
                      e.preventDefault();
                      widget.alert({
                        position: "top",
                        text: "Tag cannot contain space or comma",
                        timer: 1500,
                        color: "#e63946",
                        timerProgressBar: true,
                        showConfirmButton: false,
                        backdrop: false,
                        width: "25rem",
                      });
                    }
                    // if user press backspace again when the input value is empty then remove instance
                    if (e.key === "Backspace" && e.target.value === "") {
                      e.preventDefault();
                      e.target.remove();
                    }
                  };
                  //insert the new tag input before the last child of the tagsinput div
                  document
                    .getElementById("tagsinput")
                    ?.insertBefore(
                      input,
                      document.getElementById("tagsinput")?.lastChild as any
                    );
                }}
              >
                + Add tag
              </button>
            </div>
          </div>

          {/* Submit button */}
          <div className="flex flex-col justify-center items-center gap-3 mt-5">
            <p className="text-mobile-14 text-grey-5/60 text-center">
              You can save your project as a draft to continue editing later.
              Only submitted projects will be reviewed.
              <br />
              You can still make changes at any time before the submission
              deadline.
            </p>
            <div className="flex max-sm:flex-col space-between gap-2 md:gap-4 max-sm:w-full">
              <button
                type="button"
                className="bg-gradient-to-r bg-dangerColor hover:bg-dangerColorHover hover:-translate-y-1 hover:shadow-xl transition-all duration-200 !font-medium text-mobile-20 pl-6 pr-8 py-3 rounded-full shadow-lg text-white flex gap-2 items-center justify-center"
                onClick={handleCancel}
              >
                <i className="fa-solid fa-xmark"></i>
                Discard Changes
              </button>

              <button
                type="submit"
                className="bg-transparent border-2 border-primary-6 text-primary-6 hover:bg-primary-6 hover:text-white hover:-translate-y-1 hover:shadow-xl transition-all duration-200 !font-medium text-mobile-20 pl-6 pr-8 py-3 rounded-full shadow-lg flex gap-2 items-center justify-center"
                onClick={() => setSubmitAction("draft")}
              >
                <i className="fa-solid fa-notes"></i>
                Save Draft
              </button>

              <button
                type="submit"
                className="bg-gradient-to-r bg-primary-6 hover:bg-primary-7 hover:-translate-y-1 hover:shadow-xl transition-all duration-200 !font-medium text-mobile-20  pl-6 pr-8 py-3 rounded-full shadow-lg text-white flex gap-2 items-center justify-center"
                onClick={() => setSubmitAction("submit")}
              >
                <i
                  className={`fa-regular ${
                    pageMode === "edit" ? "fa-pencil" : "fa-paper-plane"
                  }`}
                ></i>
                {pageMode === "edit" && !project.isDraft
                  ? "Update Project"
                  : "Submit Project"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default ProjectSubmissionForm;
