<div id="container-cd" class="mt-20 text-black flex-row px-2 md:px-10 pb-2 text-md md:text-9xl">
  <div class="flex-cols-1">
    <div class="flex my-0 leading-4 justify-center" id="demo">
      <div id="time">
        <div class="cdbox"></div>
        <p id="days" class="cd-num"></p>
      </div>
      <div id="time">
        <div class="cdbox"></div>
        <p id="hours" class="cd-num"></p>
      </div>
      <div id="time">
        <div class="cdbox"></div>
        <p id="minutes" class="cd-num"></p>
      </div>
      <div id="time">
        <div class="cdbox"></div>
        <p id="seconds" class="cd-num"></p>
        <span class="cd-label"></span>
      </div>
    </div>
  </div>
</div>

<script>
  // Set the date we're counting down to
  var countDownDate = new Date("May 28, 2025 00:00:00").getTime();

  // Update the count down every 1 second
  var x = setInterval(function () {
    // Get today's date and time
    var now = new Date().getTime();

    // Find the distance between now and the count down date
    var distance = countDownDate - now;

    if (distance / (1000 * 60 * 60 * 24) < 1) {
      var seconds = Math.floor((distance % (1000 * 60)) / 1000);
      var secondsel = document.getElementById("seconds");
      if (seconds < 10) secondsel!.innerHTML = ":0" + seconds.toString();
      else secondsel!.innerHTML = ":" + seconds.toString();
    } else {
      // Time calculations for days, hours, minutes and seconds
      var days = Math.floor(distance / (1000 * 60 * 60 * 24));
      document.getElementById("days")!.innerHTML = days.toString() + ":";
    }
    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var hoursel = document.getElementById("hours");
    if (hours < 10) hoursel!.innerHTML = "0" + hours.toString() + ":";
    else hoursel!.innerHTML = hours.toString() + ":";

    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
    var minutesel = document.getElementById("minutes");
    if (minutes < 10) minutesel!.innerHTML = "0" + minutes.toString();
    else minutesel!.innerHTML = minutes.toString();

    // If the count down is over, write some text
    if (distance < 0) {
      clearInterval(x);
      document.getElementById("demo")!.innerHTML = "OPEN NOW!";
    }
  }, 1000);
</script>
