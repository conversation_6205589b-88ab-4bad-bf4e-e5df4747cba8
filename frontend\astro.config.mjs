import { defineConfig } from "astro/config";
import react from "@astrojs/react";
import tailwind from "@astrojs/tailwind";
import cloudflare from "@astrojs/cloudflare";

import compressor from "astro-compressor";

// https://astro.build/config
export default defineConfig({
  integrations: [react(), tailwind(), compressor()],
  output: "server",
  adapter: cloudflare(),
  vite: {
    ssr: {
      noExternal: ["ultrahtml"],
    },
  },
});
