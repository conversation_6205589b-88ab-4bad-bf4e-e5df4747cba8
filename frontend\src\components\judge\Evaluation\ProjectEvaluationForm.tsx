import { useState, useEffect, useRef, useContext } from "react";
import {
  EvaluationData,
  Project,
  judgeFactory,
  teamFactory,
  projectFactory,
  Judge,
} from "@/modules";
import {
  actionSessionStorage,
  compressedImage,
  widget,
  proxyApi,
  auth,
  db,
  firebaseStorage,
  mapSdg,
} from "@/utils";
import {
  CustomInput,
  CustomLabel,
} from "@components/participant/Submission/Inputs";
import Loading from "@components/general/Loading";
import * as Yup from "yup";
import ProjectNavbar from "@components/participant/Submission/ProjectNavbar";
import Rating from "@mui/material/Rating";
import BaseModal from "@components/general/BaseModal";
import EvaluationRating from "./EvaluationRating";
import EvaluationSummary from "./EvaluationSummary";
import RubricModal from "./RubricModal";
import { JudgeContext } from "../Dashboard/Context";

import evaluationSubcategories from "@data/judging-rubrics.json";

function ProjectEvaluationForm({ project_id }: any) {
  const [judgeId, setJudgeID] = useState(null);

  const [loading, setLoading] = useState(true);

  const [prevEvaluationData, setPrevEvaluationData] =
    useState<EvaluationData | null>(null);

  const [evaluationData, setEvaluationData] = useState<EvaluationData | null>(
    null
  );

  const [evaluationCategory, setEvaluationCategory] = useState<Record<
    string,
    string[]
  > | null>(null);

  // const [teamwork, setTeamwork] = useState(0); // only for software engineering  // this year no teamwork rating

  const [textCount, setTextCount] = useState(0);

  const [project, setProject] = useState<Project>(null);
  const [isEvaluated, setIsEvaluated] = useState(false);

  async function downloadSlide() {
    // Comment for testing
    const fileURL = project?.slide;
    const link = document.createElement("a");

    link.href = fileURL;
    link.target = "_blank"; //define where the link should open: In this case, the link will be opened in a new browser tab
    link.download = fileURL;
    link.click();
  }

  useEffect(() => {
    const unsub = auth.onAuthStateChanged((user) => {
      if (user) {
        setJudgeID(user.uid);
      } else {
        document.location.href = "/sign-in";
      }
    });

    // Cleanup function for useEffect
    return () => {
      unsub();
    };
  }, []);

  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        if (judgeId) {
          const projectData = await projectFactory().getProject(project_id);

          setProject(projectData);

          let evaluation;

          evaluation = await judgeFactory().getEvaluation(
            project_id,
            judgeId,
            projectData.project_name
          );

          if (evaluation === false) {
            widget
              .alert({
                position: "top",
                text: "You are not assigned to evaluate this project!",
                timer: 1500,
                color: "#e63946",
                timerProgressBar: true,
                showConfirmButton: false,
                backdrop: false,
                width: "20rem",
              })
              .then(() => {
                document.location.href = "/judge";
              });
          }

          if (evaluation === null || evaluation === undefined) {
            setIsEvaluated(false);
          } else {
            setIsEvaluated(true);
          }

          // Dynamically set evaluation categories from judging-rubrics.json
          const categoriesFromJson = evaluationSubcategories.reduce(
            (acc: Record<string, string[]>, item: any) => {
              if (!acc[item.category]) acc[item.category] = [];
              acc[item.category].push(item.name);
              return acc;
            },
            {}
          );
          setEvaluationCategory(categoriesFromJson);

            if (evaluation && Object.keys(evaluation).length > 0) {
              // Dynamically get fields from judging-rubrics.json
              const rubricFields = evaluationSubcategories.map((item: any) => item.key || item.name);
              const evalData: any = {};
              rubricFields.forEach((field: string) => {
                evalData[field] = evaluation[field] ?? "";
              });
              evalData.comments = evaluation.comments ?? "";

              setEvaluationData(evalData);
              setTextCount((evaluation.comments ?? "").split(" ").length);
            }

          setLoading(false);
        }
      } catch (error) {
        widget.alertError("Error", error);
      }
    };

    fetchProjectData();
  }, [judgeId]);

  function handleHighlightClick(e: any) {
    //the higlight will go like a modal fullscreen and the user can click on the background to close it
    const modal = document.createElement("div");
    modal.id = "modal";
    modal.classList.add(
      "fixed",
      "top-0",
      "top-0",
      "flex",
      "w-full",
      "h-full",
      "items-center",
      "justify-center",
      "w-screen",
      "h-screen",
      "bg-black",
      "bg-opacity-50",
      "z-50"
    );
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });

    //modal will have x button to close it
    const xbutton = document.createElement("button");
    xbutton.classList.add(
      "absolute",
      "top-0",
      "right-8",
      "text-white",
      "text-2xl",
      "p-2",
      "hover:text-grey-5"
    );
    xbutton.innerHTML = "&times;";
    xbutton.addEventListener("click", () => {
      modal.remove();
    });
    modal.appendChild(xbutton);

    //in the modal will have the image src
    const img = document.createElement("img");
    img.src = e.target.src;
    img.classList.add("max-h-[80%]", "max-w-[80%]");
    modal.appendChild(img);
    document.body.appendChild(modal);
  }

  function handleCommentChange(e: any) {
    setEvaluationData({ ...evaluationData, comments: e.target.value });
    const wordCount = e.target.value.trim().split(/\s+/).length;
    // If the maximum word count is 300
    if (wordCount > 300) {
      e.preventDefault();
      // remove the words that exceed the limit
      e.target.value = e.target.value
        .trim()
        .split(/\s+/)
        .slice(0, 300)
        .join(" ");

      // set the word count to 200
      setTextCount(300);

      widget.alert({
        position: "top",
        text: "Maximum word count reached!",
        timer: 1500,
        color: "#e63946",
        timerProgressBar: true,
        showConfirmButton: false,
        backdrop: false,
        width: "20rem",
      });
      return;
    }

    setTextCount(wordCount);
  }

  async function handleCancel() {
    const confirm = await widget.confirm(
      "Are you sure?",
      "Are you certain to cancel the edit?"
    );
    if (!confirm.isConfirmed) return;
    window.location.href = "/judge";
  }

  async function handleSubmit(e: any) {
    e.preventDefault();

    if (textCount < 30) {
      await widget.alertError(
        "Error",
        "Please make sure your comment is at least 30 words long."
      );
      return;
    }

    // Validation schema for the evaluation
    // For all the fields in the evaluation object, they must be a number between 1 and 10

    // console.log("evaluationData", evaluationData)

    const evaluationFields = [
      "problem_statement",
      "solution",
      "innovativeness",
      "impact_benefit",
      "practicality",
      "content_clarity",
      "presentation",
      "innovation_creativity",
      "entrepreneurship",
    ];

    // check if key in evaluationSchema contains all evaluation fields
    if (!evaluationFields.every((key) => key in evaluationData)) {
      widget.alertError("Error", "Please fill in all the fields.");
      return;
    }

    const evaluationSchema = Yup.object().shape({
      ...Object.fromEntries(
      Object.entries(evaluationData)
        .filter(([key]) => key !== "comments")
        .map(([key]) => {
        // Find the rubric item for this key
        const rubricItem = evaluationSubcategories.find(
          (item: any) => (item.key || item.name) === key
        );
        const max = rubricItem?.max ?? 10; // default max is 10
        return [key, Yup.number().min(1).max(max)];
        })
      ),
    });

    // if (project.project_major === "Software Engineering") {
    //   evaluation = {
    //     ...evaluation,
    //     teamwork, // add teamwork rating to the existing evaluation
    //   };
    //   evaluationSchema.concat(
    //     Yup.object().shape({
    //       teamwork: Yup.number().min(1).max(10),
    //     })
    //   );
    // }

    evaluationSchema
      .validate(evaluationData, { strict: true }) // validate the evaluation
      .then(() => {
        widget.loading();

        judgeFactory()
          .evaluateProject(
            project_id,
            judgeId,
            evaluationData,
            project.project_name,
            project.year
          )
          .catch((err: any) => {
            widget.alertError("Error", err);
          });
      })
      .catch((err) => {
        widget.alertError("Error", err);
      });
  }

  return (
    <div className="md:rounded-b-md">
      {loading ? (
        <div className="h-screen flex justify-center items-center">
          <Loading />
        </div>
      ) : (
        <>
          <ProjectNavbar
            loading={loading}
            project={project}
            pageMode={"evaluation"}
          />
          <div className="md:px-10 w-full py-10 bg-white" id="meow">
            <div className={`${loading && "hidden"}  px-10`}>
              <section className="flex flex-col gap-12">
                <p className="text-center py-1 bg-primary-6 text-white">
                  Project Major: <b>{project?.project_major}</b>
                </p>

                {/* First: SDG */}
                <div>
                  <CustomLabel
                    labelFor="Target Sustainable Development Goal (SDG)"
                    number="1"
                  />
                  <input
                    defaultValue={`${project?.sdg ? project?.sdg : ""}`}
                    className="daisy-input-disabled w-full "
                    disabled
                  />
                </div>

                {/* Second: Supervisor Name */}
                <div>
                  <CustomLabel labelFor={"Supervisor Name"} number="2" />
                  <input
                    defaultValue={`${
                      project?.supervisor?.name ? project?.supervisor.name : ""
                    }`}
                    className="daisy-input-disabled w-full"
                    disabled
                  />
                </div>

                {/* Third: Project Title */}
                <div>
                  <CustomLabel labelFor={"Project Title"} number="3" />
                  <input
                    value={project?.project_name ? project?.project_name : ""}
                    className="daisy-input-disabled w-full"
                    disabled
                  />
                </div>

                {/* Fourth: Project Description */}
                <div>
                  <CustomLabel labelFor={"Project Description"} number="4" />
                  <textarea
                    name="project_desc"
                    disabled
                    rows={12}
                    className="daisy-textarea-disabled w-full"
                    defaultValue={project?.project_desc}
                  ></textarea>
                </div>

                {/* Fifth: Presentation Video Link */}
                <div>
                  <CustomLabel
                    labelFor={"Presentation Video Link"}
                    number="5"
                  />
                  <div className="aspect-video h-full">
                    <iframe
                      src={project?.video}
                      className="w-full h-full rounded object-cover"
                      allowFullScreen
                    ></iframe>
                  </div>
                  <p>
                    Can't view the video?
                    <a
                      href={project?.video}
                      target="_blank"
                      className="ml-2 text-primary-5 hover:text-primary-6"
                    >
                      Click here.
                    </a>
                  </p>
                </div>

                {/* Sixth: Presentation Slides File */}
                <div>
                  <CustomLabel labelFor={"Presentation Slides"} number="6" />
                  {project?.slide ? (
                    <button
                      //onclick download file
                      onClick={downloadSlide}
                      className="bg-primary-6 hover:bg-primary-7 transition duration-100 w-full text-center text-white rounded daisy-btn flex items-center gap-3 justify-center border-none"
                    >
                      <i className="fa-solid fa-download"></i> Download
                      Presentation Slides
                    </button>
                  ) : (
                    <p>No presentation slides provided </p>
                  )}
                </div>

                {/* Seventh: Project Thumbnail */}
                <div>
                  <CustomLabel labelFor={"Project Thumbnail"} number="7" />
                  <div className="relative aspect-video object-cover">
                    <label className="flex flex-col items-center justify-center aspect-video rounded cursor-pointer">
                      <img
                        src={project?.thumbnail}
                        className="w-full h-full relative object-cover object-center rounded-lg"
                        onClick={handleHighlightClick}
                      />
                    </label>
                  </div>
                </div>

                {/* Eighth: Project Highlights */}
                <div>
                  <CustomLabel labelFor={"Project Highlights"} number="8" />
                  <p className="text-gray-500 text-sm">
                    Click on any of the images to view it in larger size
                  </p>

                  <div className="grid md:grid-cols-2 gap-5 mt-5">
                    {project?.highlights && project?.highlights?.length > 0 ? (
                      project?.highlights?.map(
                        (highlight: any, index: number) => (
                          <div className="relative aspect-video" key={index}>
                            <label className="flex flex-col items-center justify-center aspect-video rounded cursor-pointer">
                              <img
                                src={highlight}
                                className="w-full h-full relative object-cover object-center rounded-lg transition shadow hover:shadow-md"
                                onClick={handleHighlightClick}
                              />
                            </label>
                          </div>
                        )
                      )
                    ) : (
                      <p>No project highlights provided </p>
                    )}
                  </div>
                </div>

                {/* Ninth: Technologies and Stacks */}
                <div>
                  <CustomLabel
                    labelFor={"Technologies and Stacks"}
                    number="9"
                  />
                  <div
                    className="w-full h-full flex flex-wrap gap-3"
                    id="tagsinput"
                  >
                    {project?.tech_stack ? (
                      project?.tech_stack.map((tag: string, index: number) => (
                        <input
                          disabled
                          type="text"
                          size={15}
                          defaultValue={tag}
                          key={index}
                          name="tags"
                          className="bg-transparent border border-grey-3/50 hover:border-primary-7 duration-100 transition rounded-full font-medium px-5 py-2 hover:cursor-pointer text-center"
                          maxLength={20}
                        />
                      ))
                    ) : (
                      <p>No tech stacks provided </p>
                    )}
                  </div>
                </div>
              </section>

              <hr className="my-12 border-grey-3 border-solid" />

              {/* evaluation section */}
              <section>
                <div id="evaluate">
                  <p className="text-2xl md:text-3xl font-bold text-center">
                    Project Evaluation
                  </p>
                  <p className="text-mobile-14 mt-2 text-center text-grey-5">
                    Please evaluate the project based on the following criteria
                    and provide your comments. You may refer to the judging
                    rubric for more details.
                  </p>
                </div>
                <form
                  className={`${
                    loading && "hidden"
                  } w-full max-w-6xl flex flex-col gap-8`}
                >
                  <div className="flex justify-center ">
                    <div className="w-full flex flex-col gap-y-8 items-center py-8">
                      <p className="text-center py-1 bg-primary-6 text-white w-full">
                        <b>Development</b> (70%)
                        <button
                          type="button"
                          className="ml-2 text-white hover:text-primary-2"
                          onClick={() => {
                            const modal = document.getElementById(
                              "development_info_modal"
                            ) as HTMLDialogElement;
                            modal?.showModal();
                          }}
                        >
                          <i className="fa-solid fa-circle-info"></i>
                        </button>
                      </p>

                      <RubricModal
                        id="development_info_modal"
                        category="Development"
                      />
                      <EvaluationRating
                        name="problem_statement"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Problem Statement"
                      />
                      <EvaluationRating
                        name="solution"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Solution"
                      />
                      <EvaluationRating
                        name="innovativeness"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Innovativeness"
                      />
                      <EvaluationRating
                        name="impact_benefit"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Impact and Benefit"
                      />

                      {/* This year no teamwork rating */}
                      {/*
                  {project?.project_major === "Software Engineering" && (
                    <EvaluationRating data={teamwork={setEvaluationData} label="Teamwork" />
                  )}  
                  */}

                      <EvaluationRating
                        name="practicality"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Practicality"
                      />

                      <p className="text-center py-1 bg-primary-6 text-white w-full">
                        <b>Pitching</b> (20%)
                        <button
                          type="button"
                          className="ml-2 text-white hover:text-primary-2"
                          onClick={() => {
                            const modal = document.getElementById(
                              "pitch_info_modal"
                            ) as HTMLDialogElement;
                            modal?.showModal();
                          }}
                        >
                          <i className="fa-solid fa-circle-info"></i>
                        </button>
                      </p>
                      <RubricModal id="pitch_info_modal" category="Pitch" />
                      <EvaluationRating
                        name="content_clarity"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Content and Clarity"
                      />
                      <EvaluationRating
                        name="presentation"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Presentation Skills"
                      />
                      <EvaluationRating
                        name="innovation_creativity"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Innovation and Creativity"
                      />

                      <p className="text-center py-1 bg-primary-6 text-white w-full">
                        <b>Entrepreneurship</b> (10%)
                        <button
                          type="button"
                          className="ml-2 text-white hover:text-primary-2"
                          onClick={() => {
                            const modal = document.getElementById(
                              "entrepreneurship_info_modal"
                            ) as HTMLDialogElement;
                            modal?.showModal();
                          }}
                        >
                          <i className="fa-solid fa-circle-info"></i>
                        </button>
                      </p>
                      <RubricModal
                        id="entrepreneurship_info_modal"
                        category="Entrepreneurship"
                      />
                      <EvaluationRating
                        name="entrepreneurship"
                        data={evaluationData}
                        setValue={setEvaluationData}
                        label="Entrepreneurial Opportunity"
                      />

                      <p className="text-center py-1 bg-primary-6 text-white w-full">
                        <b>Summary</b>
                      </p>
                      <EvaluationSummary
                        category={evaluationCategory}
                        data={evaluationData}
                      />

                      <div className="w-full">
                        <p className="text-center py-1 bg-primary-6 text-white w-full mb-8">
                          <b>Comments</b>
                        </p>
                        <textarea
                          rows={10}
                          className="block w-full daisy-textarea"
                          placeholder="Provide your comments here (minimum: 30 words, maximum: 300 words)."
                          onChange={handleCommentChange}
                          value={evaluationData?.comments}
                          id="comments"
                        ></textarea>
                        <p className="text-right text-gray-400">
                          {textCount > 0 ? textCount : 0} / 300
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Submit button */}
                  <div className="flex flex-col justify-center items-center gap-3 mt-5">
                    <p className="text-mobile-14 text-grey-5 text-center">
                      After submitting this evaluation, you can still update it
                      anytime before the evaluation deadline.
                    </p>
                    <div className="flex max-sm:flex-col space-between gap-2 md:gap-4 max-sm:w-full">
                      <button
                        type="button"
                        className="bg-gradient-to-r bg-dangerColor hover:bg-dangerColorHover hover:-translate-y-1 hover:shadow-xl transition-all duration-200 !font-medium text-mobile-20 pl-6 pr-8 py-3 rounded-full shadow-lg text-white flex gap-2 items-center justify-center"
                        onClick={handleCancel}
                      >
                        <i className="fa-solid fa-xmark"></i>
                        Cancel
                      </button>

                      <button
                        className="bg-gradient-to-r bg-primary-6 hover:bg-primary-7 hover:-translate-y-1 hover:shadow-xl transition-all duration-200 !font-medium text-mobile-20  pl-6 pr-8 py-3 rounded-full shadow-lg text-white flex gap-2 items-center justify-center"
                        onClick={handleSubmit}
                      >
                        <i
                          className={`fa-regular ${
                            isEvaluated ? "fa-pencil" : "fa-paper-plane"
                          }`}
                        ></i>
                        {isEvaluated ? "Update" : "Submit"}
                      </button>
                    </div>
                  </div>
                </form>
              </section>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default ProjectEvaluationForm;
