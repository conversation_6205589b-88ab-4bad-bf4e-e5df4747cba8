---
const YEAR_FILTER_TYPE = [];

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isPostSubmission = import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

var adjustedYear = (isPostSubmission || isPostEvent) ? parseInt(currentYear) : parseInt(currentYear) - 1;

for (let year = 2023; year <= adjustedYear; year++) {
  YEAR_FILTER_TYPE.push({
    id: year.toString(),
    name: year.toString(),
  });
}
YEAR_FILTER_TYPE.unshift({
  id: "all",
  name: "All",
});

var { major, year } = Astro.props;

if (major === "") major = "All";
if (year === "") year = "All";

---

<div class="flex flex-row items-center gap-2 md:gap-5 max-md:justify-center">
  {
    YEAR_FILTER_TYPE.map((yearFilterType, index) => (
      <>
        {index > 0 && (
          <span class="font-bold text-sm pointer-events-none">•</span>
        )}
        <a
          href={`/projects${major && major !== "All" ? `/${major}` : ""}${yearFilterType.name === "All" ? "" : `?year=${yearFilterType.name}`}`}
          class={`*:text-sm *:font-semibold ${
            year === yearFilterType.name && "bg-primary-6 *:text-white"
          } hover:bg-primary-6 *:hover:text-white px-2 py-[0.1rem] rounded-md transition`}
        >
          <span class="md:hidden">
            {yearFilterType.id == "all"
              ? "All"
              : yearFilterType.id.toUpperCase()}
          </span>
          <span class="hidden md:inline">{yearFilterType.name}</span>
        </a>
      </>
    ))
  }
</div>
