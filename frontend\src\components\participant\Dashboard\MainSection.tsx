import { useContext, useState, useEffect } from "react";
import { TeamSection } from "./TeamSection";
import { ProjectSection } from "./ProjectSection";
import { ParticipantContext } from "./Context";
import ProjectMode from "./ProjectMode";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isPostSubmission = import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const MainSection = () => {
  const {
    TEAM_MAX_LIMIT,
    TEAM_MIN_LIMIT,
    user: participant,
    isTeam,
  } = useContext(ParticipantContext);


  return (
    <div className="bg-white flex flex-col w-full p-5 relative overflow-x-hidden overflow-y-auto h-full">
      {(TEAM_MAX_LIMIT !== 1 && TEAM_MIN_LIMIT === 1) && <ProjectMode />}
      <section id="noticeSection">
        <div className="flex flex-row gap-3 mb-4">
          <div className="bg-blue-300 w-4 rounded-md" />
          <p className="font-semibold ">Notice</p>
        </div>
        <div className="gap-2">
          {/* {isRegistration ? (
            <div className="daisy-alert flex max-lg:flex-col px-7 py-3 justify-between drop-shadow-lg bg-white text-black border-0 w-full">
              <div className="flex gap-3 items-center">
                <i className="fa-solid fa-circle-info"></i>
                <span className="text-left">
                  All final year students are required to join the PIXEL {currentYear}
                  discord server.
                </span>
              </div>
              <a
                href="http://discord.gg/b9bPD5zFDf"
                target="_blank"
                rel="noreferrer noopener"
                className="w-fit"
              >
                <button className="px-4 py-3 bg-primary-6 hover:bg-primary-7 text-white rounded-lg shadow w-full transition flex items-center group/discord gap-2">
                  <i className="fa-brands fa-discord"></i>
                  <span className="text-mobile-16 text-white">
                    Join Discord
                  </span>
                </button>
              </a>
            </div>
          ) : (
            <div className={`daisy-alert flex max-md:flex-col px-3 py-3 md:px-4 md:py-4 justify-between shadow-lg text-white
              ${(
                !participant.is_special_access_granted ||
                isPostSubmission ||
                isPostEvent
              ) && 'border-dangerColor bg-dangerColor'}`}>
              <div className="md:w-[85%] flex gap-3 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  className="stroke-white flex-shrink-0 w-6 h-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  ></path>
                </svg>
                <span className="text-left text-white">
                  {participant.is_special_access_granted && !isPostSubmission && !isPostEvent
                    ? `You have been granted special access to submit or update your project for PIXEL {currentYear}.`
                    : 'Submission has officially closed. Thanks for your hard work.'}
                </span>
              </div>
            </div>
          )} */}
        </div>
      </section>

      {/* If only solo option, hide Team Info section */}
      {TEAM_MAX_LIMIT !== 1 && isTeam && (
        <TeamSection />
      )}
      <ProjectSection />
    </div>
  );
};

export default MainSection;