import { useEffect, useState, useRef } from "react";
import AwardB<PERSON> from "./AwardBar";
import AwardAnimationModal from "./AwardAnimationModal";
import { sortArray } from "@utils/utils";

const AwardList = ({ projects, award_title, year }) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  // default is first project
  const [modal, setModal] = useState({ active: false, index: 0 });
  const [loading, setLoading] = useState(true);
  const [sortOption, setSortOption] = useState("default");

  const [isOpen, setIsOpen] = useState(false);
  const sortOptions = [
    {
      name: "From A-Z",
      icon: "fa-solid fa-arrow-down-a-z",
    },
    {
      name: "From Z-A",
      icon: "fa-solid fa-arrow-down-z-a",
    },
  ];
  const [sortDirection, setSortDirection] = useState(sortOptions[0].name);
  const [sortedProjects, setSortedProjects] = useState([]);

  useEffect(() => {
    if (projects.length > 0) {
      setLoading(false);
    }

    const sortProjectsInitial = async () => {
      const sorted = await sortArray({
        array: [...projects],
        key: year >= "2024" ? "project_short_name" : "project_name",
        type: "asc",
      });
      setSortedProjects(sorted);
    };

    sortProjectsInitial();
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleSortClick = async (sortTypeId: string) => {
    setSortDirection(sortTypeId);
    setIsOpen(false);
    if (sortTypeId === "From A-Z") {
      setSortedProjects(
        await sortArray({
          array: [...projects],
          key: year >= "2024" ? "project_short_name" : "project_name",
          type: "asc",
        })
      );
    } else if (sortTypeId === "From Z-A") {
      setSortedProjects(
        await sortArray({
          array: [...projects],
          key: year >= "2024" ? "project_short_name" : "project_name",
          type: "desc",
        })
      );
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <div className="flex items-center justify-between">
        <h2 className="font-poppins font-semibold text-mobile-20 md:text-tablet-24 lg:text-mobile-34">
          {loading ? (
            <div className="daisy-skeleton h-4 w-20 bg-gray-300"></div>
          ) : (
            award_title
          )}
        </h2>

        <div className="relative flex justify-end" ref={dropdownRef}>
          <div
            className="cursor-pointer flex flex-row items-center gap-2 font-medium w-fit justify-end"
            onClick={toggleDropdown}
          >
            <span className="text-grey-4 ">Sort:</span>
            <span className="whitespace-nowrap">{sortDirection}</span>
            <i
              className={`fa  ${
                isOpen ? "fa-chevron-circle-up" : "fa-chevron-circle-down"
              }`}
              aria-hidden="true"
            ></i>
          </div>

          {isOpen && (
            <ul className="absolute z-10 py-2 bg-white overflow-hidden rounded-md border border-gray-200 shadow mt-8 right-0">
              {sortOptions.map((sortOption, index) => (
                <label
                  htmlFor={sortOption.name}
                  key={index}
                  className="hover:cursor-pointer"
                  onClick={() => handleSortClick(sortOption.name)}
                >
                  <li
                    className={`px-6 py-2 hover:bg-gray-100 cursor-pointer whitespace-nowrap text-start font-medium ${
                      sortDirection === sortOption.name
                        ? "bg-gray-100"
                        : "hover:bg-gray-100"
                    }`}
                    onClick={() => {
                      setIsOpen(false);
                    }}
                  >
                    <i className={`${sortOption.icon} mr-3`}></i>
                    {sortOption.name}
                  </li>
                </label>
              ))}
            </ul>
          )}
        </div>
      </div>

      <div className="flex flex-col">
        {loading ? (
          <ProjectSkeleton />
        ) : (
          <>
            <hr className="w-full text-black/20" />
            {sortedProjects.map((project, index) => {
              return (
                <AwardBar
                  index={index}
                  setModal={setModal}
                  project={project}
                  key={index}
                />
              );
            })}
          </>
        )}
      </div>
      <AwardAnimationModal modal={modal} projects={projects} />
    </>
  );
};

const ProjectSkeleton = () => {
  return (
    <div className="group transition-all duration-200 ease-in-out flex flex-col w-full md:flex-row pl-4 md:px-4 md:pl-4 md:pr-14 md:justify-between py-6 ">
      <span className="group-hover:text-white transition-all duration-200 ease-in-out md:w-3/4 font-inter font-medium text-mobile-16">
        <div className="daisy-skeleton bg-gray-300 h-4 w-20"></div>
      </span>

      <div className="flex flex-col gap-2 md:w-1/4 ">
        {[...Array(1)].map((_, index) => (
          <div className="flex flex-row items-center gap-4 mt-2" key={index}>
            <div className="daisy-skeleton w-8 h-8 rounded-full object-cover bg-gray-300"></div>
            <span className="group-hover:text-white transition-all duration-200 ease-in-out font-inter font-medium text-mobile-14 capitalize">
              <div className="daisy-skeleton h-4 w-20 bg-gray-300"></div>
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AwardList;

// const ProjectSkeleton = () => {
//   return (
//     <div className="group transition-all duration-200 ease-in-out flex flex-col w-full md:flex-row pl-4 md:px-4 md:pl-4 md:pr-14 md:justify-between py-6 ">
//       <span className="group-hover:text-white transition-all duration-200 ease-in-out md:w-3/4 font-inter font-medium text-mobile-16">
//         <div className="daisy-skeleton bg-gray-300 h-4 w-20"></div>
//       </span>

//       <div className="flex flex-col gap-2 md:w-1/4 ">
//         {[...Array(1)].map((_, index) => (
//           <div className="flex flex-row items-center gap-4 mt-2" key={index}>
//             <div className="daisy-skeleton w-8 h-8 rounded-full object-cover bg-gray-300"></div>
//             <span className="group-hover:text-white transition-all duration-200 ease-in-out font-inter font-medium text-mobile-14 capitalize">
//               <div className="daisy-skeleton h-4 w-20 bg-gray-300"></div>
//             </span>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };
