import { ParticipantSession } from "@modules/auth";
import { Judge } from "@modules/judge";
import { createContext } from "react";
export const AdminContext = createContext({
  projects: [],
  judges: [] as Judge[],
  participants: [] as Partial<ParticipantSession>[],
  setParticipants: (participants: any) => {},
  page: "status",
  setPage: (page: string) => {},
  rerender: false,
  setRerender: (rerender: boolean) => {},
  assignedJudges: [] as Judge[],
  setAssignedJudges: (judges: Judge[]) => {},
  evaluatedJudges: [] as Judge[],
  setEvaluatedJudges: (judges: Judge[]) => {},
});