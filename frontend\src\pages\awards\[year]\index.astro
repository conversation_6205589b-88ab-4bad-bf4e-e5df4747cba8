---
import Layout from "@layouts/sideLayout.astro";
import { categories } from "../category";
import { proxyApi } from "@/utils";

const { year: yearSlug } = Astro.params;

export const prerender = true;

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const YEAR_FILTER_TYPE = [];
const adjustedYear = isPostEvent
  ? parseInt(currentYear)
  : parseInt(currentYear) - 1;
for (let year = 2023; year <= adjustedYear; year++) {
  YEAR_FILTER_TYPE.push(year.toString());
}

if (!isPostEvent && yearSlug == currentYear) {
  return Astro.redirect("/awards/" + (parseInt(currentYear) - 1));
}

export function getStaticPaths() {
  const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
  const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

  const YEAR_FILTER_TYPE = [];
  const adjustedYear = isPostEvent
    ? parseInt(currentYear)
    : parseInt(currentYear) - 1;

  console.log(adjustedYear);

  for (let year = 2023; year <= adjustedYear; year++) {
    YEAR_FILTER_TYPE.push(year.toString());
  }

  return YEAR_FILTER_TYPE.map((year) => ({ params: { year } }));
}

const test = await proxyApi.testBackend();
if (test !== "Connected to PIXEL Backend") {
  throw new Error("Backend not connected to " + Astro.url.origin);
}

let offset = null;
let data = [];
do {
  const result = await proxyApi.get(
    "projects",
    {
      fields: "project_major, awards, year",
      filterByFormula: `AND(year="${yearSlug}", awards!="")`,
      offset,
    },
    true
  );

  if (result.offset) data = data.concat(result.data);
  else {
    data = data.concat(result);
    break;
  }

  offset = result.offset;
} while (offset);

// count number of projects for majors and awards
let majorCount = {};
let bestPresenterCount = 0;

data.forEach((record) => {
  let major = record.project_major;
  let awards = record.awards;

  if (major) {
    if (majorCount[major]) {
      majorCount[major]++;
    } else {
      majorCount[major] = 1;
    }
  }

  if (awards && awards.includes("Best Presenter")) {
    bestPresenterCount++;
  }
});

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: yearSlug + " Winners", path: `/awards/${yearSlug}` },
];
---

<Layout
  title=`${yearSlug} Winners`
  pageDesc=`The top-tier projects of PIXEL ${yearSlug}, each reflecting an exceptional level of excellence.`
  {breadcrumbs}
>
  <div
    class="flex max-md:flex-wrap max-md:gap-y-4 gap-x-2 md:gap-x-5 *:leading-tight md:*:leading-none *:align-text-bottom justify-center items-center *:text-center [&>button]:py-1 [&>button]:px-2 [&>button]:rounded-[4px]"
    slot="menu"
  >
    {
      YEAR_FILTER_TYPE.map((year: string, index) => (
        <button
          type="submit"
          class={`group hover:bg-primary-6  ${year === yearSlug ? "*:text-white bg-primary-6 disabled" : "cursor-pointer"} max-w-[14ch] font-semibold text-center transition-all ease-in-out *:font-medium *:text-mobile-16`}
        >
          <a href={`/awards/${year}`} class="group-hover:text-white">
            {year}
          </a>
        </button>
      ))
    }
  </div>

  <section
    class="mt-16 lg:mt-24 flex flex-col md:grid md:grid-cols-2 gap-9 w-full"
  >
    {
      categories.map((category) => (
        <a
          class="flex flex-row max-md:items-center w-full gap-4 p-6 justify-between bg-white shadow-md hover:shadow-lg md:flex-col rounded-lg hover:border-black hover:scale-105  transition-all ease-in-out duration-200"
          href={`/awards/${yearSlug}/${category.name}`}
        >
          <div class="flex max-md:items-center md:flex-col gap-4 max-md:h-14">
            <i
              class={`${category.icon} text-mobile-24 lg:text-5xl md:w-fit p-4 bg-grey-1 rounded-lg`}
            />
            <div class="flex flex-col gap-1">
              <span class="max-md:hidden block font-inter font-semibold text-tablet-14">
                {category.name === "Best Presenter" ? "Award" : "Major"}
              </span>
              <span class="font-poppins font-semibold text-mobile-24 md:text-tablet-34 lg:text-desktop-48 max-[15ch]sm:max-md:whitespace-nowrap md:max-w-[10ch]">
                {category.name}
              </span>
            </div>
          </div>
          <div class="md:flex md:flex-row md:items-end md:space-x-2">
            <span class="font-medium text-mobile-14 md:text-tablet-34 text-left">
              {category.name === "Best Presenter"
                ? bestPresenterCount
                : !!majorCount[category.name]
                  ? majorCount[category.name]
                  : 0}
            </span>
            <span class="max-md:hidden font-inter font-semibold text-tablet-16">
              {category.name === "Best Presenter"
                ? bestPresenterCount > 1
                  ? "Projects"
                  : "Project"
                : "Projects"}
            </span>
          </div>
        </a>
      ))
    }
  </section>
</Layout>
