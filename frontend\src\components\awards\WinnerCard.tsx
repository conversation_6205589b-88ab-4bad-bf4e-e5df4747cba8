import { useEffect, useState } from "react";
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";
import ProjectModal from "@components/projects/ProjectModal";
import { Project } from "@modules/project/types";
import ReactDOM from "react-dom";

interface Props {
  project: Project;
  award_title: string;
  horizontal?: boolean;
}

const WinnerCard = ({ project, award_title, horizontal = false }: Props) => {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  const showProject = () => {
    setIsOpen(true);
    document.body.style.overflow  = "hidden";
  };

  const closeProject = () => {
    setIsOpen(false);
    document.body.style.overflow  = "auto";
  }

  useEffect(() => {
    if (!!project) {
      setLoading(false);
    }
  }, []);

  return (
    <>
      {loading ? (
        <ProjectSkeleton horizontal={horizontal} award_title={award_title} />
      ) : (
        <>
          {isOpen && (
            <ProjectModal id={project.id} >
              <ProjectSubmissionView project={project} onClose={closeProject} />
            </ProjectModal>
          )}
          <div
            className={`w-full h-full cursor-pointer ${
              horizontal ? "md:w-full" : "md:w-1/2"
            } `}
            onClick={showProject}
          >
            {award_title !== "BEST PRESENTER" && (
              <h2 className="w-full font-poppins font-semibold text-mobile-34 md:text-tablet-48 lg:text-desktop-60  text-center">
                {award_title}
              </h2>
            )}
            <div
              className={`flex mt-10 flex-col rounded-md border-2 border-black overflow-clip hover:scale-105 transition-all ease-in-out
                ${
                  horizontal
                    ? "min-h-[calc(229px + 240px)] md:min-h-[338px] md:flex-row lg:h-[400px]"
                    : "min-h-[calc(228px + 229px)] md:min-h-[calc(231px+226px)]"
                }`}
            >
              {/* card image */}
              <img
                src={project.thumbnail}
                alt={project.project_short_name ?? project.project_name + " thumbnail"}
                className={`bg-black border-black min-h-[229px] w-full ${
                  horizontal ? "md:w-1/2 border-r-2" : "border-b-2"
                } object-cover`}
              />

              {/* card body */}
              <div
                className={`${
                  horizontal ? "md:w-1/2" : ""
                } h-full w-full p-6 flex flex-col justify-between`}
              >
                <div className="flex flex-col gap-2 lg:gap-4">
                  <p
                    className={`!font-semibold  min-h-[52px] text-mobile-18 ${
                      award_title === "BEST PROJECT"
                        ? "md:text-tablet-24 lg:text-desktop-28"
                        : "md:text-tablet-20 lg:text-desktop-20"
                    }`}
                  >
                    {project.project_short_name ?? project.project_name}
                  </p>
                  {/* <input
                    id="showMore"
                    type="checkbox"
                    className="hidden peer/showMore"
                  /> */}
                  <p
                    className={`text-mobile-16 line-clamp-[6] ${
                      award_title === "BEST PROJECT" ||
                      award_title === "BEST PRESENTER"
                        ? " lg:line-clamp-[8]"
                        : "lg:line-clamp-4"
                    } `}
                  >
                    {project.project_desc}
                  </p>
                </div>

                <div className="flex flex-col">
                  <hr className="w-full border-black/40 my-6" />
                  <div className="flex flex-col lg:flex-row justify-between gap-3">
                    {project.team.members_name ? (
                      !(project.team.members_name.length > 1) ? (
                        <div className="flex flex-col gap-2 lg:w-2/5">
                          {project.team.members_name.map((name, index) => (
                            <div
                              key={index}
                              className="flex flex-row items-center gap-4"
                            >
                              <img
                                src={project.team.members_photo[index]}
                                alt={name.toLowerCase()}
                                width="32"
                                height="32"
                                className="bg-black h-8 w-8 rounded-full object-cover"
                              />
                              <span className="font-medium text-mobile-14 !capitalize w-fit">
                                {name.toLowerCase()}
                              </span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="flex flex-row items-center gap-4">
                          {project.team.members_name.map((name, index) => (
                            <img
                              key={index}
                              src={project.team.members_photo[index]}
                              alt={name.toLowerCase()}
                              width="32"
                              height="32"
                              className="bg-black h-8 w-8 rounded-full object-cover"
                            />
                          ))}
                        </div>
                      )
                    ) : null}

                    <div className="flex flex-col lg:flex-col md:gap-1 w-fit max-w-[20ch] lg:text-right">
                      <span className="font-medium  text-mobile-14">
                        supervised by:{" "}
                      </span>
                      <span className=" text-mobile-14 md:h-[36.4px]">
                        {project.supervisor.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default WinnerCard;

const ProjectSkeleton = ({ horizontal, award_title }) => {
  return (
    <>
      <div
        className={`w-full h-full  ${horizontal ? "md:w-full" : "md:w-1/2"} `}
      >
        {award_title !== "BEST PRESENTER" && (
          <div className="mx-auto daisy-skeleton h-6 w-60 bg-gray-300"></div>
        )}
        <div
          className={`flex mt-10 flex-col rounded-md border-2 border-black overflow-clip transition-all ease-in-out
                ${
                  horizontal
                    ? "min-h-[calc(229px + 240px)] md:min-h-[338px] md:flex-row lg:h-[400px]"
                    : "min-h-[calc(228px + 229px)] md:min-h-[calc(231px+226px)]"
                }`}
        >
          {/* card image */}
          <div
            className={`daisy-skeleton rounded-none h-4 bg-gray-300 min-h-[229px] w-full ${
              horizontal ? "md:w-1/2 border-r-2 h-full" : "border-b-2"
            }`}
          ></div>

          {/* card body */}
          <div
            className={`${
              horizontal ? "md:w-1/2" : ""
            } h-full w-full p-6 flex flex-col justify-between`}
          >
            <div className="flex flex-col gap-2 lg:gap-4">
              {/* project name */}
              <div className="daisy-skeleton h-4 w-40 bg-gray-300"></div>
              {/* content */}
              <div className="mt-10 daisy-skeleton h-4 w-full bg-gray-300"></div>
              <div className="daisy-skeleton h-4 w-full bg-gray-300"></div>
              <div className="daisy-skeleton h-4 w-full bg-gray-300"></div>
            </div>

            <div className="flex flex-col">
              <hr className="w-full border-black/40 my-6" />
              <div className="flex flex-col md:items-center lg:flex-row justify-between gap-3">
                {/* profile */}
                <div className="daisy-skeleton size-8 bg-gray-300"></div>
                {/* supervised by */}
                <div className="daisy-skeleton h-4 w-20 bg-gray-300"></div>
                <div className="daisy-skeleton h-4 w-32 bg-gray-300"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
