function generateJobApplicationsSheet() {
  const studentSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName("fyp_students");
  const jobSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName("job_lists");
  const applicationSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName("job_applications") || PUBLIC_PIXEL_SPREADSHEET.insertSheet("job_applications");

  // Clear previous applications
  applicationSheet.clearContents();

  // Headers for applications
  const headers = [
    "job_applications_id", "student_name", "matric_no", "student_major", "desc", "student_email", "personal_email",
    "github", "linkedin", "portfolio", "tel", "discord_id", "resume_specific",
    "job_id", "company", "job_title", "job_type", "work_mode"
  ];
  applicationSheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  const studentData = studentSheet.getDataRange().getValues();
  const jobData = jobSheet.getDataRange().getValues();
  // const jobHeaders = jobData.shift(); // remove header row

  const jobMap = {};
  jobData.forEach(row => {
    const job_id = row[0];
    jobMap[job_id] = row;
  });

  const applications = [];

  for (let i = 1; i < studentData.length; i++) {
    const row = studentData[i];
    const [
      id, student_name, display_name, matric_no, student_major, ic, desc, photo, student_email,
      personal_email, github, linkedin, portfolio, tel, discord_id, created_at,
      resume, resumes_applied, job_ids_applied
    ] = row;

    if (!job_ids_applied) continue;

    const jobIds = job_ids_applied.toString().split(";").map(s => s.trim());
    const resumes = resumes_applied ? resumes_applied.toString().split(";").map(s => s.trim()) : [];

    for (let j = 0; j < jobIds.length; j++) {
      const jobId = jobIds[j];
      const job = jobMap[jobId];
      if (!job) continue;

      const resume_specific = resumes[j] || resume;
      const job_applications_id = `${matric_no}_${jobId}`;
      applications.push([
        job_applications_id, student_name, matric_no, student_major, desc, student_email, personal_email,
        github, linkedin, portfolio, tel, discord_id, resume_specific,
        jobId, job[1], job[2], job[3], job[4]
      ]);
    }
  }

  // Write to sheet
  if (applications.length > 0) {
    applicationSheet.getRange(2, 1, applications.length, headers.length).setValues(applications);
  }
}
