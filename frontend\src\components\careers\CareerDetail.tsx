import type { Job } from "@/modules";
import type React from "react";

type Props = {
  job: Job;
};

const CareerDetail: React.FC<Props> = ({ job }) => {
  return (
    <div className="px-6 py-5 space-y-6 text-gray-800">
      <div>
        <h2 className="text-lg font-semibold mb-2">Job Description</h2>
        <p className="whitespace-pre-line leading-relaxed text-sm md:text-base">{job?.description}</p>
      </div>

      {job?.highlights && (
        <div>
          <h2 className="text-lg font-semibold mb-2">Job Highlights</h2>
          <p className="whitespace-pre-line leading-relaxed text-sm md:text-base">{job?.highlights}</p>
        </div>
      )}

      <div>
        <h2 className="text-lg font-semibold mb-2">Job Requirements</h2>
        <p className="whitespace-pre-line leading-relaxed text-sm md:text-base">{job?.requirements}</p>
      </div>
    </div>
  );
};

export default CareerDetail;
