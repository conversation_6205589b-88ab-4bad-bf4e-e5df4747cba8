import type { Judge } from "./model";
import { proxyApi } from "@/utils/proxyApi";
import { teamFactory } from "../team";
import { EvaluationData, Project } from "..";
import { widget } from "@utils/widget";
import { actionSessionStorage } from "@utils/utils";
import { mapProject } from "@utils/mapper";

export function judgeFactory() {
  /**
   * Get judge data from airtable using firebase_uid when user logs in
   * @param {string} firebaseUid - firebase uid of the judge
   * @returns {Promise<Judge>} judge data
   */
  async function getJudge(firebaseUid: string, year?: string): Promise<Judge> {
    try {
      const data = await proxyApi.get("judges", {
        filterByFormula: `firebase_uid="${firebaseUid}"`,
      });

      const getAirTableID = data[0].id;

      const judgeData = await proxyApi.first("judges", getAirTableID);

      // Filter assigned_projects_id by assigned_projects_year === currentYear
      if (
        judgeData.assigned_projects_id &&
        judgeData.assigned_projects_year &&
        year &&
        Array.isArray(judgeData.assigned_projects_id) &&
        Array.isArray(judgeData.assigned_projects_year)
      ) {
        const filteredProjects = judgeData.assigned_projects_id.filter(
          (_: string, idx: number) =>
            judgeData.assigned_projects_year[idx] === year
        );
        judgeData.assigned_projects_id = filteredProjects;
      }

      return judgeData;
    } catch (error) {
      widget.alertError("Error in getJudge: ", error);
      throw new Error("Error in getJudge: " + error);
    }
  }

  /**
   * Get projects assigned to a judge
   * @param {string} judgeId - firebase_uid of the judge
   * @returns {Promise<Project[]>} - projects assigned to the judge
   */
  async function getAssignedProjects(
    judgeId: string,
    year?: string
  ): Promise<Project[]> {
    const filters: string[] = [`FIND("${judgeId}", {assigned_judges_id})`];

    if (year) {
      filters.push(`{year}="${year}"`);
    }

    const filterFormula =
      filters.length > 1 ? `AND(${filters.join(",")})` : filters[0];

    let offset = null;
    let allRecords: Project[] = [];

    try {
      // const data = await proxyApi.get("projects", {
      //   filterByFormula: filterFormula,
      // });

      // return data;

      do {
        const response = (await proxyApi.get("projects", {
          filterByFormula: filterFormula,
          offset: offset,
        })) as { data: Project[]; offset?: string };

        // If response is an array, just concat it directly
        if (Array.isArray(response)) {
          // if result.offset has value, meaning to say there are more records to fetch
          allRecords = allRecords.concat(response);
        } else if (Array.isArray(response.data)) {
          allRecords = allRecords.concat(response.data);
        } else {
          // no more records to fetch after this
          allRecords = allRecords.concat(response.data);
          break;
        }

        offset = response.offset;
      } while (offset);
    } catch (error) {
      widget.alertError("Error in getAssignedProjects: ", error);
      throw new Error("Error in getAssignedProjects: " + error);
    }

    return (allRecords as any).map(
      (response: any) => mapProject(response) as Project
    );
  }

  /**
   * Get evaluation data of a judge for a project
   * @param {string} projectId - project id
   * @param {string} firebaseUid - firebase_uid of the judge
   * @param {string} projectName - project name
   * @returns {Promise<EvaluationData>} - evaluation data of the judge for the project
   */
  async function getEvaluation(
    projectId: string,
    firebaseUid: string,
    projectName: string
  ): Promise<EvaluationData> {
    try {
      const judgeData = (await proxyApi.get("judges", {
        filterByFormula: `firebase_uid="${firebaseUid}"`,
      })) as Judge;

      // Check if judge is assigned to the project based on the project id
      if (
        !judgeData ||
        !judgeData[0].assigned_projects_id.includes(projectId)
      ) {
        widget.alertError("Error", "You are not assigned to this project");
        return;
      }

      // If the judge is assigned to the project, fetch the existing evaluation data
      const response = (await proxyApi.get("evaluations", {
        filterByFormula: `AND(judge_id = "${firebaseUid}", project_name = "${projectName}")`,
      })) as EvaluationData[];
      if (!response[0]) return;

      return response[0];
    } catch (error) {
      throw new Error("Error in getEvaluation: " + error);
    }
  }

  /**
   * Submit evaluation data of a judge for a project
   * @param {string} projectId - project id
   * @param {string} judgeId - airtable id of the judge
   * @param {EvaluationData} evaluationData - evaluation data of the judge for the project
   * @param {string} projectName - project name
   * @param {string} projectYear - project year
   */
  async function evaluateProject(
    projectId: string,
    judgeId: string,
    evaluationData: any,
    projectName: string,
    projectYear: string
  ): Promise<void> {
    const createEvaluationDetails: EvaluationData = {
      judge_id: [judgeId],
      project_id: [projectId],
      ...evaluationData,
    };

    const evaluation = await getEvaluation(projectId, judgeId, projectName);
    let evaluationId = evaluation?.id;

    try {
      if (!evaluationId) {
        // if the evaluation record does not exist, create a new record
        let response = (await proxyApi.post(
          "evaluations",
          createEvaluationDetails,
          true
        )) as EvaluationData;

        evaluationId = response.id;

        const judgeSession = actionSessionStorage().get("judgeData");
        // console.log("🚀 ~ judgeFactory ~ judgeSession:", judgeSession);

        // console.log(evaluationId)

        const updatedEvaluationId = judgeSession?.evaluation_id;
        updatedEvaluationId?.push(evaluationId);
        // console.log(updatedEvaluationId)

        const updatedEvaluatedProjectsId = judgeSession?.evaluated_projects_id;
        const updatedEvaluatedProjectsName = judgeSession?.evaluated_projects_name;
        const updatedEvaluatedProjectsYear = judgeSession?.evaluated_projects_year;

        if (!judgeSession?.evaluated_projects_id) {
          judgeSession.evaluate;
        } else {
        }
        // console.log("🚀 ~ judgeFactory ~ judgeSession?.evaluated_projects_id:", judgeSession?.evaluated_projects_id)
        // console.log("🚀 ~ judgeFactory ~ updatedEvaluatedProjectsId:", updatedEvaluatedProjectsId)

        updatedEvaluatedProjectsId?.push(projectId);
        updatedEvaluatedProjectsName?.push(projectName);
        updatedEvaluatedProjectsYear?.push(projectYear);

        await actionSessionStorage().update("judgeData", {
          ...judgeSession,
          evaluation_id: updatedEvaluationId,
          evaluated_projects_id: updatedEvaluatedProjectsId,
          evaluated_projects_name: updatedEvaluatedProjectsName,
          evaluated_projects_year: updatedEvaluatedProjectsYear,
        });

        widget
          .alertSuccess("Success", "Evaluation submitted successfully!", 3000)
          .then(() => {
            window.location.href = "/judge";
          });
      } else {
        // if the evaluation record exists, update the existing record
        await proxyApi.patch("evaluations", evaluationData, evaluationId, true);

        widget
          .alertSuccess("Success", "Evaluation submitted successfully!", 3000)
          .then(() => {
            window.location.href = "/judge";
          });
      }
    } catch (error) {
      throw new Error("Error in evaluateProject: " + error);
    }
  }

  return {
    getAssignedProjects,
    getJudge,
    getEvaluation,
    evaluateProject,
  };
}
