---
const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const menuList = [
  {
    name: "WINNERS", // show after isPostEvent == true
    link: "/awards",
  },
  {
    name: "PROJECTS", // show after isPostSubmission == true
    link: "/projects",
  },
  {
    name: "HOME",
    link: "/",
  },
  {
    name: "ABOUT",
    link: "/about-us",
  },
  {
    name: "JUDGES",
    link: "/judges",
  },
  {
    name: "PARTNERS",
    link: "/partners",
  },
  {
    name: "PAST WINNERS", // show when isPostEvent == false
    link: "/awards", 
  },
  {
    name: "FAQS",
    link: "/faq",
  },
  {
    name: "CAREERS",
    link: "/careers"
  },
  {
    name: "TERMS",
    link: "/terms",
  },

];

// const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isPostSubmission = import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";
const isOfficialLaunch = import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";
---
<div class = "flex gap-4 cursor-pointer" >
  {
    isOfficialLaunch &&
    <a href ="/sign-in" class = " faderight z-50 hover:text-primary-6 transition  duration-300  text-base bg-white shadow-lg rounded-full px-4 md:px-6 py-2 md:py-3 m-auto hidden whitespace-nowrap" id = "signinbtn">Sign In</a>
  }
  

  <button id="menu" type="button" class="w-auto relative bg-white transition duration-100 ease-in-out z-20 rounded-full shadow-lg p-2 md:p-3  flex justify-center items-center cursor-pointer group">
    <div class="size-8 lg:size-6 flex justify-center items-center">
      <i id="menu-icon" class="fa-solid fa-bars text-lg group-hover:text-primary-6 transition"></i>
    </div>
  </button>
</div>

<!-- Modal window for navbar -->
<div
  class="fixed w-full h-0 bg-black left-0 bottom-0 transition-all duration-500"
  id="backnavbar"
>
</div>
<div
  class="fixed w-full h-0 bg-white left-0 bottom-0 transition-all duration-500 z-50"
  id="transitionnavbar"
>
</div>


<div class="fixed w-full h-[100dvh]  bg-white left-0 bottom-0 closeModal" id="navbarmodal">
  <!-- to take up navbar space -->
  <div class="py-10" />
  <div class="flex flex-col justify-center items-center h-[calc(100dvh-80px)] w-full lg:py-10 px-4 md:px-5 xl:px-0 container-max-w mx-auto gap-10">
    <!-- Navbar content -->
    <div
      class="w-full flex md:flex-row gap-2 md:gap-10 md:justify-normal md:items-center items-stretch h-[85%]"
    >

      <!-- LEFT SIDE -->
      <div
        class="max-lg:hidden md:w-[50%] flex flex-col gap-5 md:gap-0 navbarleft self-stretch "
      >
        <a
            href={(isPostSubmission || isPostEvent) ? `/projects?year=${currentYear}` : `/projects?year=${parseInt(currentYear) - 1}`}
          class="flex justify-center items-center bg-primary-6 border-2 border-b-0 border-black transition ease-linear w-full min-h-[50%]"
        >
          <span class="largeMenuText text-white -rotate-[4deg] tracking-tight text-[80px] lg:text-[95px] leading-none font-poppins font-bold text-center">{(isPostSubmission || isPostEvent) ? `${currentYear} PROJECTS` : `${parseInt(currentYear) - 1} PROJECTS`}</span>
        </a>
        <a
          href="/awards"
          class="flex justify-center items-center bg-primary-2 border-2 border-black transition ease-linear w-full min-h-[50%]"
        >
          <span class="largeMenuText text-black -rotate-[4deg] tracking-tight text-[80px] lg:text-[95px] leading-none font-poppins font-bold text-center">{isPostEvent ? `${currentYear} WINNERS` : `${parseInt(currentYear) - 1} WINNERS`}</span>
        </a>
      </div>

      <!-- RIGHT SIDE -->
      <div
        class="flex flex-col h-full lg:justify-around navbarright"
      >
        <div class="flex md:gap-10 h-full w-full">
          <div
            class="h-full flex flex-col justify-center w-full lg:*:w-fit"
          >
            {menuList.filter((menu) => isPostEvent ? menu.name !== "PAST WINNERS" : !["WINNERS", "PROJECTS"].includes(menu.name)).map((menu) => (
              !import.meta.env.PUBLIC_BLOCKPATH.split(',').includes(menu.link) &&
              <a href={menu.link} class={`${["WINNERS", "PROJECTS"].includes(menu.name) ? "lg:hidden text-primary-3 hover:text-primary-6" : ""} menuText text-grey-2 hover:text-black transition duration-300 ease-in-out font-poppins font-semibold leading-none tracking-tight text-mobile-48 md:text-[72px]`}>{menu.name}</a>
            ))}
          </div>
        </div>
      </div>
    </div>




    <!-- Navbar footer -->
    <div class="flex items-center justify-between w-full">
      <p class="font-semibold">PIXEL {currentYear}</p>
      <div
        class="flex gap-5"
      >
        <a href="https://www.instagram.com/pixel.usm/" aria-label ="instagram" target="_blank"
          ><i class="fa-brands fa-instagram text-grey-3 hover:text-primary-6 transition duration-200 text-2xl md:text-3xl"
          ></i></a
        >
        <a href="https://www.facebook.com/usm.pixel" target="_blank" aria-label ="facebook"
          ><i
            class="fa-brands fa-square-facebook text-grey-3 hover:text-primary-6 transition duration-200 text-2xl md:text-3xl"
          ></i></a
        >
        <a href="https://www.linkedin.com/company/pixel-usm/" target="_blank" aria-label ="linkedin"
          ><i class="fa-brands fa-linkedin text-grey-3 hover:text-primary-6 transition duration-200 text-2xl md:text-3xl"
          ></i></a
        >
      </div>
    </div>
  </div>

  <script>
    import gsap from 'gsap';

    const menu = document.getElementById("menu") as HTMLElement;
    const menuIcon = document.getElementById("menu-icon") as HTMLElement;
    const close = document.getElementById("close") as HTMLElement;
    const backnavbar = document.getElementById("backnavbar") as HTMLElement;
    const transitionnavbar = document.getElementById("transitionnavbar") as HTMLElement;
    const modal = document.getElementById("navbarmodal") as HTMLElement;
    const signinbtn = document.getElementById("signinbtn") as HTMLElement;

    //get every a tag in the navbar modal and show animation if click
    // const navlinks = document.querySelectorAll("#navbarmodal a");
    // console.log(navlinks);
    // navlinks.forEach((link) => {
    //   link.addEventListener("click", () => {
    //     transitionnavbar.classList.toggle("runbacknavbar");
    //     transitionnavbar.classList.toggle("closebacknavbar");
    //     setTimeout( function() { window.location = link.getAttribute("data-href")}, 500 );
    //   });
    // });

    menu.addEventListener("click", function () {
      backnavbar.classList.toggle("runbacknavbar");
      // if navbar is open then close
      if (modal.classList.contains("close")) {
        modal.classList.toggle("close");
        modal.classList.toggle("openModal");

        menuIcon.classList.remove("fa-xmark" );
        menuIcon.classList.add("fa-bars");
      } else {
        // if modal is close then open after certain time for animation
        
        menu.classList.toggle("open");

        menuIcon.classList.remove("fa-bars");
        menuIcon.classList.add("fa-xmark" );

        setTimeout(function () {
          modal.classList.toggle("close");
          modal.classList.toggle("openModal");

          const tl = gsap
            .timeline({ defaults: { duration: 0.3, ease: "power2.inOut" } })
            .from(".navbarleft", {
              duration: 0.5,
              delay: 0.25,
              y: 100,
              opacity: 0,
              stagger: 0.05,
              skewY: 5,
              ease: "power4.Out",
            });
          // make gsap animation for the class to make it move from bottom to top
          const righttl = gsap
            .timeline({ defaults: { duration: 0.3, ease: "power2.inOut" } })
            .from(".navbarright", {
              duration: 0.35,
              delay: 0.28,
              y: 100,
              opacity: 0,
              stagger: 0.05,
              skewY: 5,
              ease: "power4.Out",
            });
        }, 400);
      }

      //add animation to backnavbar after certain time to show more animation when open/close
      setTimeout(function () {
        backnavbar.classList.toggle("runbacknavbar");
        backnavbar.classList.toggle("closebacknavbar");
      }, 200);

      if(signinbtn.classList.contains("hidden")) {
        signinbtn.classList.toggle("faderight");
        // go in 
        setTimeout(() => {
          signinbtn.classList.toggle("hidden");
          signinbtn.classList.toggle("fadeleft");


        }, 600);
      }else {
        // go out
        signinbtn.classList.toggle("fadeleft");
        signinbtn.classList.toggle("faderight");
        setTimeout(() => {
          signinbtn.classList.toggle("hidden");
        }, 100);
      }
    });
  </script>

  <style is:inline>
    @media (max-height: 640px) {
      .menuText {
        font-size: 60px;
      }

      .largeMenuText {
        font-size: 80px;
      }
    }

    a {
      cursor: pointer;
      overflow:hidden;
    }

    .runbacknavbar {
      transition: height 0.35s ease;
      height: 0%;
    }

    .closebacknavbar {
      height: 100%;
      transition: height 0.35s ease;
    }

    .closeModal {
      height: 0%;
      transition: all 0.5s ease;
    }
    .openModal {
      height: 100%;
      transition: all 0.5s ease;
    }

    .transitionnavbar {
      height:100%
      transition: all 0.5s ease;
    }

    .fadeleft {
      opacity: 0;
      transform: translateX(20px);
      animation: fadeleft 0.5s ease forwards;
    }

    .faderight  {
      opacity: 1;
      transform: translateX(0px);
      animation: faderight 1s ease forwards;
    }

    @keyframes fadeleft {
      to {
        opacity: 1;
        transform: translateX(0px);
      }
    }
    @keyframes faderight {
      to {
        opacity: 0;
        transform: translateX(20px);
      }
    }
  </style>
</div>
