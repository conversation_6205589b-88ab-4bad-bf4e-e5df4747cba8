import type { ParticipantInfo } from "@/modules";
import { useEffect, useState, useId, useRef, ChangeEvent } from "react";
import { authFactory, participantFactory, Participant } from "@/modules";
import { widget, auth, actionSessionStorage, googleDriveUpload } from "@/utils";
import * as Yup from "yup";
import { onAuthStateChanged } from "firebase/auth";
// import BaseModal from "@components/general/BaseModal";
import ProfileModal from "@components/general/ProfileModal";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const resumeFolderId = import.meta.env.PUBLIC_GDRIVE_RESUME_FOLDER;

const Register: React.FC = () => {
  const profileModalRef = useRef<any>();
  const [userUid, setUserUid] = useState<any>();

  const [userEmail, setUserEmail] = useState<string>();
  const [name, setName] = useState<string>();
  const [displayName, setDisplayName] = useState<string>();
  const [matric_no, setMatricNo] = useState<string>();
  const [major, setMajor] = useState<string>();
  const [aboutMe, setAboutMe] = useState<string>();
  const [linkedIn, setLinkedIn] = useState<string>();
  const [github, setGithub] = useState<string>();
  const [portfolio, setPortfolio] = useState<string>();
  const [textCount, setTextCount] = useState(0);
  const [discordID, setDiscordID] = useState<string>();
  const [personalEmail, setPersonalEmail] = useState<string>();
  const [IC, setIC] = useState<string>();
  const [Tel, setTel] = useState<string>();
  const [resume, setResume] = useState<string>();
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [memberId, setMemberId] = useState("");

  const [profileImage, setProfileImage] = useState<any>(null);
  const [imageDetail, setImageDetail] = useState<any>({
    src: "https://www.w3schools.com/howto/img_avatar.png",
    alt: "Avatar",
  });

  // This useEffect is not meant for handle the registration
  // but to redirect the user to the correct page if they've already
  // registered themselves before this or if they have reached some state
  useEffect(() => {
    // user here is the return object from the firebase authentication method, onAuthStateChanged
    // if the user is logged in, then the user object will be returned
    // if the user is not logged in, then the user object will be null
    // used as flag

    const unsub = onAuthStateChanged(auth, (user) => {
      // if user is not logged in, redirect to sign-in page
      if (!user) {
        widget
          .alertError(
            "You are not logged in",
            "Please login to continue.",
            undefined,
            3000
          )
          .then(() => {
            window.location.href = "/sign-in";
          });
      }

      setUserEmail(user.email!);
      setUserUid(user.uid); 

      // if participant has logged in to dashboard before, redirect to participant dashboard
      const participantSessionData = actionSessionStorage().get("userData");
      if (participantSessionData) {
        widget
          .alert({
            title: "You have logged in",
            text: "Sign out to switch account. Redirecting to participant dashboard ...",
            icon: "info",
          })
          .then(() => {
            window.location.href = "/participant";
          });
      }

      (async () => {
        if (user) {
          // check if the user has verified their email
          //uncomment this to skip email verification
          if (!user.emailVerified) {
            widget
              .alertError(
                "Email not verified",
                "Please verify your email before proceeding."
              )
              .then(() => {
                window.location.href = "/sign-in";
              });
          } else {
            // get participant data from airtable based on the firebase uid to check if the user has registered before
            const participantData: Participant =
              await participantFactory().getParticipant(user.uid as string);

            // if data present in airtable, then the user has registered before, redirect to participant dashboard
            // if data not present in airtable, then the user has not registered before, proceed with registration
            if (participantData) {
              widget
                .alertError(
                  "You have registered before",
                  "You may log in to your account.",
                  undefined,
                  3000
                )
                .then(() => {
                  window.location.href = "/sign-in";
                });
            }
          }
        }
      })();
    });

    return () => {
      unsub();
    };
  }, []);

  // This one baru in charge of the submitting the registration information.
  const handleSubmit = async (e: any) => {
    e.preventDefault();

    let registerInfo: Partial<ParticipantInfo> & { agreement: boolean } = {
      firebase_uid: userUid,
      student_name: e.target.name.value,
      display_name: displayName,
      matric_no: e.target.matric_no.value,
      student_major: major,
      desc: aboutMe,
      photo: profileImage, // in the form of url
      agreement: e.target.agreement.checked,
      student_email: userEmail,
      github: github,
      linkedin: linkedIn,
      portfolio: portfolio,
      resume: resume,
      resume_agreement: e.target.resume_agreement.checked,
      year: currentYear,
      // created_at: new Date().toISOString(),
      // updated_at: new Date().toISOString(),
      created_at: new Date(),
      updated_at: new Date(),

      // Uncomment this after you have added the following inputs in the code
      // left is the name of the input field, right is the state variable
      ic: IC,
      tel: Tel,
      photo_agreement: e.target.photo_agreement.checked,
      discord_id: discordID,
      personal_email: personalEmail,
    };

    // registerSchema is a Yup validation schema that used to define and validate how the expected data structure should look like
    let registerSchema = Yup.object().shape<
      Record<
        // Create the Type Record using the type ParticipantInfo, but excluding the type of airtable_id and project_id
        keyof Omit<ParticipantInfo, "id" | "project_id"> & {
          agreement: boolean;
        },
        Yup.AnySchema
      >
    >({
      student_email: Yup.string()
        .email("Please enter a valid email")
        .trim()
        .required(),
      student_name: Yup.string().trim().required("Please enter your full name"),
      display_name: Yup.string()
        .trim()
        .required("Please enter your display name"),
      matric_no: Yup.number()
        .required("Please enter your matric number")
        .typeError("Please enter a valid matric number"),
      student_major: Yup.string()
        .oneOf(
          [
            "Software Engineering",
            "Intelligent Computing",
            "Computing Infrastructure",
          ],
          "Please select the proper major"
        )
        .required(),
      ic: Yup.string()
        .trim()
        // .required("Please enter your IC number")
        .matches(
          /^[a-zA-Z0-9]*$/,
          "No special characters are allowed for the IC number"
        ),
      tel: Yup.string()
        .matches(
          /^[0-9]{3,15}$/g,
          "Please enter a valid phone number. Example: 0123456789"
        )
        .trim()
        .required(),
      personal_email: Yup.string()
        .email("Please enter a valid personal email address")
        .trim()
        .required(),
      github: Yup.string()
        .matches(
          /^https:\/\/github\.com\/[a-zA-Z0-9-]+\/?$/,
          "Invalid URL, please use a proper Github Profile link. Example: https://github.com/johndoe"
        )
        .trim(),
      linkedin: Yup.string()
        .matches(
          /^https:\/\/www\.linkedin\.com\/in\/[a-zA-Z0-9-]+\/?$/,
          "Invalid URL, please use a proper LinkedIn Profile link. Example: https://www.linkedin.com/in/johndoe/"
        )
        .trim()
        .required(),
      discord_id: Yup.string()
        .trim()
        .matches(
          /^[^#]{2,32}#\d{4}$|^(?!.*\.\.|[A-Z])[a-z\d_.]{2,32}$/,
          "Invalid Discord ID, please use a proper Discord ID. Example: johndoe123"
        )
        .optional(),
      portfolio: Yup.string()
        .url("Please enter a valid portfolio url")
        .trim()
        .optional(),
      photo: Yup.string()
        .trim()
        .required("IC not found in school database."),
        // .optional(),
      desc: Yup.string().trim().required("Please tell us about yourself"),
      resume: Yup.mixed()
        .required("Please upload your resume file"),
      agreement: Yup.boolean().oneOf(
        [true],
        "Please agree to the terms and conditions"
      ),
      photo_agreement: Yup.boolean().oneOf(
        [true],
        "Please agree to the photo agreement"
      ),
      resume_agreement: Yup.boolean().oneOf(
        [true],
        "Please agree to the resume agreement"
      ),
    });

    try {
      // validate form data using the Yup setup registerSchema, the parameter is the registerInfo (user input)
      await registerSchema.validate(registerInfo);

      //remove too many break linies for about me
      registerInfo.desc = registerInfo.desc?.replace(
        /(\r\n|\n|\r){3,}/gm,
        "\n"
      );

      const confirmMajor = await widget.confirm(
        "Major Confirmation",
        "Are you sure that the major selected is correct?"
      );
      if (!confirmMajor.isConfirmed) return;

      // check if user want to proceed with registration
      const confirm = await widget.confirm(
        "Register Confirmation",
        "Are you certain that the information provided is accurate and complete? Would you like to proceed with this submission?"
      );
      if (!confirm.isConfirmed) return;

      widget.loading();
      // MAIN register function
      //  console.log("!!!Register Details on register page: ", registerInfo);
      return await registerNewAccount(registerInfo);
    } catch (error: any) {
      if (error instanceof Yup.ValidationError) {
        widget.alertError("Sorry", error.message);
      }
      // console.log(error.message);
      return await widget.alertError("Error in handleSubmit: ", error);
    }
  };

  // first: compress image
  const registerNewAccount = async (registerInfo: Partial<ParticipantInfo>) => {
    try {
      widget.loading();

      // This can be commented out
      // const publicUrl: string = await getPublicUrlFirebase(webpFile, fileName);

      // but study and know why the createAccount will require the publicUrl?
      // create new participant record
      await authFactory().createAccount(registerInfo);

      // delete the image from firebase storage after created airtable record
      // delay 2 second to make sure airtable record is created first
      // await new Promise((resolve) => {
      //   setTimeout(async () => {
      //     resolve(null);
      //   }, 2500);
      // });
      widget
        .alertSuccess(
          "Registration Successful",
          "You may now log in to your account.",
          3000
        )
        .then(() => {
          window.location.href = "/sign-in";
        });
    } catch (error: any) {
      throw new Error("Error in registerNewAccount: " + error);
    }
  };

  function handleAboutMeChange(e: any) {
    const wordCount = e.target.value.trim().split(/\s+/).length;

    if (wordCount > 100) return;

    setTextCount(wordCount);
    setAboutMe(e.target.value);
  }

  function handleImageUpdate(e: any) {
    let newIC = e.target.value; // This ensures the newIC captures the latest IC in real time; useState method is a step slower, such that the latest value will be only updated in next render

    setIC(newIC);

    if (newIC !== "") {
      let imageURL =
        "https://campusonline2.usm.my/photo/StudentPic/" + newIC + ".jpg";

      let img = new Image();
      img.src = imageURL;
      img.onload = function () {
        // if there is image that matches to ic inputted
        setProfileImage(imageURL);
      };
      img.onerror = function () {
        // if there is no image that matches to ic inputted
        setProfileImage(null);
      };
    }
  }

  async function handleResumeUpload(e:any){
    try {
      const file = e.files[0];
      // Validate file size
      if(file.size > 5000000){
        return await widget.alertError("Error", "File size is too large!");
      }

      // Validate file type
      if(file.type !== 'application/pdf'){
        return await widget.alertError("Sorry", "Please upload a PDF file!");
      }
      setIsUploading(true);
      setUploadProgress(10);

      const reader = new FileReader();

      // Read file as data URL
      const dataUrl = await new Promise<string>((resolve, reject) =>{
        reader.onload = ()=>{
          setUploadProgress(30);
          resolve(reader.result as string);
        };
        reader.onerror = (error) => reject(error);
        reader.readAsDataURL(file);
      });
      setUploadProgress(50);

      // Upload to Google Drive
      const fileUrl = await googleDriveUpload(
        file,
        dataUrl,
        resumeFolderId
      );
      setUploadProgress(100);
      setResume(fileUrl);

      // Reset progress after a short delay
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 1000);
    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      return await widget.alertError("Error", "Failed to upload file. Please try again.");
    }
  }

  return (
    <div className="h-full flex flex-col md:justify-center px-4 md:px-16 lg:px-20 py-8">
      <div className="flex flex-col sm:flex-row  md:items-center">
        <a href="/sign-in">
          <i
            title="Back to Sign In"
            className="relative fa-solid fa-arrow-left flex justify-center items-center max-md:size-8 size-6 md:-ml-8  text-xs rounded-full border border-grey-4 transition duration-100 cursor-pointer hover:bg-primary-2 hover:border-primary-6 hover:text-primary-6"
          ></i>
        </a>
        <div className="text-mobile-14">
          <p className="text-primary-linear max-md:mt-2">Registration</p>
          <h1 className="font-bold text-mobile-20 lg:text-tablet-24">
            Fill Out the Details
          </h1>
        </div>
      </div>
      <form onSubmit={handleSubmit} className="w-full h-full">
        <div className="grid md:grid-cols-2 md:gap-12 mt-5">
          {/* Left side */}
          <div className="flex flex-col w-full space-y-3">
            {/* General */}
            <input
              type="text"
              id="emailaddress"
              name="email"
              defaultValue={userEmail}
              required
              readOnly
              disabled
              className="daisy-input cursor-not-allowed disabled:text-grey-3"
            />
            <input
              type="text"
              placeholder="Full Name"
              className="daisy-input capitalize"
              name="name"
              required
              onChange={(e: ChangeEvent<HTMLInputElement>) => {
                e.target.value = e.target.value.replace(/\b\w/g, (l) =>
                  l.toUpperCase()
                );
                setName(e.target.value);
              }}
            />
            <input
              type="text"
              placeholder="Display Name"
              className="daisy-input"
              name="display_name"
              required
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setDisplayName(e.target.value)
              }
            />

            <div className="w-full flex flex-col lg:flex-row gap-4 md:gap-2">
              <input
                type="text"
                placeholder="Matric Number"
                className="!m-0 w-full lg:w-[50%] daisy-input "
                name="matric_no"
                required
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setMatricNo(e.target.value)
                }
              />
              <select
                defaultValue={"DEFAULT"}
                name="major"
                required
                className="!m-0 w-full lg:w-[50%] daisy-select text-mobile-16"
                onChange={(e: ChangeEvent<HTMLSelectElement>) =>
                  setMajor(e.target.value)
                }
              >
                <option value="DEFAULT" className="" disabled>
                  Major Category
                </option>
                <option value="Software Engineering">
                  Software Engineering
                </option>
                <option value="Intelligent Computing">
                  Intelligent Computing
                </option>
                <option value="Computing Infrastructure">
                  Computing Infrastructure
                </option>
              </select>
            </div>

            <div className="w-full flex flex-col lg:flex-row gap-4 md:gap-2">
              <input
                type="text"
                placeholder="IC (without '-')"
                name="ic"
                required
                className="!m-0 w-full daisy-input"
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  handleImageUpdate(e)
                }
              />
              <input
                type="text"
                placeholder="Phone No. (without '-')"
                name="tel"
                required
                className="!m-0 w-full daisy-input"
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setTel(e.target.value)
                }
              />
            </div>

            <input
              type="email"
              placeholder="Personal Email"
              name="personal_email"
              required
              className="daisy-input"
              onChange={(e: ChangeEvent<HTMLInputElement>) =>
                setPersonalEmail(e.target.value)
              }
            />

            <h2 className="underline underline-offset-8 font-semibold !mt-5">
              Social Network
            </h2>

            {/* Social Network */}
            <div className="flex flex-col gap-3 justify-center !mt-5">
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-github fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="https://github.com/johndoe"
                  name="github"
                  required
                  className="w-full daisy-input"
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setGithub(e.target.value)
                  }
                />
              </div>
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-linkedin fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="https://www.linkedin.com/in/johndoe/"
                  name="linkedin"
                  required
                  className="w-full daisy-input"
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setLinkedIn(e.target.value)
                  }
                />
              </div>
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-brands fa-discord fa-xl"></i>
                <input
                  type="text"
                  placeholder="Discord ID (optional)"
                  name="discord_id"
                  className="w-full daisy-input"
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setDiscordID(e.target.value)
                  }
                />
              </div>
              <div className="flex gap-3 items-center">
                <i className="hidden sm:flex fa-solid fa-globe fa-xl w-[32px] justify-center"></i>
                <input
                  type="text"
                  placeholder="Portfolio website url (optional)"
                  name="portfolio"
                  className="w-full daisy-input"
                  onChange={(e: ChangeEvent<HTMLInputElement>) =>
                    setPortfolio(e.target.value)
                  }
                />
              </div>
            </div>
          </div>

          {/* Right side */}
          <div className="flex flex-col w-full h-full justify-between">
            <div>
              {/* Profile Image */}
              <div className="max-md:mt-5">
                <h2 className="underline underline-offset-8 font-semibold max-md:mt-5">
                  Profile Image
                </h2>
                <div className=" relative  flex justify-center mt-5">
                  <img
                    src={profileImage ? profileImage : imageDetail.src}
                    width="100"
                    height="100"
                    alt={imageDetail.alt}
                    className="mb-4 rounded-full object-contain size-32 border-2 border-grey-2"
                  />
                </div>
                <div>
                  <ul className="list-disc pl-5 text-sm">
                    <li className="text-green-800">
                      If your IC/ Passport Number is correctly entered (without
                      any special characters like '-'), your student profile
                      picture will be shown above.
                    </li>
                  </ul>
                </div>
              </div>

              {/* About Me */}
              <div>
                <h2 className="underline underline-offset-8 font-semibold mt-6">
                  About Me
                </h2>
                <textarea
                  placeholder="Tell us about yourself..."
                  name="about_me"
                  className="w-full daisy-textarea  mt-5 text-base"
                  required
                  rows={5}
                  value={aboutMe}
                  onChange={(e: ChangeEvent<HTMLTextAreaElement>) =>
                    handleAboutMeChange(e)
                  }
                />
                <p className="text-right text-grey-4 mt-1">
                  {aboutMe ? textCount : 0} / 100
                </p>
              </div>

              {/* Resume */}
              <div>
                <h2 className="underline underline-offset-8 font-semibold !mt-5">
                  Resume
                </h2>
                <input 
                  className="w-full text-base daisy-file-input mt-5 border-2 border-grey-2 focus:outline-primary-2" aria-describedby="file_input_help" 
                  id="resume-file" 
                  type="file"
                  name="resume"
                  required
                  accept=".pdf"
                  onChange={(e: any)=>{
                    handleResumeUpload(e.target);
                  }}
                />
                <div className="flex justify-between">
                  <p className="mt-1 text-sm text-grey-3" id="file_input_help">PDF only (Max size 5MB).</p>
                </div>
                {/* Progress Bar */}
                {isUploading && (
                  <div className="mt-2">
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div 
                        className="bg-black h-2.5 rounded-full transition-all duration-300 ease-in-out"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm mt-1">
                      {uploadProgress < 100 ? 'Uploading...' : 'Upload complete!'}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Agreement */}
            <div className="w-full flex flex-col  justify-between mt-4">
              <div className="flex flex-col mt-3 gap-1">
                {/* terms and conditions agreement */}
                <div className="flex items-center  md:text-base justify-end gap-2">
                  <label htmlFor="agreement" className="ml-2 text-mobile-14">
                    I agree to the{" "}
                    <a
                      href="/terms"
                      className="text-primary-6 underline text-mobile-14"
                      target="_blank"
                    >
                      Terms of Service
                    </a>
                  </label>
                  <input
                    id="agreement"
                    type="checkbox"
                    name="agreement"
                    required
                    className="hover:cursor-pointer checkbox-md !border-grey-2 hover:!border-primary-6 [--chkbg:theme(colors.white)]"
                  />
                </div>

                {/* This is the photo agreement */}
                <div className="flex items-center mt-0.5 md:text-base justify-end gap-2">
                  <label
                    htmlFor="photo_agreement"
                    className="ml-2 text-mobile-14 text-right "
                  >
                    I agree to use my Student Card photo as my profile picture. You may change it later in the dashboard.
                  </label>
                  <input
                    id="photo_agreement"
                    type="checkbox"
                    name="photo_agreement"
                    required
                    className="hover:cursor-pointer checkbox-md !border-grey-2 hover:!border-primary-6 [--chkbg:theme(colors.white)]"
                  />
                </div>

                {/* This is the photo agreement */}
                <div className="flex items-center mt-0.5 md:text-base justify-end gap-2">
                  <label
                    htmlFor="resume_agreement"
                    className="ml-2 text-mobile-14 text-right "
                  >
                    I agree with resume collection
                  </label>
                  <input
                    id="resume_agreement"
                    type="checkbox"
                    name="resume_agreement"
                    required
                    className="hover:cursor-pointer checkbox-md !border-grey-2 hover:!border-primary-6 [--chkbg:theme(colors.white)]"
                  />
                </div>
              </div>
              <div className="flex  mt-3 mx-auto md:mx-0 space-x-3 place-content-end w-full">
                <button
                  data-modal-target="profileModal"
                  data-modal-toggle="profileModal"
                  className="border border-primary-6  rounded text-primary-6 transition duration-200 hover:-translate-y-0.5 hover:shadow-md px-5 py-2"
                  type="button"
                  title="Preview Profile"
                  onClick={() => profileModalRef.current.showModal()}
                >
                  Preview
                </button>
                <ProfileModal
                  ref={profileModalRef}
                  display_name={displayName}
                  student_major={major}
                  matric_no={matric_no}
                  desc={aboutMe?.replace(/(\r\n|\n|\r){3,}/gm, "\n")}
                  personal_email={userEmail}
                  linkedin={linkedIn}
                  github={github}
                  portfolio={portfolio}
                  photo={profileImage}
                />
                <button
                  type="submit"
                  id="submit"
                  className="bg-primary-6 hover:bg-primary-7 transition-all  rounded text-white duration-200 hover:-translate-y-0.5 hover:shadow-md px-5 py-2"
                  title="Register"
                >
                  Register
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Register;
