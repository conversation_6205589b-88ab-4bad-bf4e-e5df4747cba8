import { projectFactory } from "@/modules";
import { actionSessionStorage, widget } from "@/utils";
import { useContext, useEffect, useState } from "react";
import { ParticipantContext } from "./Context";
import ProjectSubmisionCard from "./ProjectSubmissionCard";
import LoadingSkeleton from "./ProjectSubmissionCard";
import Loading from "@components/general/Loading";

const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";

export const ProjectSection: React.FC = () => {
  const [loading, setLoading] = useState(true);

  const {
    user: participant,
    setUser,
    isTeam,
    team,
    setProject,
  } = useContext(ParticipantContext);

  useEffect(() => {
    (async function () {
      if (participant.project_id) {
        await fetchProjectData();
        setLoading(false);
      } else {
        setLoading(false);
      }
    })();
  }, []);

  async function fetchProjectData() {
    const projectDoc = await projectFactory().getProject(
      participant.project_id
    );

    // console.log("Project Doc:", projectDoc);

    if (projectDoc) {
      setProject(projectDoc);
      // update user session storage everytime project data is fetched
      // this is to ensure that the project data is always up-to-date
      await actionSessionStorage().update("userData", {
        project_name: projectDoc.project_name,
        project_major: projectDoc.project_major,
        sdg: projectDoc.sdg,
        project_id: projectDoc.id,
      });

      setUser({
        ...participant,
        project_name: projectDoc.project_name,
        project_major: projectDoc.project_major,
        sdg: projectDoc.sdg,
      });
    } else {
      // if projectDoc not found, alert error
      await widget.alertError("error", "Project not found");
      window.location.href = "/sign-in";
    }
  }

  const createProject = async () => {
    isTeam &&
      (await widget.alert({
        icon: "info",
        title: "Info",
        html: "Only one team member is required to submit the project at once.",
      }));
    window.location.href = "participant/submission/create";
  };

  return (
    <>
      <section className="flex flex-col justify-center ">
        <div className="flex flex-row gap-3 my-4 flex-grow">
          <div className="bg-green-300 w-4 rounded-md" />
          <p className="font-semibold ">Project Info</p>
        </div>
        {loading && participant.project_name ? (
          <LoadingSkeleton />
        ) : (
          loading && (
            <div className="w-full flex justify-center ">
              <Loading />
            </div>
          )
        )}

        {!loading && (
          <div className="h-full flex items-center">
            {/* team mode, but yet to create or join a team */}
            {isTeam && team && Object.keys(team).length === 0 ? (
              <div className="mx-auto w-full text-center my-5 h-full grow">
                <img
                  src="/assets/empty-state/no-team.svg"
                  className="w-full max-w-[12rem] mx-auto"
                  alt="No Team"
                />
                <p className="text-gray-500 italic mb-2">
                  You are not in a team yet.
                  <br /> Either create or join a team.
                </p>
              </div>
            ) : (
              <>
                {/* No project submitted yet */}
                {participant.project_name === undefined ||
                participant.project_name === "" ? (
                  isRegistration || participant.is_special_access_granted ? (
                    <div className="mx-auto w-full text-center flex-grow">
                      <img
                        src="/assets/empty-state/no-project.svg"
                        className="w-full max-w-[10rem] mx-auto mb-4"
                        alt="No Project"
                      />
                      <button
                        onClick={createProject}
                        className="bg-primary-6 text-white px-3 py-2 hover:bg-primary-7 transition rounded-lg"
                        id="addProjectButton"
                      >
                        Click here to submit project
                      </button>
                    </div>
                  ) : (
                    <div>No project submitted.</div>
                  )
                ) : (
                  // Project submitted exists
                  <ProjectSubmisionCard />
                )}
              </>
            )}
          </div>
        )}
      </section>
    </>
  );
};
