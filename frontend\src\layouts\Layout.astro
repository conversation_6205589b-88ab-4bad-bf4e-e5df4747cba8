---
import Navbar from "@components/navbar/Navbar.astro";
import "./layout.css";

interface Props {
  title: string;
  hasNavbarSpace?: boolean;
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title, hasNavbarSpace = false } = Astro.props;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society" />
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

    <meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025" />
    <meta property="og:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp" />
    <meta property="og:url" content="https://pixelusm.com/" />

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta name="twitter:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp" />

    <meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />

    <!-- FontAwesome cdn css -->
    <link
      rel="stylesheet"
      href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css"
    />

    <!-- AOS cdn css -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    
    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>
    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body>
    <div id="main-modal"></div>
    <Navbar />
    {hasNavbarSpace && <div class="py-10" />}
    <slot />

    <!-- GSAP cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
    ></script>

    <!-- ScrollTrigger cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"
    ></script>

    <!-- AOS script cdn js -->
    <script is:inline src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script is:inline>
      AOS.init();
    </script>

    <script src="../../node_modules/flowbite/dist/flowbite.min.js"></script>
  </body>
</html>
