import React, { useEffect, useState } from "react";
import { useStore } from "@/utils/store";
import * as pkg from "react-lazy-load-image-component";
const { LazyLoadImage } = pkg;
import "react-lazy-load-image-component/src/effects/blur.css";

interface Props {
  name: string;
  desc: string;
  tier: string;
}

const PartnerCard: React.FC<Props> = ({ name, desc, tier }) => {
  return (
    <div className="relative w-full border border-black h-[250px] partners-card bg-white max-w-96">
      {/* Image div */}
      <div className="flex justify-center items-center h-[215px] px-4 py-2">
        <LazyLoadImage
          src={`/assets/images/partners/${name}.webp`}
          alt={name + " image"}
          className="h-28 aspect-auto"
          effect="blur"
        />
      </div>
      {/* Partner's tier div */}
      <div className="py-1 border-t border-black text-center font-semibold text-mobile-16 bg-primary-3">
        {tier}
      </div>
      {/* Tooltip div */}
      <div className="z-5 absolute top-5 h-44 p-4 w-full bg-white opacity-0 hover:opacity-100 transition-opacity duration-300">
        {desc ? (
          <div className="flex flex-col justify-center h-full w-full p-4">
            <p className="text-center font-semibold text-mobile-14 lg:text-base mb-4 md:mb-2 sm:mb-3">
              {name}
            </p>
            <p className="text-center text-mobile-14 text-xs md:text-tablet-14 lg:text-desktop-14">
              {desc}
            </p>
          </div>
        ) : (
          <div className="flex flex-col justify-center items-center h-full w-full">
            <p className="font-semibold text-center leading-tight">{name}</p>
          </div>
        )}
      </div>
    </div>
  );
};

const PartnersList = ({ data }) => {
  const [partners, setPartners] = useState(data);
  const [isLoading, setIsLoading] = useState(true);

  const { filter, setFilter } = useStore();

  useEffect(() => {
    if (data) {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (filter === "All") {
      setPartners(data);
    } else {
      const filteredPartners = data.filter(
        (partner) => partner.tier === filter
      );
      setPartners(filteredPartners);
    }
  }, [filter]);

  return (
    <section className="w-full flex flex-col items-center">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 justify-center md:justify-evenly xl:justify-start gap-y-8 md:gap-x-8 md:gap-y-10 lg:gap-x-10 lg:gap-y-12 xl:gap-y-12 xl:gap-x-8 w-full mx-auto place-items-center">
        {!isLoading ? (
          <>
            {partners.length > 0 ? (
              partners.map((partner: any, index: number) => {
                return (
                  <PartnerCard
                    key={index}
                    name={partner.name}
                    desc={partner.desc}
                    tier={partner.tier}
                  />
                );
              })
            ) : (
              <p className="text-center w-full">No partners to display.</p>
            )}
          </>
        ) : (
          // <!-- Partner card skeleton -->
          Array.from({ length: 6 }).map((_, index) => (
            <div className="relative w-full bg-white animate-pulse border-2 border-grey-3 sm:max-md:w-[382.4px]">
              <div className="flex justify-center items-center h-44 px-4 py-2 bg-grey-2" />
              <div className="py-3 px-6 text-center font-semibold text-mobile-16 flex justify-center">
                <div className="h-2 w-24 bg-grey-2 rounded-lg" />
              </div>
            </div>
          ))
        )}
      </div>
    </section>
  );
};

export default PartnersList;
