import {
  deleteObject,
  getDownloadURL,
  getStorage,
  ref,
  uploadBytesResumable,
  getMetadata,
  uploadBytes,
} from "firebase/storage";

class FirebaseStorage {
  private storage: any;

  constructor() {
    this.storage = getStorage();
  }

  getPublicUrlFirebase = async (file: any, filename: string) => {
    const storageRef = ref(this.storage, filename);
    let metadata: any = {};

    if (
      file.type ===
      "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    ) {
      metadata = {
        contentType:
          "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        contentDisposition: `attachment; filename="${filename.split("/")[2]}.pptx"`,
      };
    } else {
      metadata = {
        contentType: file.type,
      };
    }

    await uploadBytesResumable(storageRef, file, metadata);
    return await getDownloadURL(ref(getStorage(), filename));
  };

  uploadImageFirebase = async (
    file: File,
    filename: string,
    folderPath: string
  ): Promise<string> => {
    try {
      const storageRef = ref(this.storage, `${folderPath}/${filename}`);

      // Add optional metadata
      const metadata = {
        contentType: file.type || "application/octet-stream",
      };

      // Upload file
      await uploadBytes(storageRef, file, metadata);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.error("Error uploading file:", error);
      throw new Error("Upload failed: " + error);
    }
  };

  deleteImageFirebase = async (filename: string) => {
    const storageRef = ref(this.storage, filename);
    await deleteObject(storageRef)
      .then(() => {
        return "Success";
      })
      .catch((error) => {
        return "Error" + error;
      });
  };

  getAttachmentFirebaseType = async (link: string) => {
    const storageRef = ref(this.storage, link);
    const type = await getMetadata(storageRef).then((metadata: any) => {
      return metadata.contentType;
    });
    return type;
  };
}

const firebaseStorage = new FirebaseStorage();
export { firebaseStorage };
