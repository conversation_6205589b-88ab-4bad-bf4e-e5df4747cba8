import { projectFactory } from "@modules/index";
import { widget } from "@utils/widget";
import { useContext, useState } from "react";
import ReactDOM from "react-dom/client";
import Feedback from "./Feedback";
import { ParticipantContext } from "./Context";
import ProjectModal from "@components/projects/ProjectModal";
import ProjectSubmissionView from "../Submission/ProjectSubmissionView";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";
const isOfficialLaunch =
  import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "true";
const isJudging = import.meta.env.PUBLIC_PIXEL_IS_JUDGING === "true";
const isPostSubmission =
  import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const ProjectSubmissionCard = () => {
  const [isOpen, setIsOpen] = useState(false);

  const {
    project,
    user: participant,
    TEAM_MAX_LIMIT,
    TEAM_MIN_LIMIT,
  } = useContext(ParticipantContext);

  const showProject = () => {
    document.body.style.overflow = "hidden";
    setIsOpen(true);
  };

  const closeProject = () => {
    document.body.style.overflow = "auto";
    setIsOpen(false);
  };

  const deleteProject = async () => {
    const confirmDelete = await widget.confirm(
      "Delete Project",
      "Are you sure you want to delete this project?",
      "Delete"
    );
    if (!confirmDelete.isConfirmed) return;

    try {
      widget.loading();
      await projectFactory().deleteProject(participant.project_id, participant);
      await widget.alertSuccess("Success", "Project deleted successfully.");
      window.location.reload();
    } catch (error) {
      widget.alertError("Error deleting project", error);
    }
  };

  // TODO: show feedback to participants
  const openFeedback = () => {
    widget.alert({
      title: "Feedback",
      html: '<div id="feedback"></div>',
      showCloseButton: true,
      showConfirmButton: false,
      width: "42rem",
      allowOutsideClick: false,
      didOpen: () => {
        ReactDOM.createRoot(
          document.querySelector("#feedback") as HTMLElement
        ).render(<Feedback feedbacks={project.comments} />);
      },
    });
  };

  return (
    <>
      {isOpen && (
        <ProjectModal onClose={closeProject} id={project.id}>
          <ProjectSubmissionView project={project} onClose={closeProject} />
        </ProjectModal>
      )}
      <div className="w-full flex flex-col md:flex-row justify-between h-full drop-shadow-md transition-all px-5 md:px-7 py-5 bg-white rounded">
        <div className="flex flex-col items-start justify-between h-full w-fit mb-4 lg:mb-0 mr-auto lg:mr-0">
          <p className="flex items-baseline justify-center min-h-12 text-lg md:text-2xl tracking-wider text-black font-bold break-words w-fit p-0 m-0 lg:max-w-[100%]">
            {participant.project_name}
            {project.isDraft && (
              <span className="text-sm text-gray-700">&nbsp;(Draft)</span>
            )}
          </p>

          <div className="flex text-xs gap-3 mr-auto lg:mr-0">
            <p className="text-primary-6 text-mobile-14 sm:text-mobile-16 uppercase border border-primary-6 rounded px-2 py-1">
              {participant.project_major}
            </p>
            <p className="text-primary-6 text-mobile-14 sm:text-mobile-16 uppercase border border-primary-6 rounded px-2 py-1 ">
              SDG {participant.sdg}
            </p>
          </div>
        </div>

        <div className="flex gap-3 max-sm:w-full flex-col justify-end float-right text-center">
          {participant.project_name ? (
            <>
              {!project.isDraft && (
                <button
                  onClick={showProject}
                  className="daisy-btn border-none bg-primary-6 hover:bg-primary-7  min-w-fit max-sm:w-full text-white rounded shadow transition flex justify-center items-center"
                  id="viewProjectButton"
                >
                  <i className="fa-solid fa-eye mr-2 fa-xs"></i>
                  <p className="text-mobile-16 whitespace-nowrap text-white">
                    View Project
                  </p>
                </button>
              )}

              {/* Update Project / Edit Draft Button */}
              {isOfficialLaunch &&
                !(participant.year < currentYear) &&
                (isRegistration || participant.is_special_access_granted) &&
                !isPostSubmission &&
                !isPostEvent && (
                  <a
                    href={`participant/submission/edit/${participant.project_id}`}
                    className={`daisy-btn border-primary-6 border-[1px] bg-white ${!project.isDraft && "flex-1"} min-w-fit max-sm:w-full hover:bg-primary-1 hover:border-primary-7 text-primary-6 hover:text-primary-7 group rounded cursor-pointer shadow duration-100 transition flex justify-center items-center`}
                    id="updateProjectButton"
                  >
                    <i className="fa-solid fa-pencil fa-xs mr-2"></i>
                    <p className="text-mobile-16 whitespace-nowrap text-primary-6 group-hover:text-primary-7">
                      {project.isDraft ? "Edit Draft" : "Update Project"}
                    </p>
                  </a>
                )}

              {isRegistration && TEAM_MAX_LIMIT != 1 && TEAM_MIN_LIMIT != 1 && (
                <button
                  onClick={deleteProject}
                  className="bg-red-600 hover:bg-red-700  transition text-white p-2 lg:px-3 lg:py-2 rounded  shadow w-full  n flex items-center"
                  id="deleteProjectButton"
                >
                  <i className="fa-solid fa-eye mr-2 fa-xs"></i>
                  <p className="text-mobile-16 whitespace-nowrap text-white flex-grow">
                    Delete Project
                  </p>
                </button>
              )}

              {(isPostEvent || participant.year < currentYear) && (
                <button
                  onClick={openFeedback}
                  className="px-4 py-3 border-primary-6 border-[1px] bg-white flex-1 min-w-fit max-sm:w-full hover:bg-primary-1 hover:border-primary-7 text-primary-6 hover:text-primary-7 group rounded cursor-pointer shadow duration-100 transition flex justify-center items-center"
                  id="oepnFeedbackButton"
                >
                  <i className="fa-regular fa-comment-dots mr-2 fa-sm"></i>
                  <p className="text-mobile-16 whitespace-nowrap text-primary-6 group-hover:text-primary-7">
                    View Feedback
                  </p>
                </button>
              )}
            </>
          ) : (
            <>
              <div className="w-28 md:w-36 h-6 md:h-10 bg-blue-800 rounded animate-pulse"></div>
              {isOfficialLaunch && isRegistration && (
                <div className="w-28 md:w-36 h-6 md:h-10 bg-[#14AE93] rounded animate-pulse"></div>
              )}
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default ProjectSubmissionCard;

export const LoadingSkeleton = () => {
  return (
    <div className="w-full flex flex-col md:flex-row justify-between h-full drop-shadow-md transition-all px-5 md:px-7 py-5 bg-white rounded animate-pulse">
      <div className="flex flex-col items-start justify-between h-full w-fit mb-4 lg:mb-0 mr-auto lg:mr-0">
        <div className="w-28 md:w-36 h-6 md:h-10 bg-gray-300 rounded mb-3"></div>
        <div className="flex text-xs gap-3 mr-auto lg:mr-0">
          <div className="w-20 h-6 bg-gray-300 rounded px-2 py-1"></div>
          <div className="w-20 h-6 bg-gray-300 rounded px-2 py-1"></div>
        </div>
      </div>

      <div className="flex gap-3 max-sm:w-full flex-col justify-end float-right text-center">
        <div className="w-28 md:w-36 h-6 md:h-10 bg-gray-300 rounded mb-2"></div>
        <div className="w-28 md:w-36 h-6 md:h-10 bg-gray-300 rounded mb-2"></div>
      </div>
    </div>
  );
};
