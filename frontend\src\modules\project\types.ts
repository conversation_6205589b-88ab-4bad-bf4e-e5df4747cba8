import type { Team } from "../team";

export interface Project {
	id: string;
	project_name: string;
	project_short_name: string;
	project_desc: string;
	project_major: string;
  supervisor: Supervisor;
	sdg: string;
	tech_stack: string[];
	thumbnail: any;
  highlights: string[];
	video: string;
	video_qc: string; // presentation video quality check
	slide: string;
	year: string;
	created_at: Date;
  updated_at: Date;
  updated_by: string;
	updated_by_student_name: string;

	isDraft?: boolean; // indicates if the project is a draft
	
	likes: number;
	awards: string[];
  // slide: Slide;
  team: Team;
	
	assigned_judges_id?: string[]; // judge airtable id

	// evaluation data
	evaluation_id?: string[]; // evaluation airtable id
	evaluated_judge_name?: string[]; // list of judges that have already evaluated (based on evaluation_id)


  comments: string[]; // list of comments ids
	
	// TO BE REMOVED - LEGACY CODE
	// likes?: string[]; // list of guest ids

	// added these missing types below due to build error
	// firebase_id: string; // project airtable id
	// tags: string;
	// project_no: string;
	// description: string;
	// hashtags: string[];
	// presentation_link: string;
	// highlights: {
	// 	url: string;
	// }[];
	// likes: string[];
	// feedbacks: any[];
	// airtable_id: string;
	// presentation_slide: {
		// url: string;
		// type: string;
	// }[];
	// award: string[];
	// team_images: string[];
}

// TO BE REMOVED - old supervisor type
// type supervisor = {
//   name: string;
//   profile_link: string;
// };

// TO BE REMOVED - old Project model
// export interface Project {
//   id: string;
//   supervisor: supervisor; // {name: string, profile_link: string}
//   major: string;
//   name: string;
//   project_no: string;
//   sdg: string;
//   description: string;
//   hashtags: string[];
//   presentation_link: string;
//   highlights: any[]; // [] list of files
//   likes: string[]; // list of guest ids
//   comments: string[]; // list of comments ids
//   tags: string;
//   feedbacks: any[];

//   //airtable fields
//   airtable_id: string;
//   presentation_slide: any[];
//   thumbnail: any;
//   award: string[];

//   //firebase fields
//   firebase_id: string;

//   team: Team;
//   team_images: string[];
// }

// export interface Slide {
//   url:  string;
//   type: string;
// }




export interface Supervisor {
  name: string;
  profile_link: string;
}