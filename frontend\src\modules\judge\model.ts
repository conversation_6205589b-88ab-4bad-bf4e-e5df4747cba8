export interface Judge {
  id: string; 
  firebase_uid: string;
  // airtable_id?: string;
  email?: string;
  name: string;
  salutation: string;
  year: string;
  organization?: string;
  position?: string;
  country: string;
  linkedin?: string;
  major?: string;
  assigned_projects_id?: string[];
  project_name?: string[];
  project_major?: string[];
  sdg?: string[];
  assigned_projects_year?: string[];
  evaluation_id?: string[];
  evaluated_projects_id?: string[];
  evaluated_projects_name?: string[];
  evaluated_projects_year?: string[];
  created_at?: Date;
  updated_at?: Date;
}

export interface Evaluation {
  id: string;
  judge_id: string;
}
