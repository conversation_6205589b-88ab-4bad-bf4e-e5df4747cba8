---
import SideLayout from "@layouts/sideLayout.astro";
import faqs from "@data/faq.json";

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "FAQs", path: "/faq" },
];

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
---

<SideLayout
  title="FAQs"
  pageDesc=`Confused no more. Here is your guide to everything PIXEL ${currentYear}.`
  hasMenu={false}
  {breadcrumbs}
>
  <div class="flex w-full space-x-4 max-md:justify-center items-center">
    <span
      id="accordion_expand"
      class="cursor-pointer hover:text-primary-5 text-grey-4">Expand All</span
    >
    <span class="text-black/60">|</span>
    <span
      id="accordion_collapse"
      class="cursor-pointer hover:text-primary-5 text-grey-4"
      >Collapse All</span
    >
  </div>

  <section class="mt-10">
    <div class="space-y-6">
      {
        faqs.map((faq: any) => (
          <div class="daisy-collapse daisy-collapse-plus border-b-2 border-grey-2 rounded-none h-fit group">
            <input type="checkbox" class="accordion_checkbox w-full" />
            <div class="daisy-collapse-title text-xl pl-0 text-mobile-16 md:text-tablet-20 lg:text-desktop-20 group-hover:text-primary-6 duration-300 transition group-checked:text-primary-6">
              {faq.question}
            </div>
            <div class="daisy-collapse-content text-grey-5 pl-0 text-justify">
                <div set:html={faq.answer}></div>
            </div>
          </div>
        ))
      }
    </div>
  </section>
</SideLayout>

<!-- Declare a local @media(min-width) for precise mx-auto for the width of 1080px for responsiveness-->
<!-- <style>
  @media (min-width: 1280px) {
    #FAQ {
      margin-left: auto;
      margin-right: auto;
    }
  } -->

<script>
  document
    .querySelector(".accordion_checkbox")
    .addEventListener("click", function () {
    //  console.log("hello");
    });

  document
    .getElementById("accordion_expand")
    .addEventListener("click", function () {
      const collapses = document.querySelectorAll(".accordion_checkbox");
      collapses.forEach((collapse) => {
        (collapse as HTMLInputElement).checked = true;
      });
    });
  document
    .getElementById("accordion_collapse")
    .addEventListener("click", function () {
      const collapses = document.querySelectorAll(".accordion_checkbox");
      collapses.forEach((collapse) => {
        (collapse as HTMLInputElement).checked = false;
      });
    });
</script>
