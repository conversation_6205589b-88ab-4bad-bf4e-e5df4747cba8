{"jobs": [{"id": "1", "name": "Software Engineer", "location": "Remote", "type": "Full Time", "mode": "Remote", "company": {"name": "Tech Corp", "logo": "/assets/images/careers/huawei.jpg", "website": "https://techcorp.com", "sponsor_type": ["Platinum"]}, "description": "Join our team to develop scalable, high-performance web applications using the latest JavaScript frameworks and cloud technologies.", "requirements": ["Proficiency in JavaScript, React, and Node.js", "Experience with REST APIs and cloud environments", "Strong debugging and problem-solving skills"], "responsibilities": ["Lorem ipsum dolor sit amet", "Able to provide enablement to regional frontline DA support teams by training to improve the overall business analysis, algorithm development, and data analysis capabilities of regions.", "Sed do eiusmod tempor incididunt", "Ut labore et dolore magna aliqua", "Ut enim ad minim veniam", "<PERSON>ui<PERSON> nostrud exercitation ullamco", "Laboris nisi ut aliquip ex ea commodo", "Duis aute irure dolor in reprehenderit", "In voluptate velit esse cillum dolore", "Eu fugiat nulla pariatur", "Experience with REST APIs and cloud environments", "Strong debugging and problem-solving skills"], "benefits": ["OT WITHOUT OT pay", "Free stress with tight deadlines", "Unlimited coffee (until the machine breaks)", "Exposure™ — you'll grow, one burnout at a time", "Flexible hours (as long as you're always available)", "Performance-based praise (not raises)"], "applyLink": "https://techcorp.com/apply"}, {"id": "2", "name": "Product Manager", "location": "New York, NY", "type": "Full Time", "mode": "Onsite", "company": {"name": "Jabil Inc.", "logo": "/assets/images/careers/jabil.png", "website": "https://jabil.com", "sponsor_type": ["Gold"]}, "description": "Lead product strategy and roadmap, collaborating across teams to launch impactful solutions in a fast-paced tech environment.", "requirements": ["3+ years in product management", "Strong analytical and communication skills", "Experience working with agile teams"], "applyLink": "https://jabil.com/careers"}, {"id": "3", "name": "UX Designer", "location": "Kuala Lumpur", "type": "Contract", "mode": "Hybrid", "company": {"name": "Huawei Malaysia", "logo": "/assets/images/careers/huawei.jpg", "website": "https://huawei.com", "sponsor_type": ["Silver"]}, "description": "Design intuitive digital experiences for mobile and web platforms. Collaborate closely with engineering and product teams.", "requirements": ["Strong portfolio with UX/UI projects", "Proficiency in Figma, Adobe XD, or Sketch", "Understanding of user testing and accessibility"], "applyLink": "https://huawei.com/careers"}, {"id": "4", "name": "Marketing Executive", "location": "Selangor", "type": "Part Time", "mode": "Hybrid", "company": {"name": "CelcomDigi", "logo": "/assets/images/careers/celcomdigi.jpg", "website": "https://celcomdigi.com", "sponsor_type": ["Gold"]}, "description": "Support our marketing campaigns, digital outreach, and customer engagement activities for one of Malaysia's leading telco brands.", "requirements": ["Familiarity with digital marketing tools (Meta Ads, Google Ads)", "Creative mindset and strong copywriting skills", "Ability to work independently and manage timelines"], "applyLink": "https://celcomdigi.com/careers"}, {"id": "5", "name": "Data Analyst", "location": "Penang", "type": "Full Time", "mode": "Onsite", "company": {"name": "Huawei Malaysia", "logo": "/assets/images/careers/huawei.jpg", "website": "https://huawei.com", "sponsor_type": ["Silver"]}, "description": "Analyze business and operational data to generate insights and support strategic decisions using modern analytics tools.", "requirements": ["Strong skills in SQL and Excel", "Experience with Tableau, Power BI or equivalent", "Ability to communicate data clearly to stakeholders"], "applyLink": "https://huawei.com/jobs"}, {"id": "6", "name": "Software Engineer <PERSON><PERSON>", "location": "Cyberjaya", "type": "Internship", "mode": "Onsite", "company": {"name": "Jabil Inc.", "logo": "/assets/images/careers/jabil.png", "website": "https://jabil.com", "sponsor_type": ["Gold"]}, "description": "<PERSON>ain hands-on experience building real-world software in a global tech environment. You’ll support ongoing development projects and learn best practices.", "requirements": ["Currently pursuing a degree in Computer Science or related", "Basic programming skills in HTML, CSS, JavaScript", "Team player with willingness to learn"], "applyLink": "https://jabil.com/apply"}, {"id": "7", "name": "Software Engineer", "location": "<PERSON><PERSON>", "type": "Full Time", "mode": "Hybrid", "company": {"name": "CelcomDigi", "logo": "/assets/images/careers/celcomdigi.jpg", "website": "https://celcomdigi.com", "sponsor_type": ["Gold"]}, "description": "Build and maintain scalable backend systems and services that power mobile connectivity for millions of users across Malaysia.", "requirements": ["Proficient in backend technologies like Node.js, Python, or Java", "Familiarity with REST APIs and database systems", "Experience working in an agile development environment"], "applyLink": "https://celcomdigi.com/jobs"}]}