import Swal, { type SweetAlertOptions } from "sweetalert2";

// widget.close();
const close = () => {
  Swal.close();
};

const alert = (option: SweetAlertOptions) => {
  option.confirmButtonColor = "#9D7F0B";
  option.cancelButtonColor = "#D43A42";
  return Swal.fire(option);
};

const confirm = (
  title = "Are you sure?",
  content = "You won't be able to revert this!",
  confirmButtonText = "Confirm"
) => {
  const config: SweetAlertOptions = {
    title,
    text: content,
    icon: "warning",
    showCancelButton: true,
    confirmButtonColor: "#9D7F0B",
    cancelButtonColor: "#D43A42",
    confirmButtonText,
  };

  return Swal.fire(config);
};

const alertSuccess = (
  title: string = "Success",
  content: string,
  duration?: number
) => {
  return Swal.fire({
    title: title,
    text: content,
    icon: "success",
    timer: duration,
    timerProgressBar: true,
  });
};

const success = (content: string, duration?: number) => {
  return Swal.fire({
    title: "Success",
    text: content,
    icon: "success",
    timer: duration,
    timerProgressBar: true,
  });
};

const warn = (...content: string[]) => {
  const formattedContent = content.map((item) => `<p>${item}</p>`).join("");
  return Swal.fire({
    title: "Warning",
    html: formattedContent,
    icon: "warning",
    showConfirmButton: true,
  });
};

const alertError = (
  title: string = "Error",
  content: string,
  e?: any,
  duration?: number
) => {
  return Swal.fire({
    title: title,
    text: content,
    icon: "error",
    timer: duration,
    timerProgressBar: true,
    html:
      import.meta.env.DEV && e
        ? `<div><h1 class='text-center text-lg'>${content}</h1><details class='bg-[#f8f9fe] text-left p-2 border border-zinc-200'><summary>Error detail</summary><p class='text-red-600'>${e}</p></details></div>`
        : content,
  });
};

const error = (content: string, e?: any) => {
  return Swal.fire({
    title: "Error",
    text: content,
    icon: "error",
    html:
      import.meta.env.DEV && e
        ? `<div><p class='text-center text-lg'>${content}</p><details class='bg-[#f8f9fe] text-left p-2 border border-zinc-200'><summary>Error detail</summary><p class='text-red-600'>${e}</p></details></div>`
        : content,
  });
};

const loading = (title: string = "Loading...") => {
  return Swal.fire({
    title,
    allowOutsideClick: false,
    showConfirmButton: false,
    allowEscapeKey: false,
    didOpen: () => {
      Swal.showLoading(null);
    },
  });
};

type IconType = "info" | "success" | "error" | "warning" | "question";

const toast = (title: string, icon: IconType = "info") => {
  const Toast = Swal.mixin({
    toast: true,
    position: "top-end",
    showConfirmButton: false,
    timer: 3000,
    timerProgressBar: true,
    didOpen: (toast) => {
      toast.addEventListener("mouseenter", Swal.stopTimer);
      toast.addEventListener("mouseleave", Swal.resumeTimer);
    },
  });

  Toast.fire({
    icon,
    title,
  });
};

const prompt = (
  title: string,
  confirmButtonText: string,
  inputPlaceholder: string
) => {
  return Swal.fire({
    title,
    input: "text",
    confirmButtonText,
    inputPlaceholder,
    cancelButtonColor: "#D43A42",
    allowOutsideClick: true,
    allowEscapeKey: true,
  });
};

const select = async (
  title: string,
  confirmButtonText: string,
  inputOptions: Record<string, string>
) => {
  const { value: result } = await Swal.fire({
    title,
    input: "select",
    inputOptions,
    inputPlaceholder: "SELECT",
    confirmButtonText,
    allowOutsideClick: false,
    allowEscapeKey: false,
    inputValidator: (value) => {
      return new Promise((resolve) => {
        if (value === "") {
          resolve("You need to select an option from the list.");
        } else {
          resolve("");
        }
      });
    },
  });
  return result;
};

export const widget = {
  alert,
  alertSuccess,
  alertError,
  close,
  success,
  warn,
  error,
  confirm,
  loading,
  toast,
  prompt,
  select,
};
