// -----------------------------------
// Import required package here
import React, { useState, useEffect, useRef } from "react";
// -----------------------------------

// -----------------------------------
// Declare type, interface, dummy data here
type ComponentType = {
  id: string;
}

const DUMMY_USER = {
  name: 'PIXEL2025',
  year: 2025
}
// -----------------------------------

// Component using arrow syntax
const Component: React.FC = (props: any) => {
  // -----------------------------------
  // Declare component const here
  const id = props;
  // -----------------------------------

  // -----------------------------------
  // Declare react hooks here
  const inputRef = useRef();
  const [name, setName] = useState("");

  useEffect(() => {}, []);
  // -----------------------------------

  // -----------------------------------
  // Declare component function here
  function handleSubmit() {}

  // -----------------------------------
  return (
    <>
      <h1>Hello!</h1>
    </>
  );
};

// Export component as default
export default Component;
