import department from "@data/department.json";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";

const StudentCommittee = () => {
  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
      {department.map((department) => (
        <div
          key={department.name}
          className="border-2 border-black bg-[#fffef5] rounded-lg overflow-hidden flex flex-col transition transform hover:shadow-lg hover:scale-[1.02]"
        >
          <div className="w-full">
            <LazyLoadImage
              src={`/assets/images/department/${department.photo}`}
              alt={department.name}
              className="w-full lg:min-h-[390px] object-cover block border-b-2 border-black"
              effect="blur"
            />
          </div>
          <div className="pb-1 px-2 text-center">
            <h3 className="text-black text-sm md:text-lg font-semibold uppercase tracking-wide uppercase">
              {department.name}
            </h3>
          </div>
        </div>
      ))}
    </div>
  );
};

export default StudentCommittee;
