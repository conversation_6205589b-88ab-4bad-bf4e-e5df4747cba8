import Rating from "@mui/material/Rating";
import type { Dispatch, SetStateAction } from "react";
import { useState } from "react";
import { EvaluationData } from "@modules/judge";

import evaluationSubcategories from "@data/judging-rubrics.json";

type EvaluationRatingProps = {
  name: string;
  label: string;
  data: Partial<EvaluationData>;
  setValue: Dispatch<SetStateAction<Partial<EvaluationData>>>;
};

const evaluationSubcategoriesMax = evaluationSubcategories.reduce(
  (acc: Record<string, number>, curr: any) => {
    acc[curr.name] = curr.max;
    return acc;
  },
  {}
);

function EvaluationRating({
  name,
  label,
  data,
  setValue,
}: EvaluationRatingProps) {
  const [hover, setHover] = useState<number | null>(null);

  const noOfStars = 10; // Number of stars for the rating

  // Helper to format number: show decimal only if needed
  const formatValue = (value: number) =>
    Number.isInteger(value) ? value : value.toFixed(1);

  // Get the current value in /10 scale for display
  const getDisplayValue = () => {
    if (hover !== null && hover !== -1) {
      return hover;
    }
    if (data && typeof data[name] === "number") {
      // Convert actual value to /10 scale
      return formatValue(
        ((data[name] as number) * noOfStars) / evaluationSubcategoriesMax[name]
      );
    }
    return 0;
  };

  return (
    <div className="w-full flex flex-col md:flex-row justify-center items-center md:justify-between md:gap-x-32 gap-y-5 border-b-2 border-dotted  border-b-[rgba(0,0,0,0.2)] pb-4">
      <div className="flex flex-row gap-2 items-center">
        <p className="font-bold text-xl">{label}</p>
        <p className="text-base">
          &#40;
          <span className={hover !== null && hover !== -1 ? "text-infoColor" : undefined}>
            {getDisplayValue()}/10
          </span>
          &#41;
          <span className="text-md text-dangerColor"> *</span>
        </p>
      </div>
      <Rating
        name={name}
        defaultValue={0}
        max={noOfStars}
        size="large"
        value={
          data && typeof data[name] === "number"
            ? Math.round(
                ((data[name] as number) * noOfStars) / evaluationSubcategoriesMax[name]
              )
            : 0
        }
        onChange={(_, newValue) => {
          setValue({
            ...data,
            [name]:
              (evaluationSubcategoriesMax[name] * (newValue as number)) / noOfStars,
          });
          setHover(newValue as number);
        }}
        onChangeActive={(_, newHover) => {
          setHover(newHover);
        }}
      />
    </div>
  );
}

export default EvaluationRating;
