# PIXEL Firebase Admin SDK Automation

This folder contains automation scripts using Firebase Admin SDK for PIXEL project.

## Prerequisites

1. Node.js installed (version 14 or higher recommended)
2. Firebase service account key (service-account.json)

## Setup Instructions

1. Place your Firebase service account key file:
   - Rename your Firebase service account JSON file to `test-service-account.json`
   - Place it in this directory (`appsScripts/automation/`)

2. Install dependencies:
   ```bash
   cd appsScripts/automation
   npm install
   ```

## Running the Test Script

To test Firebase connectivity and retrieve user information:

```bash
npm test
```

## Expected Output

If successful, you should see:
1. "Firebase Admin SDK initialized successfully!"
2. A list of up to 10 users from your Firebase project
3. "Firebase connection test completed successfully!"

## Troubleshooting

If you encounter any errors:

1. Verify that `service-account.json` is in the correct location
2. Check that the service account has the necessary permissions
3. Ensure your Firebase project is properly set up
4. Verify that the service account key is valid and not expired

## Security Note

- Never commit `service-account.json` to version control
- Keep your service account key secure
- Consider using environment variables for production use 