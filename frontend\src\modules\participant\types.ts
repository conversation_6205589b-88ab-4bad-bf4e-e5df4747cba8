export interface Participant {
  id: string; // participant airtable id
  firebase_uid: string; 
  
  student_name: string;
  display_name: string; // shorter version of name to be displayed
  matric_no: string;
  student_major: string;
  ic: string;
  desc: string;
  
  photo: any; //image file
  
  agreement: boolean;
  photo_agreement: boolean;
  
  student_email: string;
  personal_email: string;
  
  github: string;
  linkedin: string;
  portfolio?: string;
  tel: string;
  discord_id: string;
  resume: string;
  created_at: Date;
  updated_at: Date;
  
  is_team: boolean;

  project_id: string | undefined;
  project_name: string | undefined;
  project_major: string | undefined;
  sdg: string | undefined;
  job_application_id: string[] | undefined;
}