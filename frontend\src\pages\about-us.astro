---
import SideLayout from "@layouts/sideLayout.astro";
import LecturerCommittee from "@components/aboutus/LecturerCommittee";
import StudentCommittee from "@components/aboutus/StudentCommittee";
import { proxyApi } from "@//utils";

// import type { Lecturer } from "@components/aboutus/LecturerCommittee";

export const prerender = true;

// const result  = await proxyApi.get("lecturer-committees", {}, true);

// const data = result.map((lecturer: any) => {
//   return {
//     id: lecturer.id,
//     name: lecturer.name,
//     role: lecturer.role,
//     photo: lecturer.photo[0].url,
//     url: lecturer.url,
//   };
// }) as Lecturer[];

// sort the lecturers by role
// data.sort((a, b) => a.role.localeCompare(b.role));

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "About", path: "/about-us" },
];

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
---

<SideLayout
  title="About PIXEL"
  pageDesc="One of the CS Society USM flagship events, brought to you by the joint effort of the USM CS lecturer and student committees."
  {breadcrumbs}
>
  <div
    class="flex max-md:flex-wrap max-md:gap-y-2 gap-x-5 *:leading-tight md:*:leading-none *:align-text-bottom justify-center items-center *:text-center [&>a]:font-medium [&>a]:text-mobile-14 [&>a]:py-1 [&>a]:px-2 [&>a]:rounded-[4px]"
    slot="menu"
  >
    <a
      href="#about-pixel"
      class="hover:bg-primary-6 hover:text-white transition-all ease-in-out"
      >What is PIXEL?</a
    >
    <div class="border-l-2 h-[18px] border-black"></div>
    <a
      href="#lecturer-committees"
      class="hover:bg-primary-6 hover:text-white transition-all ease-in-out"
      >Lecturer Committees</a
    >
    <div class="border-l-2 h-[18px] border-black"></div>
    <a
      href="#student-committees"
      class="hover:bg-primary-6 hover:text-white transition-all ease-in-out"
      >Student Committees</a
    >
  </div>

  <!-- What is PIXEL? -->
  <a id="about-pixel" class="anchor"></a>
  <section class="mt-16 lg:mt-24 flex flex-col mx-auto w-full lg:w-fit gap-4">
    <h2 class="flex flex-col text-mobile-20 font-semibold">WHAT IS PIXEL?</h2>
    <div class="flex max-md:flex-col gap-6 md:gap-8">
      <p class="text-mobile-16 md:w-[40ch] text-justify">
        Project Innovation & eXploration in CS Education and Learning (PIXEL
        {currentYear}) is a dynamic platform that is held annually, specially curated for
        final year Computer Science students to showcase their creative and
        innovative final year projects. PIXEL {currentYear} is organised by the Computer
        Science Society in collaboration with the School of Computer Sciences,
        Universiti Sains Malaysia.
      </p>
      <p class="text-mobile-16 md:w-[40ch] text-justify">
        PIXEL {currentYear} ensures that students get valuable feedback from industry
        professionals, enhance their skills and be well-equipped for future
        challenges in the tech world. PIXEL {currentYear} serves as a podium for students
        to explore and delve into varieties of ideas and innovations and
        discover beyond the horizons.
      </p>
    </div>
  </section>

  <!-- Lecturer Committees -->
  <a id="lecturer-committees" class="anchor"></a>
  <section
    class="mt-16 md:mt-20 lg:mt-24 flex flex-col gap-4 md:gap-12 lg:gap-14 mx-auto w-full"
  >
    <h2 class="flex flex-col text-mobile-20 font-semibold md:place-self-center uppercase">
      Lecturer Committees
    </h2>
    <LecturerCommittee client:only="react" />
  </section>

  <!-- Student Committees -->
  <a id="student-committees" class="anchor"></a>
  <section
    class="mt-16 md:mt-20 lg:mt-24 flex flex-col gap-4 md:gap-12 lg:gap-14 mx-auto w-full"
  >
    <h2 class="flex flex-col text-mobile-20 font-semibold md:place-self-center uppercase">
      Student Committees
    </h2>
    <StudentCommittee client:only="react" />
  </section>
</SideLayout>
