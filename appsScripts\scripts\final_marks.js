function updateFinalMarks() {
  /**
    *
    * @desc
    * This function generates final evaluation results for each project
    * by aggregating data from the normalization and evaluations sheets.
    * 
    * For each project, it calculates:
    * - project_mean: average of raw marks
    * - final_z: average of z-score sums
    * - z_score: normalized z-score (scaled to 0–100)
    * - weighted_mean and weighted_z: used to compute the final_score
    * 
    * It also gathers team members and judge info per project.
    * 
    * The final summary is written to the final_marks sheet.
    * 
    */

  Logger.log("Updating final_marks sheet ...");

  const normSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName('normalization');
  const finalSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName('final_marks');
  const evalSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName('evaluations');

  // Helper to extract a single value from semicolon-separated fields
  const extractSingleValue = (row, fieldName) => {
    const val = row[colIndex(fieldName)];
    return typeof val === 'string' ? val.split(';')[0] : val;
  };

  // Extract team member map, mark map, and evaluation map from evaluations sheet
  const evalHeaders = evalSheet.getRange(1, 1, 1, evalSheet.getLastColumn()).getValues()[0];
  const evalData = evalSheet.getRange(2, 1, evalSheet.getLastRow() - 1, evalHeaders.length).getValues();
  const evalCol = name => evalHeaders.indexOf(name);

  const teamMap = {};   // project_id => team_members
  const markMap = {};   // project_id => list of marks
  const evaluationsMap = {}; // project_id => list of "evaluation_id,judge_id,judge_name"

  evalData.forEach(row => {
    const evalId =  row[evalCol('evaluation_id')];
    const judgeId = row[evalCol('judge_id')];
    const judgeName = row[evalCol('judge_name')];
    const pid = row[evalCol('project_id')];
    const members = row[evalCol('team_members')];
    const mark = row[evalCol('mark')];

    if (pid && members && !teamMap[pid]) {
      teamMap[pid] = members;
    }

    if (pid && mark !== '') {
      if (!markMap[pid]) markMap[pid] = [];
      markMap[pid].push(mark);
    }

    if (pid && mark !== '') {
      if (!evaluationsMap[pid]) evaluationsMap[pid] = [];
      evaluationsMap[pid].push(`${evalId},${judgeId},${judgeName}`);
    }
  });

  // Load normalization sheet
  const normHeaders = normSheet.getRange(1, 1, 1, normSheet.getLastColumn()).getValues()[0];
  const normData = normSheet.getRange(2, 1, normSheet.getLastRow() - 1, normHeaders.length).getValues();
  const colIndex = name => normHeaders.indexOf(name);

  const projectMap = {}; // project_id => { metadata + scores }

  normData.forEach(row => {
    const projects = row[colIndex('project_id')].split(';');
    const names = row[colIndex('project_name')].split(';');
    const majors = row[colIndex('project_major')].split(';');
    const sdgs = row[colIndex('sdg')].split(';');

    // sometimes zSums only consists of a single number
    const rawZ = row[colIndex('project_z_sum')];
    const zSums = typeof rawZ === 'string' ? rawZ.split(';').map(Number) : [Number(rawZ)];

    for (let i = 0; i < projects.length; i++) {
      const pid = projects[i];

      if (!projectMap[pid]) {
        projectMap[pid] = {
          project_id: pid,
          project_name: names[i],
          project_major: majors[i],
          sdg: sdgs[i],
          team_members: teamMap[pid] || '',
          evaluations: evaluationsMap[pid] || [],
          mark: markMap[pid] || [],
          project_z_sum: [],
        };
      }

      projectMap[pid].project_z_sum.push(zSums[i]);
    }
  });

  // Prepare output
  const output = [];

  for (let pid in projectMap) {
    const p = projectMap[pid];

    // Compute average of marks
    const numericMarks = p.mark.map(Number).filter(v => !isNaN(v));
    const project_mean = (numericMarks.reduce((a, b) => a + b, 0) / numericMarks.length).toFixed(2);

    // Compute average of z-scores
    const final_z = (p.project_z_sum.reduce((a, b) => a + b, 0) / p.project_z_sum.length).toFixed(2);

    // Normalize z-score to 0–100 scale
    const z_score = ((parseFloat(final_z) + 9) / 18 * 100).toFixed(4);

    // Weighted mean and z for final score
    const weighted_mean = (project_mean * PUBLIC_PIXEL_ALPHA).toFixed(4);
    const weighted_z = (z_score * (1 - PUBLIC_PIXEL_ALPHA)).toFixed(4);
    const final_score = parseFloat(weighted_mean) + parseFloat(weighted_z);

    output.push([
      p.project_id,
      p.project_name,
      p.project_major,
      p.sdg,
      p.team_members,
      p.evaluations.join(';'),
      p.mark.join(';'),
      project_mean,
      p.project_z_sum.join(';'),
      final_z,
      z_score,
      weighted_mean,
      weighted_z,
      final_score
    ]);
  }

  // Write to final_marks sheet
  const headers = [
    'project_id',
    'project_name',
    'project_major',
    'sdg',
    'team_members',
    'evaluations',
    'mark',
    'project_mean',
    'project_z_sum',
    'final_z',
    'z_score',
    'weighted_mean',
    'weighted_z',
    'final_score'
  ];

  finalSheet.clearContents();
  finalSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  finalSheet.getRange(2, 1, output.length, headers.length).setValues(output);

  Logger.log("✅ Final marks with correct judges and scores written.");
}