import { useEffect } from "react";

function DraftCheckbox() {
  useEffect(() => {
    const draftAck = document.getElementById("draftack") as HTMLInputElement;
    draftAck.value = "true";
  }, []);

  return (
    <div className="py-2 text-sm md:text-base text-left">
      <div className="mb-5 pl-1 md:pl-3">
        <p className="mb-3">
          <strong>This is a temporary save.</strong> Your project is <span className="text-red-600 font-semibold">not submitted</span> yet.
        </p>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            You can continue editing your project and return to submit it later.
          </li>
          <li>
            Only projects submitted via the <strong>"Submit Project"</strong> button will be reviewed.
          </li>
          <li>
            Make sure to complete all required sections before the submission deadline.
          </li>
        </ul>
      </div>

      <p className="font-bold text-center my-3 p-2 rounded border bg-yellow-50">
        Your draft is safe, don’t forget to submit it when everything is ready.
      </p>

      <input type="hidden" id="draftack" name="draftack" value="false" readOnly />
    </div>
  );
}

export default DraftCheckbox;
