export function onRequest(context, next) {
  const { request } = context;
  const url = new URL(request.url);

  const blockMode = import.meta.env.PUBLIC_BLOCKMODE === "true";
  const blockPaths = import.meta.env.PUBLIC_BLOCKPATH
    ? import.meta.env.PUBLIC_BLOCKPATH.split(",").map((p) => p.trim())
    : [];

  const shouldBlock = blockMode && blockPaths.some((path) => url.pathname.startsWith(path));

  if (shouldBlock) {
    let host = request.headers.get("host");

    // some bugs happen here @WenHao1223, try to fix it later
    // If in production and host is localhost, use your actual domain
    if (import.meta.env.PROD && (host?.includes('localhost') || !host)) {
      host = 'pixelusm.com';
    }
    
    const protocol = url.protocol || "https:";
    const redirectBase = host ? `${protocol}//${host}` : url.origin;

    return new Response(null, {
      status: 302,
      headers: {
        Location: `${redirectBase}/404`,
      },
    });
  }

  return next();
}
