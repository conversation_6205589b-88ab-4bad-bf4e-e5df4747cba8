import React, { useState, useRef, useEffect, useContext } from "react";
import { useStore } from "@utils/store";

const SORT_BY_TYPE = [
  {
    name: "Shuffle",
    icon: "fa-solid fa-shuffle",
  },
  {
    name: "From A-Z",
    icon: "fa-solid fa-arrow-down-a-z",
  },
  {
    name: "From Z-A",
    icon: "fa-solid fa-arrow-down-z-a",
  },
  {
    name: "From Awards",
    icon: "fa-solid fa-trophy",
  },
];

const ProjectSort: React.FC = () => {
  const { sort, setSort } = useStore();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleSort = (sortTypeId: string) => {
    // if the current sort is Shuffle and the user selects Reshuffle, set the sort to Reshuffle
    if (sort === "Shuffle" && sortTypeId === "Shuffle") {
      setSort("Reshuffle");
    } else if (sort === "Reshuffle" && sortTypeId === "Shuffle") {
      setSort("Shuffle");
    } else if (sortTypeId === "Shuffle") {
      setSort("Shuffle");
    } else {
      setSort(sortTypeId);
    }
  };

  return (
    <>
      {SORT_BY_TYPE.map((sortType) => (
        <input
          key={sortType.name}
          id={sortType.name}
          type="radio"
          name="sortType"
          value={sortType.name}
          className="hidden"
          onInput={(e: any) => setSort(e.target.value)}
        />
      ))}

      <div ref={dropdownRef} className="relative">
        {/* Label */}
        <div
          className="cursor-pointer flex flex-row items-center gap-2 font-medium w-fit justify-end"
          onClick={toggleDropdown}
        >
          <span className="text-grey-4 ">Sort:</span>
          <span className="whitespace-nowrap">
            {sort === "Reshuffle" ? "Shuffle" : sort}
          </span>
          <i
            className={`fa  ${
              isOpen ? "fa-chevron-circle-up" : "fa-chevron-circle-down"
            }`}
            aria-hidden="true"
          ></i>
        </div>

        {/* Dropdown Items */}
        {isOpen && (
          <ul className="absolute z-10 py-2 bg-white overflow-hidden rounded-md border border-gray-200 shadow mt-2 right-0">
            {SORT_BY_TYPE.map((sortType) => (
              <label
                htmlFor={sortType.name}
                key={sortType.name}
                className="hover:cursor-pointer"
                onClick={() => handleSort(sortType.name)}
              >
                <li
                  className={`px-6 py-2 hover:bg-gray-100 cursor-pointer whitespace-nowrap text-start font-medium ${
                    sort === sortType.name ? "bg-gray-100" : "hover:bg-gray-100"
                  }`}
                  onClick={() => {
                    setIsOpen(false);
                  }}
                >
                  <i className={`${sortType.icon} mr-3`}></i>
                  {(sort === "Shuffle" || sort === "Reshuffle") &&
                  sortType.name === "Shuffle"
                    ? "Reshuffle"
                    : sortType.name}
                </li>
              </label>
            ))}
          </ul>
        )}
      </div>
    </>
  );
};

export default ProjectSort;
