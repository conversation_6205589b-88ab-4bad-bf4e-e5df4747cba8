import { useStore } from "@utils/store";

type Filter = {
  filterName: string;
  shortName?: string;
};

const Filter: React.FC<{ filters: Filter[] }> = ({ filters }) => {
  const { filter, setFilter } = useStore();
  return (
    <>
      {filters.map((filterItem, index, array) => (
        <div className="flex items-center gap-2" key={index}>
          <button
            type="button"
            className={`group mx-1 text-white hover:bg-primary-6 rounded-[4px] ${
              filter === filterItem.filterName ? "bg-primary-6" : ""
            } transition-all ease-in-out`}
            onClick={() => setFilter(filterItem.filterName)}
          >
            <div
              className={`group-hover:text-white ${
                filter === filterItem.filterName
                  ? "*:text-white"
                  : "*:text-black"
              } px-3 py-2 *:font-medium *:text-mobile-14 *:text-center group-hover:*:text-white group`}
            >
              <p className="xl:block hidden">{filterItem.filterName}</p>
              <p className="max-xl:block hidden ">{filterItem.shortName}</p>
            </div>
          </button>
          {index < array.length - 1 && (
            <span className="font-bold font-inter text-mobile-14">/</span>
          )}
        </div>
      ))}
    </>
  );
};

export default Filter;