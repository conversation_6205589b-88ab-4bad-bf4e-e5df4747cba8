---
import SideLayout from "@layouts/sideLayout.astro";
import PartnersList from "@components/partners/PartnersList";
import Filter from "@components/global/Filter";
import { proxyApi } from "@/utils";

// export const prerender = true;

const data = await proxyApi.get(
  "partners",
  {
    fields: "name, desc, tier",
  },
  true
);

const filters = [
  "All",
  "Program",
  "Media",
].map((filter) => ({
  filterName: filter,
}));

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "Partners", path: "/partners" },
];
---

<SideLayout
  title="Partners"
  pageDesc="The elite panel of industry experts ready to evaluate submitted projects."
  breadcrumbs={breadcrumbs}
>
  <!-- Hide for PIXEL 2025 since only media partners involve -->
  <!-- <div
    slot="menu"
    class="flex max-md:flex-wrap max-md:gap-y-2 gap-x-1 *:leading-tight md:*:leading-none *:align-text-bottom justify-center items-center *:text-center [&>button]:font-medium [&>button]:text-mobile-14 [&>button]:py-1 [&>button]:px-2 [&>button]:rounded-[4px] [&_p]:block"
  >
    <Filter filters={filters} client:load/>
  </div> -->

  <!-- Gallery -->
  <PartnersList client:load data={data} />
</SideLayout>
