// This blocs contain the AuthFactory which consists of the
// sign up, sign in, create account, reset password, and update account functions
// However, it uses the Firebase Authentication API to handle the authentication process

import type { ParticipantInfo, Credential, ParticipantSession } from "./types";
import {
  widget,
  auth,
  actionSessionStorage,
  sanitizeData,
  proxyApi,
  db,
  firebaseStorage,
} from "@/utils";
import { getStorage, ref } from "firebase/storage";
import { participantFactory, teamFactory } from "@/modules";
import {
  sendEmailVerification,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendPasswordResetEmail,
} from "firebase/auth";
import { isUserJudgeAndStoreToSessionStorage } from ".";

export function authFactory() {
  /**
   * @param signUpCredential
   * @description
   * This function creates a new user account with the email and password provided.
   */
  async function signUp(signUpCredential: Credential) {
    // Firebase action code settings for email verification link to the register page
    // This is the action code setting (instruction) in the email verification link
    const actionCodeSettings = {
      url: window.location.origin + "/register",
      handleCodeInApp: true,
    };

    widget.loading();

    try {
      // This one is to create user account using email and password
      // Also verifies first if the email is functionable
      const auth1 = await createUserWithEmailAndPassword(
        auth,
        signUpCredential.email,
        signUpCredential.password
      );

      // Send email verification link to the user
      await sendEmailVerification(auth1.user, actionCodeSettings);
      await widget
        .alertSuccess(
          "Success",
          "Your account has been created. Please check your email for verification.",
          2000
        )
        .then(() => {
          // Redirect to the login page after successful account creation
          window.location.href = "/sign-in";
        });

      // Error handling when creating an account
    } catch (error) {
      const errorCode = error.code;
      let errorMessage = error.message;

      // Error message for different error codes
      switch (errorCode) {
        case "auth/weak-password":
          errorMessage = "Password selected is too weak.";
          break;
        case "auth/email-already-in-use":
          errorMessage = "Email address already in use.";
          break;
        case "auth/invalid-email":
          errorMessage = "Email address is invalid.";
          break;
        default:
          errorMessage = error;
          break;
      }
      throw new Error(errorMessage);
    }
  }

  /**
   * @param loginCredential
   * @description
   * This function logs in the user with the email and password provided.
   */
  async function signIn(loginCredential: Credential): Promise<void> {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        loginCredential.email,
        loginCredential.password
      );

      const user = userCredential.user;

      // get the user entered email and its uid based on the credentials return by the firebase authentication method
      await isUserJudgeAndStoreToSessionStorage(
        user,
        loginCredential.email,
        user.uid,
        "login"
      );
    } catch (error) {
      let errorMessage = error.message;
      switch (error.code) {
        case "auth/invalid-email":
          errorMessage = "Email address is badly formatted.";
          break;
        case "auth/too-many-requests":
          errorMessage = "Too many login attempts. Please try again later.";
          break;
        case "auth/invalid-credential":
          errorMessage = "Either email address or password is incorrect.";
          break;
        case "auth/user-not-found":
          errorMessage = "Account not found. <NAME_EMAIL> to create your account.";
          break;
        case "auth/wrong-password":
          errorMessage = "Incorrect password. Please try again.";
          break;
        default:
          errorMessage =
            "Something is wrong" +
            (error.code ? ": " + error.code : "") +
            ". Have you verify your email?";
          break;
      }
      await widget.alertError("Error", errorMessage);
    }
  }

  /**
   * @param email
   * @description
   * This function sends a reset password email to the email provided.
   */
  async function sendResetPasswordEmail(email: string) {
    try {
      sendPasswordResetEmail(auth, email);
      widget.success("Reset password email sent");
    } catch (error) {
      let errorMessage = error.message;
      switch (error.code) {
        case "auth/user-not-found":
          errorMessage = "The email address you entered is not registered.";
          break;
        case "auth/invalid-email":
          errorMessage = "The email address you entered is badly formatted.";
          break;
        case "auth/too-many-requests":
          errorMessage = "Too many login attempts. Please try again later.";
          break;
        case "auth/wrong-password":
          errorMessage = "The password you entered is incorrect.";
          break;
        default:
          errorMessage =
            "Something is wrong" +
            (error.code ? ": " + error.code : "") +
            ". Have you verify your email?";
          break;
      }
      await widget.alertError("Something seems wrong!", errorMessage);
    }
  }

  /**
   * @param registerInfo
   * @param userId
   *
   * @description
   * This function creates a new record in airtable with the provided data in /register page.
   */
  async function createAccount(
    registerData: Partial<ParticipantInfo>
  ): Promise<void> {
    try {
      registerData = sanitizeData(registerData);

      // Get fresh CSRF token before making the request
      await proxyApi.getCSRFToken();

      await proxyApi.post("participants", registerData, true);
    } catch (error) {
      console.error("Create account error:", error);
      throw new Error("Error in createAccount: " + error);
    }
  }

  /**
   * @param registerInfo
   * @param userId
   *
   * @description
   * This function updates the record of matching userId in airtable with the provided data in /participant/edit page.
   */
  const updateAccount = async (
    registerInfo: Partial<ParticipantSession>,
    userId: string
    // publicUrl?: string
  ): Promise<void> => {
    try {
      await participantFactory().updateParticipant(registerInfo as any, userId);

      actionSessionStorage().update("userData", registerInfo);
    } catch (error) {
      widget.alertError("Error in updateAccount", error);
      throw new Error("Error in updateAccount: " + error);
    }
  };

  return {
    signUp,
    signIn,
    sendResetPasswordEmail,
    createAccount,
    updateAccount,
  };
}
