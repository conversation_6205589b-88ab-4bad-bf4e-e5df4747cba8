// parameter (tour object, message, element and position of the message)
function nextStep(tour: any, text: string, element: string, on: string) {
  tour.addStep({
    text: text,
    attachTo: {
      element: element,
      on: on,
    },
    buttons: [
      {
        action(this): any {
          return this.back();
        },
        classes: "shepherd-button-secondary",
        text: "Back",
      },
      {
        action(this): any {
          return this.next();
        },
        text: "Next",
      },
    ],
  });
}

export default function startTour() {
  const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

  // @ts-ignore
  const tour = new Shepherd.Tour({
    useModalOverlay: true,
    defaultStepOptions: {
      cancelIcon: {
        enabled: true,
      },
      classes: "shadow-lg shepherd-theme-arrows",
      scrollTo: { behavior: "smooth", block: "center" },
    },
    header: {
      classes: "bg-cs-blue3",
    },
  });

  tour.addStep({
    title: `Welcome To PIXEL ${currentYear} Judge Dashboard!`,
    text: `This is the judge dashboard for PIXEL ${currentYear}. You can see your profile and projects to be evaluated here. Click "Next" to continue.`,
    attachTo: {
      element: "body",
    },
    buttons: [
      {
        action(): any {
          return this.next();
        },
        text: "Next",
      },
    ],
  });

  nextStep(
    tour,
    "In desktop, hovering this sidebar will show your profile details.",
    "#sidebar",
    "left"
  );
  nextStep(
    tour,
    "You can log out from your current session by clicking this button.",
    "#logoutbtn",
    "bottom"
  );

  nextStep(
    tour,
    "This is the countdown indicating the deadline of the project evaluation.",
    "#countdowntopbar",
    "bottom"
  );

  nextStep(
    tour,
    "2 different icons and colours indicate the status of any project evaluation.",
    "#info-evaluation",
    "bottom"
  );

  nextStep(
    tour,
    "This is the progress bar which indicates the number of project you have evaluated.",
    "#evaluate-progressbar",
    "bottom"
  );

  tour.addStep({
    title: "End of tutorial",
    text: 'Good luck evaluating the projects!',
    attachTo: {
      element: "body",
    },
    buttons: [
      {
        action(): any {
          return this.next();
        },
        text: "Done",
      },
    ],
  });

  tour.start();
}
