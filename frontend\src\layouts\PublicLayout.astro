---
import Navbar from "@components/navbar/Navbar.astro";
import "./layout.css";
import Breadcrumb, {
  Breadcrumb as BreadcrumbType,
} from "@components/general/Breadcrumb.astro";

export interface Props {
  title: string;
  breadcrumbs?: BreadcrumbType[];
}

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const { title, breadcrumbs } = Astro.props;
let openNavbar = false;
---

<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="keywords" content="PIXEL" />

    <meta name="author" content="USM Computer Science Society" />
    <meta name="copyright" content="" />
    <meta name="application-name" content="PIXEL 2025" />

    <meta name="color-scheme" content="light only" />

    <!-- For Facebook -->
    <meta property="og:title" content="PIXEL 2025" />
    <meta property="og:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/assets/images/pixel_thumbnail.webp" />
    <meta property="og:url" content="https://pixelusm.com/" />

    <!-- For Twitter -->
    <meta name="twitter:card" content="summary" />
    <meta name="twitter:title" content="PIXEL 2025" />
    <meta name="twitter:description" content="Discover Beyond Horizons at PIXEL 2025! Witness tech revolution through our Final Year Students’ groundbreaking and innovative projects." />
    <!-- TBU -->
    <meta name="twitter:image" content="/assets/images/pixel_thumbnail.webp" />

    <meta name="viewport" content="width=device-width" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/favicon/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/assets/favicon/apple-touch-icon.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/assets/favicon/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/assets/favicon/favicon-16x16.png"
    />
    <link rel="manifest" href="/assets/favicon/site.webmanifest" />

    <!-- FontAwesome cdn css -->
    <link
      rel="stylesheet"
      href="https://site-assets.fontawesome.com/releases/v6.5.2/css/all.css"
    />

    <!-- GSAP cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
    ></script>

    <!-- ScrollTrigger cdn js -->
    <script
      crossorigin="anonymous"
      src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"
    ></script>

    <script>
      //@ts-ignore
      gsap.registerPlugin(ScrollTrigger);
    </script>

    <meta name="generator" content={Astro.generator} />
    <title>{title} | PIXEL {currentYear}</title>
    <script src="@/utils/pageAccess.ts"></script>
  </head>
  <body class="!m-0">
    <div id="main-modal"></div>
    <Navbar />
    <div class="h-28"></div>
    <div class="container-max-w mx-auto px-4 md:px-5 xl:px-0">
      <Breadcrumb {breadcrumbs} />
    </div>
    <slot />
    <script
      is:inline
      src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"
    ></script>
    <!-- AOS cdn js -->
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <!-- AOS script cdn js -->
    <script is:inline src="https://unpkg.com/aos@next/dist/aos.js"></script>
    <script is:inline>
      AOS.init();
    </script>
  </body>
</html>
