const AirtableProjectView = () => {
  return (
    <>
      <div className="flex gap-3 mb-5">
        <div className="bg-yellow-300 w-4 rounded-md" />
        <p className="font-semibold">Airtable Project View</p>
      </div>

      <p className="mb-2">
        Note: This is where you can monitor projects assignation and evaluation.
      </p>

      <iframe
        className="airtable-embed"
        src="https://airtable.com/embed/appVbcSklEhhwzV2d/shrN5FnF8ezYxsXHF?viewControls=on"
        width="100%"
        height="533"
        style={{
          background: "transparent",
          border: "1px solid #ccc",
        }}
      ></iframe>
    </>
  );
};

export default AirtableProjectView;
