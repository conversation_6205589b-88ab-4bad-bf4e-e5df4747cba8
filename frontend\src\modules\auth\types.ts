// types.ts as in V HACK
// define the types of the data that we are going to send to the backend

import { Timestamp } from "firebase/firestore";

/**
 * @description
 * This type is used to create a new user account in Firebase (in /sign-up), or authenticate a user in Firebase (in /sign-in).
 */
export type Credential = {
  email: string;
  password: string;
  confirm_password?: string; // for sign up only
};

/**
 * @description
 * * This type represents the data structure of a participant during registration (in /register).
 */
export type ParticipantInfo = {
  id: string;
  firebase_uid: string;

  student_name: string;
  display_name: string;
  matric_no: string;
  student_major: string;
  ic: string;
  desc: string;
  
  photo: string; //image file
  agreement: boolean;
  photo_agreement: boolean;
  resume_agreement: boolean;

  student_email: string;
  personal_email: string;

  year: string; 

  github: string;
  linkedin: string;
  portfolio?: string;
  tel: string;
  discord_id: string;
  resume: string;
  created_at: Date;
  updated_at: Date;
};

/**
 * @description
 * This type represents the data structure of a user session.
 */
export type ParticipantSession = ParticipantInfo & {
  is_team: boolean;
  is_special_access_granted: boolean;

  project_id: string | undefined;
  project_name: string | undefined;
  project_major: string | undefined;
  sdg: string | undefined;
  job_application_id: string[] | undefined;
};

/**
 * @description
 * This type represents the data structure of a judge session.
 */
export type JudgeSession = { 
  id: string; 
  firebase_uid: string;
  email: string;
  name: string;
  salutation: string;
  // photo: any;
  organization?: string;
  position: string;
  country: string;
  linkedin: string;
  major: string;

  assigned_projects_id?: string[];
  assigned_projects_year?: string[];

  project_name?: string[];
  project_major?: string[];
  sdg?: string[];

  evaluation_id?: string[];
  evaluated_projects_id?: string[];
  evaluated_projects_name?: string[];
  evaluated_projects_year?: string[];

  comments?: string[];
};
