import type { Participant } from "./index";
import { widget, sanitizeData, proxyApi } from "@/utils";

export function participantFactory() {
  /**
   * Get participant data from airtable using airtable_uid
   * @param {string} airtable_uid - airtable uid of the participant
   * @returns {Promise<Participant>} participant data
   */
  async function getParticipantAirtable(
    airtable_uid: string
  ): Promise<Participant> {
    try {
      const response = await proxyApi.first("participants", airtable_uid);

      return response;
    } catch (error) {
      throw new Error("Error in getParticipantAirtable: " + error);
    }
  }

  /**
   * Get participant data from airtable using firebase_uid when user logs in
   * @param {string} firebase_uid - firebase_uid of the participant
   * @returns {Promise<Participant>} participant data
   */
  async function getParticipant(firebase_uid: string) {
    let participantData;
    try {
      const data = await proxyApi.get("participants", {
        filterByFormula: `firebase_uid="${firebase_uid}"`,
      });
      // no record of participant in airtable
      if (!data[0]?.id) {
        return null;
      }
      const getAirTableID = data[0]?.id;

      participantData = getParticipantAirtable(getAirTableID);

      return participantData;
    } catch (error) {
      throw new Error("error in getParticipant: " + error);
    }
  }

  // get members data from airtable
  async function getMembersAirtable(
    members_uid: string[]
  ): Promise<Participant[]> {
    try {
      let response: any;

      const { data } = await proxyApi.get("participants", {
        filterByFormula: `FIND("${members_uid}", {student_name})`,
      });

      response = data;

      // response = await proxyApi.get(
      //   "/participants/filter/student_name/" + members_uid
      // );

      return response.map((record: any) => {
        const { photo, ...otherFields } = record;

        return {
          photo: photo,
          ...otherFields,
        } as Participant[];
      });
    } catch (error) {
      throw new Error("Error in getMembersAirtable: " + error);
    }
  }

  // get members airtable id from firebase uid
  async function getMembersAirtableId(uid: string): Promise<string> {
    try {
      const data = await proxyApi.get("participants", {
        filterByFormula: `firebase_uid="${uid}"`,
      });
      if (!data[0].id) throw new Error("No ID from getMembersAirtableId");
      // response = await proxyApi.get("/participants/filter/firebase_uid/" + uid);

      return data[0].id;
    } catch (error) {
      throw new Error("Error in getMembersAirtableId: " + error);
    }
  }

  async function updateParticipant(
    data: Omit<Participant, "id" | "created_at"> &
      Partial<Pick<Participant, "updated_at">> | any,
    uid: string
  ): Promise<void> {
    //  console.log(data)
    data["updated_at"] = new Date();

    data = sanitizeData(data);

    try {
      // console.log("ID from updateParticipant: ", uid);

      // response = await api.patch("/participants/" + id, data);

      // not using firebase docs anymore
      // await updateDoc(participantApi.participantDoc(id), data as any);

      // if (data.project_id) {
      //   const prevTeam = (await teamFactory().getTeam(data.project_id)) as any;
      //   //replace the old membersname with the new one from the data.name
      //   prevTeam.members_name[
      //     prevTeam.members_email.indexOf(data.student_name)
      //   ] = data.display_name;
      //   await teamFactory().updateTeam(prevTeam, data.project_id);
      // }

      // update participant data in airtable
      // let record: any;
      // let response = null;

      const recordId = await getMembersAirtableId(uid);
      // console.log("recordId", recordId);  
      
      await proxyApi.patch("participants", data, recordId);
    } catch (error) {
      throw new Error("Error in updateParticipant: " + error);
    }
  }

  // async function createParticipant(data: any, id: any) {
  //   // comment out for now by @koayck
  //   data["created_at"] = Timestamp.now();
  //   data = sanitizeData(data);
  //   await setDoc(participantApi.participantDoc(id), data);
  // }

  async function removeField(id: string, fieldName: string) {
    // comment out for now by @koayck
    let flag = false;
    try {
      // await updateDoc(participantApi.participantDoc(id), {
      //   [fieldName]: deleteField(),
      // });
      return (flag = true);
    } catch (error) {
      throw new Error("Error in removeField: " + error);
      // return flag;
    }
  }

  return {
    getParticipantAirtable,
    // getMembersAirtable,
    updateParticipant,
    // createParticipant,
    removeField,
    getParticipant,
  };
}
