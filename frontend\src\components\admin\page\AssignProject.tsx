import { adminFactory, Project, type Judge } from "@/modules";
import { widget } from "@/utils";
import { useContext, useState } from "react";
import { AdminContext } from "../Context";
import Loading from "@components/general/Loading";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

const AssignProject = () => {
  const {
    judges,
    projects,
    rerender,
    setRerender,
    assignedJudges,
    setAssignedJudges,
    evaluatedJudges,
    setEvaluatedJudges,
  } = useContext(AdminContext);

  const [availableJudges, setAvailableJudges] = useState<Judge[]>(judges);
  const [availableProjects, setAvailableProjects] =
    useState<Project[]>(projects);

  // assign project to selected judge
  async function handleSubmit(e: any) {
    e.preventDefault();

    if (
      e.target.judge.value === "DEFAULT" ||
      e.target.project.value === "DEFAULT"
    )
      return widget.alertError("Sorry", "Please fill all the fields");

    const selectedJudge = judges.find(
      (judge) => judge.id === e.target.judge.value
    );
    const selectedProjectId = e.target.project.value;

    try {
      if (
        selectedJudge.assigned_projects_id &&
        selectedJudge.assigned_projects_id.includes(selectedProjectId)
      )
        // if project is already assigned to selectedJudge
        return widget.alertError(
          "Sorry",
          "This project has already been assigned to this judge. Please select another project"
        );

      // confirm if user wants to assign the project
      const confirm = await widget.confirm(
        "Are you sure?",
        "Do you want to assign this project to " + selectedJudge.name + "?"
      );
      if (!confirm.isConfirmed) return;

      widget.loading();

      await adminFactory().assignProject(
        selectedJudge as Judge,
        selectedProjectId as string
      );

      // rerender the page to fetch the latest data
      setRerender(!rerender);
      handleReset();
    } catch (err) {
      widget.alertError(
        "Something went wrong while assigning the project",
        err
      );
    }
  }

  function handleSelectChange(e: any) {
    const { id, value } = e.target;

    if (id === "judge" && value !== "DEFAULT") {
      const selectedJudge = judges.find((judge) => judge.id === value);
      if (selectedJudge) {
        // filter projects so that only unevaluated projects that are not assigned to the judge selected can be selected
        const filteredProjects = projects.filter(
          (project) =>
            project.project_major === selectedJudge.major &&
            !selectedJudge.assigned_projects_id?.includes(project.id) &&
            project.year === currentYear
        );

        // Log the filtering process for debugging
        // projects.forEach((project) => {
        //   console.log(project.project_major, selectedJudge.major);
        //   console.log(project.year, currentYear, project.year === currentYear);
        //   console.log(
        //     selectedJudge.assigned_projects_id,
        //     project.id,
        //     !selectedJudge.assigned_projects_id?.includes(project.id)
        //   );
        //   console.log(
        //     project.project_major === selectedJudge.major,
        //     !selectedJudge.evaluated_projects_id?.includes(project.id),
        //     project.year === currentYear
        //   );
        //   console.log("-----------------");
        // });

        setAvailableProjects(filteredProjects);
        // console.log("Available Projects: ", filteredProjects);
      }
    } else if (id === "project" && value !== "DEFAULT") {
      const selectedProject = projects.find((project) => project.id === value);
      if (selectedProject) {
        // filter judges so that only judges who have not been assigned the project can be selected
        const filteredJudges = judges.filter(
          (judge) =>
            judge.major === selectedProject.project_major &&
            !assignedJudges
              .find((j) => j.id === judge.id)
              ?.assigned_projects_id?.includes(selectedProject.id) &&
            !evaluatedJudges
              .find((j) => j.id === judge.id)
              ?.evaluated_projects_id?.includes(selectedProject.id) &&
            judge.year?.includes(currentYear)
        );

        // Log the filtering process for debugging
        // console.log(judges[0]);
        // console.log(projects[0]);
        // judges.forEach((judge) => {
        //   console.log(judge.major, selectedProject.project_major);
        //   console.log(
        //     assignedJudges.find((j) => j.id === judge.id)?.assigned_projects_id,
        //     selectedProject.id
        //   );
        //   console.log(
        //     evaluatedJudges.find((j) => j.id === judge.id)
        //       ?.evaluated_projects_id,
        //     selectedProject.id
        //   );
        //   console.log(judge.year, currentYear);
        //   console.log(
        //     judge.major === selectedProject.major,
        //     !judge.assigned_projects_id?.includes(selectedProject.id),
        //     !judge.evaluated_projects_id?.includes(selectedProject.id),
        //     judge.year?.includes(currentYear)
        //   );
        //   console.log("-----------------");
        // });

        setAvailableJudges(filteredJudges);
        // console.log("Available Judges: ", filteredJudges);
      }
    } else {
      // reset available judges and projects if default option is selected
      setAvailableJudges(judges);
      setAvailableProjects(projects);
    }
  }

  // assign all projects to all judges
  async function handleAssignAllProjects() {
    let judgesCopy = [...judges];

    // only applies for current year, doesn't take previous year into account
    judgesCopy = judgesCopy.filter((judge) => judge.year == currentYear);

    // check if any of the judges has been assigned projects
    // to be removed once functionality to assign leftover projects to judges is implemented
    while (judgesCopy.length > 0) {
      const judge = judgesCopy.shift();
      if (judge?.assigned_projects_id?.length > 0) {
        // if first judge that has been assigned projects is found
        return widget.alertError(
          "Unsuccessful",
          "At least one judge has already been assigned projects."
        );
      }
    }

    try {
      // confirm if user wants to assign all projects to all judges
      const confirm = await widget.confirm(
        "Are you sure?",
        "Do you want to assign all projects to all judges?"
      );
      if (!confirm.isConfirmed) return;

      widget.loading();

      await adminFactory().assignAllProjects(judges, projects);

      // rerender the page to fetch the latest data
      setRerender(!rerender);
      handleReset();
    } catch (err) {
      widget.alertError(
        "Something went wrong while assigning the projects",
        err
      );
    }
  }

  function handleReset() {
    //reset select input value
    (document.getElementById("judge") as HTMLSelectElement).value = "DEFAULT";
    (document.getElementById("project") as HTMLSelectElement).value = "DEFAULT";
  }

  return (
    <section className="mb-4">
      <div className="flex gap-3 mb-4">
        <div className="bg-green-300 w-4 rounded-md" />
        <p className="font-semibold">Assign Project</p>
      </div>

      {/* Show loading state when the judges and projects is blank in the initial state */}
      {judges.length === 0 && projects.length === 0 ? (
        <div className="w-full text-center mx-auto flex justify-center col-span-3 my-5">
          <Loading />
        </div>
      ) : (
        <>
          <form id="assignProjectForm" onSubmit={handleSubmit}>
            <div className="flex flex-col gap-3">
              {/* input 1: judge to assign project to */}
              <div>
                <label
                  htmlFor="judge"
                  className="line-clamp-2 mb-2 text-base font-medium text-black"
                >
                  Select Judge
                </label>
                <select
                  className="daisy-select max-sm:w-full w-[60%]"
                  name="judge"
                  id="judge"
                  onChange={handleSelectChange}
                >
                  <option value="DEFAULT">-- Select Judge --</option>
                  {availableJudges.map((judge) => (
                    <option key={judge.id} value={judge.id}>
                      {judge.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* input 2: project to assign to the selected judge */}
              <div>
                <label
                  htmlFor="project"
                  className="line-clamp-2 mb-2 text-base font-medium text-black"
                >
                  Select Project
                </label>
                <select
                  className="daisy-select max-sm:w-full w-[80%]"
                  name="project"
                  id="project"
                  onChange={handleSelectChange}
                >
                  <option value="DEFAULT">-- Select Project --</option>
                  {availableProjects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.project_name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </form>
          <div className="flex items-center gap-3 mt-3">
            <button
              type="button"
              onClick={handleReset}
              className="daisy-btn text-white rounded transition hover:shadow border-none bg-grey-3 hover:bg-grey-4"
            >
              Reset
            </button>
            <button
              className="daisy-btn text-white rounded transition hover:shadow border-none bg-primary-6 hover:bg-primary-7"
              type="submit"
              form="assignProjectForm"
            >
              Assign Project
            </button>
            <span>OR</span>
            <button
              className="daisy-btn text-white rounded transition hover:shadow border-none bg-primary-6 hover:bg-primary-7"
              onClick={() => handleAssignAllProjects()}
            >
              Assign Projects to All Judges
            </button>
          </div>
        </>
      )}
    </section>
  );
};
export default AssignProject;
