---
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";
import PublicLayout from "@layouts/PublicLayout.astro";
import { decrypt } from "@/utils";

const { id } = Astro.params;

const isPostSubmission = import.meta.env.PUBLIC_IS_POST_SUBMISSION === "true";

let breadcrumbs = [
	{ name: "Home", path: "/" },
	{ name: "Shared Project", path: "/" + id },
];

if (isPostSubmission) {
	breadcrumbs.splice(1, 0, { name: "Projects", path: "/projects" });
}

const projectId = decrypt(id);
---

<PublicLayout title="Projects" {breadcrumbs}>
	<main class="bg-white h-full">
		<ProjectSubmissionView client:only="react" project_id={projectId} />
	</main>
</PublicLayout>
