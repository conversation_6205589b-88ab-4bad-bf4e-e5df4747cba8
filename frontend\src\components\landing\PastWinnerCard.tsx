import { useContext, useState } from "react";
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";
import ProjectModal from '@components/projects/ProjectModal';
import { Project } from "@modules/project/types";
import { LazyLoadImage } from "react-lazy-load-image-component";
import "react-lazy-load-image-component/src/effects/blur.css";
import { ProjectContext } from "@components/projects/Context";
import { encryptProject } from "@utils/utils";

type Props = {
  project: Project;
};

const PastWinnerCard: React.FC<Props> = ({ project }) => {
  const [isOpen, setIsOpen] = useState(false);

  const {prevProjectId, setPrevProjectId} = useContext(ProjectContext);

  const showProject = (id: string) => {
    // set current project id as the previous project id so that we can close it later when we open another project
    setPrevProjectId(id);
    // if there is already a project modal opened, close it, and reset the isOpen property for the previous project
    if (prevProjectId) {
      const prevProjectModal = document.getElementById(
        `modal-${encryptProject(prevProjectId)}`
      );

      prevProjectModal.style.display = "none";
    }

    setIsOpen(true);

    document.body.style.overflow = "hidden";
  };

  const closeProject = (id: string) => {
    setPrevProjectId("");
    document.body.style.overflow = "auto";
    setIsOpen(false);
  };

  return (
    <>
    {isOpen && (
        <ProjectModal onClose={closeProject} id={project.id} >
        <ProjectSubmissionView project={project} onClose={closeProject} />
      </ProjectModal>
      )}
      <div className="relative group cursor-pointer" onClick={() => showProject(project.id)}>
        <div className="relative rounded-md overflow-clip flex flex-col bg-white transition ease-in-out z-[1] h-full w-full">
          <div
            className={`relative ${
              project.project_major === "Software Engineering"
                ? "after:content-['SE'] after:bg-green-se-1"
                : project.project_major === "Intelligent Computing"
                ? "after:content-['IC'] after:bg-red-ic-1"
                : "after:content-['CI'] after:bg-blue-ci-1"
            }  after:absolute after:right-0 after:bottom-0  after:font-bold after:italic after:p-1 after:px-2 after:w-fit h-fit w-full max-md:min-h-[220px] lg:min-h-[180px] xl:min-h-[220px]`}
          >
            <div className="flex ">
              <LazyLoadImage
                src={project.thumbnail}
                alt={project.project_short_name ?? project.project_name}
                effect="blur"
              />
            </div>
          </div>
          <div className="flex py-3 px-4">
            <p className="!font-medium leading-tight">{project.project_short_name ?? project.project_name}</p>
          </div>
        </div>
        <div className="absolute bottom-0 h-full w-full border-2 border-white border-opacity-70 border-dashed rounded-md scale-95 group-hover:scale-105 transition-all" />
      </div>
    </>
  );
};

export default PastWinnerCard;
