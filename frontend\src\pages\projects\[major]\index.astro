---
import ProjectLayout from "@layouts/ProjectLayout.astro";
import { categories } from "@/pages/awards/category";

export const prerender = false;

const { major } = Astro.params;

const url = new URL(Astro.request.url);
const year = url.searchParams.get("year") ?? "All";

export function getStaticPaths() {
  const paths = [];

  for (const category of categories) {
    paths.push({
      params: { major: category.name },
    });
  }

  return paths;
}

const breadcrumbs = [
  { name: "Home", path: "/" },
  { name: "Projects", path: "/projects/" + major.split(" ").join("%20") + (year ? `?year=${year}` : "") }
];
// console.log({ major, year, breadcrumbs }); // eslint-disable-line no-console

---
<ProjectLayout title={major} major={major} year={year} {breadcrumbs}>
