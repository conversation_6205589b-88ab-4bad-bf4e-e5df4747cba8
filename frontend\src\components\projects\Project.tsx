import React, { useState, useEffect, useRef } from "react";
import type { Project } from "@/modules";
import { projectFactory } from "@/modules";
import { shuffleArray, sortArray, widget } from "@/utils";
import ProjectList from "./ProjectList";
import { useStore } from "@/utils/store";

type Props = {
  major?: string;
  year?: string;
  tech?: string;
};

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
// const isJudging = import.meta.env.PUBLIC_PIXEL_IS_JUDGING === "true";
const isPostSubmission =
  import.meta.env.PUBLIC_PIXEL_IS_POST_SUBMISSION === "true";
const isPostEvent = import.meta.env.PUBLIC_PIXEL_IS_POST_EVENT === "true";

const Project: React.FC<Props> = ({ major, year, tech }) => {
  const firstTimeRender = useRef(true);
  const { sort, setSort } = useStore();
  const [search, setSearch] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  const allProjects = useRef<Project[]>([]); // to contain all projects based on the route (e.g. for /projects/Software%20Engineering, it will contain all projects with major Software Engineering, etc.) when first rendered
  const [projects, setProjects] = useState<Project[]>([]); // to contain filtered, queried, and sorted projects

  // First render
  useEffect(() => {
    const fetchData = async () => {
      // Set up event listeners for search input
      document
        .getElementById("search-input")!
        .addEventListener("input", (e: any) => {
          setSearch(e.target.value);
        });

      setIsLoading(true);

      // if isPostEvent and year is current year, redirect to previous year
      if (year !== "All" && year !== undefined) {
        if ((isPostSubmission || isPostEvent) && year > currentYear) {
          year = parseInt(currentYear).toString();
          window.location.href = `/projects${major ? "?" + major : ""}?year=${year}`;
          return;
        }
        if (!(isPostSubmission || isPostEvent) && year >= currentYear) {
          year = (parseInt(currentYear) - 1).toString();
          window.location.href = `/projects${major ? "?" + major : ""}?year=${year}`;
          return;
        } else if (year > currentYear) {
          year = parseInt(currentYear).toString();
          window.location.href = `/projects${major ? "?" + major : ""}?year=${year}`;
          return;
        }
      }

      // console.log(year, major, tech);

      // Fetch all projects from Airtable
      const projects = await projectFactory().getProjects({
        // year: (parseInt(currentYear) - 1).toString(), by default to get the previous year's projects
        year:
          year == "All"
            ? undefined
            : year || (parseInt(currentYear) - 1).toString(),
        isVideoQC: true, // only fetch projects that have been through video QC
        isNotDraft: true, // only fetch projects that are not drafts
        filterCurrentYear: !(isPostSubmission || isPostEvent), // only fetch all projects after post event, otherwsise fetch all projects other than current year
      });
      const sortedArray = await shuffleArray(projects);
      // console.log("Projects fetched:", sortedArray);
      allProjects.current = sortedArray;

      // If there is a major query in the URL, filter the projects based on the major
      if (major || tech) {
        await filterProjects(major, tech);
      } else {
        setProjects(sortedArray);
      }

      setIsLoading(false);
      setTimeout(() => {
        document.getElementById("loading-skeleton").style.display = "none";
      }, 50);
      firstTimeRender.current = false;
    };

    fetchData();
  }, []);

  // Search value changes
  useEffect(() => {
    // Run this only after the first render
    if (!firstTimeRender.current) {
      if (search !== "") {
        searchProjects(search);
      } else {
        setProjects(allProjects.current);
      }
    }
  }, [search]);

  // Sort value changes
  useEffect(() => {
    // Run this only after the first render
    if (!firstTimeRender.current) {
      setIsLoading(true);
      (async () => {
        if (sort === "Shuffle" || sort === "Reshuffle") {
          setProjects(await shuffleArray(projects));
        } else if (sort === "From A-Z") {
          setProjects(
            await sortArray({
              array: projects,
              key: "project_short_name",
              type: "asc",
            })
          );
        } else if (sort === "From Z-A") {
          setProjects(
            await sortArray({
              array: projects,
              key: "project_short_name",
              type: "desc",
            })
          );
        } else if (sort === "From Awards") {
          const awardCategories = {
            gold: [],
            silver: [],
            bronze: [],
          };

          projects.forEach((project) => {
            const awards = project.awards || [];

            for (const award of awards) {
              const key = award.toLowerCase();
              if (key in awardCategories) {
                awardCategories[key].push(project);
                break;
              }
            }
          });

          // Set all projects in a single array, ordered by award level
          setProjects([
            ...awardCategories.gold,
            ...awardCategories.silver,
            ...awardCategories.bronze,
          ]);
        }
        setIsLoading(false);
      })();
    }
  }, [sort]);

  async function searchProjects(query?: string) {
    setIsLoading(true);

    // filter the project by tech stack or project name
    let searchResult = allProjects.current.filter(
      (project) =>
        project.project_name.toLowerCase().includes(query.toLowerCase()) ||
        (project.project_short_name &&
          project.project_short_name
            .toLowerCase()
            .includes(query.toLowerCase())) ||
        (project.tech_stack &&
          project.tech_stack.some((tech) =>
            tech.toLowerCase().includes(query.toLowerCase())
          ))
    );

    setProjects(searchResult);

    setIsLoading(false);
  }

  // Filter projects based on major or tech stack, e.g. when route is projects/[major] or projects/tech/[tech]
  async function filterProjects(major?: string, tech?: string) {
    setIsLoading(true);

    let filterResult;

    if (major) {
      filterResult = allProjects.current.filter(
        (project) => project.project_major === major
      );
    }

    if (tech) {
      filterResult = allProjects.current.filter((project) =>
        project.tech_stack.includes(tech)
      );
    }

    if (firstTimeRender.current) {
      allProjects.current = filterResult;
    }

    setProjects(filterResult);
    setIsLoading(false);
  }

  return (
    <>
      {!isLoading && (
        <div className="my-10 md:mt-14 md:mb-16">
          <ProjectList projects={projects} />
        </div>
      )}
    </>
  );
};

export default Project;
