/**
 * This script automates the creation of judge accounts for the judging system.
 * It reads email addresses and passwords from a Google Sheet,
 * creates Firebase Authentication accounts, initializes judge documents in Firestore,
 * and updates the spreadsheet with the generated UIDs.
 * 
 * @prerequisites
 * - Firebase service account JSON file located at '../service-account.json'
 * - Google Sheets API enabled in Google Cloud Console
 * - Proper permissions for the service account (Firebase Admin, Google Sheets API access)
 * 
 * @spreadsheet_format
 * The spreadsheet must have the following columns:
 * - Column A: UID (initially empty, will be populated by this script)
 * - Column B: Password (temporary password for the judge account)
 * - Column C: Email (email address for the judge account)
 * 
 * @usage
 * 1. Set up the Google Sheet with columns for UID (empty), password, and email
 * 2. Update the SPREADSHEET_ID constant with your Google Sheet ID
 * 3. Ensure the service account has necessary permissions
 * 4. Run: node create-judges.js
 * 
 * @important_notes
 * - THIS CREATES ACTUAL FIREBASE ACCOUNTS - use with caution
 * - Passwords are stored in plaintext in the Google Sheet - ensure proper security
 * - The script will skip rows that already have a UID (Column A)
 * - Error handling is implemented for individual accounts, so the script continues even if one account creation fails
 * - Make sure your service account has both Firebase Admin and Google Sheets API permissions
 *
 * @customization
 * - Change the spreadsheet ID on line 61 to point to your own spreadsheet
 * - Modify the range on line 62 and 102 if your sheet has a different name or structure
 * - Adjust the Firestore document structure on line 70 if needed for your application
 */

const admin = require('firebase-admin');
const { google } = require('googleapis');
const sheets = google.sheets('v4');
const path = require('path');

// Get absolute path to service account file
const serviceAccountPath = path.resolve(__dirname, 'service-account.json');

// Initialize Firebase Admin with service account
const serviceAccount = require(serviceAccountPath);
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

async function createJudgeAccounts() {
  try {
    // 1. Read from Google Sheet
    const auth = new google.auth.GoogleAuth({
      keyFile: serviceAccountPath,  // Use the same absolute path
      scopes: ['https://www.googleapis.com/auth/spreadsheets'],
    });
    
    const sheetsClient = await auth.getClient();
    // Use test sheet ID from README.md during development
    const spreadsheetId = '1DKZ5AMgljL0gRjTZy98e47yWPY5x5SM8DL5ji2hFNoU';
    const range = 'judges!A2:C'; // Start from A2 to skip header

    const response = await sheets.spreadsheets.values.get({
      auth: sheetsClient,
      spreadsheetId,
      range,
    });

    const rows = response.data.values;
    if (!rows) {
      console.log('No data found in sheet');
      return;
    }

    // 2. Create accounts and update sheet
    for (const row of rows) {
      const [existingUid, password, email] = row;
      
      // Skip if already has UID
      if (existingUid) {
        console.log(`Skipping ${email}, already has UID`);
        continue;
      }

      try {
        // Create auth account
        const userRecord = await admin.auth().createUser({
          email: email,
          password: password,
        });

        // Create judge document in Firestore (following existing structure)
        // await admin.firestore().collection('judges').doc(userRecord.uid).set({
        //   prelim_evaluated_teams: []  // Only create prelim field initially
        // });

        // Update UID back to sheet
        await sheets.spreadsheets.values.update({
          auth: sheetsClient,
          spreadsheetId,
          range: `judges!A${rows.indexOf(row) + 2}`, // +2 because sheet is 1-based and we skipped header
          valueInputOption: 'RAW',
          requestBody: {
            values: [[userRecord.uid]]
          }
        });

        console.log(`Created account for ${email} with UID: ${userRecord.uid}`);
      } catch (error) {
        console.error(`Error creating account for ${email}:`, error);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
createJudgeAccounts();