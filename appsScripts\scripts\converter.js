function convertJobsJSONtoSheet() {
  const converterSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName('converter');
  const jobListsSheet = PUBLIC_PIXEL_SPREADSHEET.getSheetByName('job_lists') || ss.insertSheet('job_lists');

  // Clear existing content in output sheet
  jobListsSheet.clearContents();

  // Set headers
  const headers = [
    'job_id',
    'company',
    'job_title',
    'job_type',
    'work_mode'
  ];
  jobListsSheet.getRange(1, 1, 1, headers.length).setValues([headers]);

  // Get JSON string from cell A1
  const jsonString = converterSheet.getRange('A2').getValue();

  let jobData;
  try {
    jobData = JSON.parse(jsonString).jobs;
  } catch (e) {
    Logger.log('Invalid JSON format in A1');
    return;
  }

  // Format and insert job data
  const output = jobData.map(job => [
    job.id,
    job.company.name,
    job.name,
    job.type,
    job.mode
  ]);

  // Write to job_lists sheet
  jobListsSheet.getRange(2, 1, output.length, headers.length).setValues(output);
}