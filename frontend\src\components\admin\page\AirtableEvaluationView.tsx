const AirtableEvaluationView = () => {
  return (
    <>
      <div className="flex gap-3 mb-5">
        <div className="bg-lime-300 w-4 rounded-md" />
        <p className="font-semibold">Airtable Evaluation View</p>
      </div>

      <p className="mb-2">Note: You may download the evaluation result in csv format by clicking the "Download CSV" button at the bottom of the table.</p>

      <iframe
        className="airtable-embed"
        src="https://airtable.com/embed/appVbcSklEhhwzV2d/shr1obihZDNvvqZcN?viewControls=on"
        width="100%"
        height="533"
        style={{
          background: "transparent",
          border: "1px solid #ccc",
        }}
      ></iframe>
    </>
  );
};

export default AirtableEvaluationView;
