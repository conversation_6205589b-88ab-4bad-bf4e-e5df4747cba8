export const googleDriveUpload = async (file: File, dataUrl: string, folderName: string) => {
  // Send a POST request to your Google Apps Script web app
  const response = await fetch(import.meta.env.PUBLIC_GDRIVE_URL, {
    redirect: 'follow',
    method: 'POST',
    body: JSON.stringify({
      file: dataUrl.split(',')[1],  // the file data is after the comma
      mimeType: file.type,
      filename: file.name,
      folderName: folderName
    }),
    headers: {
    "Content-Type": "text/plain;charset=utf-8", // avoid triggering CORS preflight
    }
    });

  const data = await response.json();
  
  if (data.result.status === 'error') {
    throw new Error(data.result.error);
  }

  return data.result.fileUrl;
};



