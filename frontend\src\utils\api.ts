type ResponseType = {
  data: any;
  offset: string;
};

type TableName =
  | "lecturer-committees"
  | "student-committees"
  | "sponsors"
  | "partners"
  | "participants"
  | "projects"
  | "judges"
  | "evaluations"
  | "supervisors";

class Api {
  private backend_url: string;
  private endpoint: string;

  // Initialise the class with the backend URL
  constructor(backend_url: string) {
    this.backend_url =
      import.meta.env.PUBLIC_PIXEL_IS_PRODUCTION === "true" ||
      import.meta.env.PROD // ensure that cloudflare build uses the worker url
        ? backend_url
        : "http://127.0.0.1:8787";

    // to switch between airtable PIXEL-2024 and TEST-PIXEL-2024
    if (import.meta.env.PUBLIC_PIXEL_IS_PRODUCTION === "true") {
      this.endpoint = this.backend_url + "/prod";
    } else {
      this.endpoint = this.backend_url + "/dev";
    }
  }

  async sanitizeData(response: any): Promise<ResponseType> {
    if (!response.ok) {
      const message = await response.text();
      const msg = JSON.parse(message).message;
      throw new Error(msg);
    }

    // const { data, message } = await response.json();
    const result = await response.json();
    
    const { data, message, offset } = result;

    return {
      data: response.ok ? data : undefined,
      offset: offset ? offset : "",
    };
  }

  // this is to replace the Airtable API URL Encoder done in https://codepen.io/airtable/full/MeXqOg
  constructQuery({
    fields = "",
    filterByFormula = "",
    maxRecords = 0,
    pageSize = 0,
    sort = "",
    view = "",
    offset = ""
  }: {
    fields?: string;
    filterByFormula?: string;
    maxRecords?: number;
    pageSize?: number;
    sort?: string;
    view?: string;
    offset?: string;
  }) {
    // To generate query string
    const params = {
      fields: fields
        ? fields
            .split(",")
            .map((field) => `fields[]=${encodeURIComponent(field.trim())}`)
        : [],
      filterByFormula: filterByFormula
        ? 
        `filterByFormula=${encodeURIComponent(filterByFormula)}`
        // (() => {
        //     // Replace all spaces within quoted strings in the formula with '+', to ensure proper URL encoding.
        //     let updatedFilterByFormula = filterByFormula.replace(
        //       /"([^"]*)"/g,
        //       function (match, p1) {
        //         return `"${p1.replace(/\s+/g, "+")}"`;
        //       }
        //     );

        //     return `filterByFormula=${encodeURIComponent(
        //       updatedFilterByFormula.replace(/\s+/g, "")
        //     )
        //       .replace(/{/g, "+%7B") // change ,{awards} to +%7Bawards%7D
        //       .replace(/}/g, "%7D")}`
        //       .replace(/%7B/g, "+%7B")
        //       .replace(/%2B/g, "+"); // change %2B to + because it was encoded
        //   })()
        : "",

      maxRecords: maxRecords
        ? `maxRecords=${encodeURIComponent(maxRecords)}`
        : "",
      pageSize: pageSize ? `pageSize=${encodeURIComponent(pageSize)}` : "",
      sort: sort
        ? sort.split(",").map((sortField, index) => {
            const [field, direction] = sortField.split(":");
            return `sort[${index}][field]=${encodeURIComponent(
              field ? field.trim() : field
            )}&sort[${index}][direction]=${encodeURIComponent(
              direction ? direction.trim() : direction
            )}`;
          })
        : [],
      view: view ? `view=${encodeURIComponent(view)}` : "",
      offset: offset ? `offset=${encodeURIComponent(offset)}` : "",
    };

    // console.log("api.ts", params.filterByFormula);

    return Object.entries(params)
      .filter(([, value]) => value.length > 0)
      .flatMap(([key, values]) => values)
      .join("&");
  }

  /**
   * @example
   *```ts
  //  use this code in Astro frontmatter
    const test = await api.testBackend();
    if (test !== "Connected to PIXEL Backend") {
      throw new Error("Backend not connected to " + Astro.url.origin);
    }
    ```
   * @returns {Promise<any>} The response data
   */
  async testBackend(): Promise<any> {
    try {
      const response = await fetch(this.backend_url + "/testbackend");

      return await response.text();
    } catch (error: any) {
      throw new Error("error in api.testBackend: " + error);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.first(
        "tablename",
        "recordid",
      )

    } catch (error) {
      widget.alertError("Error in first", error);
    }
   *
   * ```
   * @returns {Promise<ResponseType>} The response data
   */

  async first(table: TableName, id: string): Promise<ResponseType> {
    try {
      const response = await fetch(this.endpoint + "/" + table + "/" + id);
      return await this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.first: " + error);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.get(
        "tablename",
        {
          fields: "field1, field2, field3", // comma-separated list of fields
          filterByFormula: `AND(year="2023",
      OR(
        FIND("Best Project", {awards}),
        FIND("Best Presenter", {awards})
      )
    )`,
          maxRecords: 1000,
          pageSize: 100,
          sort: "field1:asc, field2:desc", // comma-separated list of sort fields
          view: "view1",
        }
      );

    } catch (error) {
      throw new Error("Error in functionX: "+ error);
    }
   * ```
   * @returns {Promise<ResponseType>} The response data
   */
  async get(
    table: TableName,
    params?: {
      fields?: string;
      filterByFormula?: string;
      maxRecords?: number;
      pageSize?: number;
      sort?: string;
      view?: string;
      offset?: string;
    }
  ): Promise<ResponseType> {
    const queryString = params ? `?${this.constructQuery(params)}` : "";

    try {
      const response = await fetch(`${this.endpoint}/${table}${queryString}`);
      
      return await this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.get: " + error.message);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.post(
        "tablename",
        {
          field1: "value1",
          field2: "value2",
        }
      );

    } catch (error) {
      throw new Error("Error in functionX: "+ error);
    }
   * ```
   * @returns {Promise<ResponseType>} The response data
   */
  async post(
    table: TableName,
    data: any,
    typecast?: boolean
  ): Promise<ResponseType> {
    try {
      const response = await fetch(this.endpoint + "/" + table, {
        method: "POST",
        body: JSON.stringify({
          fields: {
            ...data,
          },
          typecast: typecast, // typecast true allows new choice to be created for multiple choice column in airtable
        }),
      });

      return this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.post: " + error);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.put(
        "tablename",
        "recordid",
        {
          field1: "value1",
          field2: "value2",
        }
      );

    } catch (error) {
      throw new Error("Error in functionX: "+ error);
    }

      * ```
   * @returns {Promise<ResponseType>} The response data
   */
  async put(
    table: TableName,
    recordId: string,
    data: any
  ): Promise<ResponseType> {
    try {
      const response = await fetch(
        this.endpoint + "/" + table + "/" + recordId,
        {
          method: "PUT",
          body: JSON.stringify({
            fields: {
              ...data,
            },
          }),
        }
      );

      return this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.put: " + error);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.patch(
        "tablename",
        "recordid",
        {
          field1: "value1",
          field2: "value2",
        },
        true // typecast
      );

      } catch (error) {
      throw new Error("Error in functionX: "+ error);
    }
  * ```
   * @returns {Promise<ResponseType>} The response data
   */
  async patch(
    table: TableName,
    recordId: string,
    data: any,
    typecast?: boolean
  ): Promise<ResponseType> {
    try {
      const response = await fetch(
        this.endpoint + "/" + table + (recordId ? "/" + recordId : ""),
        {
          method: "PATCH",
          body: JSON.stringify(
            !Array.isArray(data)
              ? {
                  fields: {
                    ...data,
                  },
                  typecast: typecast,
                }
              : {
                  records: data,
                  typecast: typecast,
                }
          ),
        }
      );

      return this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.patch: " + error);
    }
  }

  /**
   * @example
   * ```ts
    try {
      const { data } = await api.delete(
        "tablename",
        "recordid"
      );

    } catch (error) {
      throw new Error("Error in functionX: "+ error);
    }
   * ```
   * @returns {Promise<ResponseType>} The response data
   */
  async delete(table: TableName, recordId: string): Promise<ResponseType> {
    try {
      const response = await fetch(
        this.endpoint + "/" + table + "/" + recordId,
        {
          method: "DELETE",
        }
      );

      return this.sanitizeData(response);
    } catch (error: any) {
      throw new Error("error in api.delete: " + error);
    }
  }
}

export { Api };
