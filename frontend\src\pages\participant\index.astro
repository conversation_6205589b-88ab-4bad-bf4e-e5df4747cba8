---
import Dashboard from "@components/participant/Dashboard/Dashboard";
import DashboardLayout from "@layouts/DashboardLayout.astro";

// Astro.redirect("/404");
---

<DashboardLayout title="Dashboard">
  <!-- <Dashboard route="/" client:load /> -->
  <Dashboard client:only="react" />
</DashboardLayout>

<style is:inline>
  .clip-path-polygon {
    clip-path: polygon(0 0, 82% 0, 81% 100%, 0% 100%);
  }

  .shepherd-modal-overlay-container {
    background: rgba(0, 0, 0, 0.5) !important;
    opacity: 0.3 !important;
  }

  .shepherd-header {
    background: #fff !important;
  }

  .shepherd-title {
    font-weight: bold;
  }
  .shepherd-arrow {
    top: -4px;
  }

  [data-popper-arrow]::before,
  [data-popper-arrow]::after {
    width: 15px !important;
    height: 15px !important;
  }


</style>
