import { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import MainSection from "./MainSection";
import type { Team, ParticipantSession } from "@/modules";
import { actionSessionStorage, widget, auth } from "@/utils";
import { ParticipantContext } from "./Context";
import Countdown from "./Countdown";
import { onAuthStateChanged } from "firebase/auth";

const isRegistration = import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";

// const Dashboard = ({ route }: { route: string }) => {
const Dashboard = () => {
  const [user, setUser] = useState({} as ParticipantSession);

  const [TEAM_MIN_LIMIT, setMinLimit] = useState(0);
  const [TEAM_MAX_LIMIT, setMaxLimit] = useState(0);

  // for ProjectMode
  const [isTeam, setIsTeam] = useState(false);

  // for TeamSection
  const [team, setTeam] = useState({} as Team);

  // for ProjectSection
  const [project, setProject] = useState({} as any);

  const [dashboardLoad, setDashboardLoad] = useState(true);
  const [teamLoad, setTeamLoad] = useState(true);

  // submission deadline
  const targetDate = new Date(import.meta.env.PUBLIC_SUBMISSION_DEADLINE);

  function handleTeamLoad() {
    setTeamLoad(false);
  }

  if (import.meta.env.PUBLIC_PIXEL_IS_OFFICIAL_LAUNCH === "false") {
    widget.alertError(
      "PIXEL is not officially launched yet",
      "Please wait for the official launch.",
      undefined,
      3000
    );
    setTimeout(() => {
      window.location.href = "/";
    }, 3000);
    return;
  }

  useEffect(() => {
    let unsub;

    (function () {
      unsub = onAuthStateChanged(auth, (user) => {
        if (unsub) {
          unsub();
        }

        if (!user) {
          widget
            .alertError(
              "You are not logged in",
              "Please login to continue.",
              undefined,
              3000
            )
            .then(() => {
              window.location.href = "/sign-in";
            });
        } else if (user) {
          // case where participant has done signed up (there is user object from firebase at this point), but not registered yet, and attempts to access dashboard
          const participantSessionData = actionSessionStorage().get("userData");
          if (!participantSessionData) {
            widget
              .alertError(
                "You are not registered",
                "Please make sure you are registered before logging in.",
                undefined,
                3000
              )
              .then(() => {
                window.location.href = "/sign-in";
              });
          }
        }
      });
    })();

    let storedUser: ParticipantSession = actionSessionStorage().get("userData");

    if (storedUser) {
      setUser(storedUser);
      setIsTeam(storedUser.is_team);

      let majorInitials = storedUser.student_major
        .split(" ")
        .map((word) => word[0].toUpperCase())
        .join("");

      setMinLimit(
        Number(import.meta.env[`PUBLIC_${majorInitials}_TEAM_MIN_LIMIT`])
      );
      setMaxLimit(
        Number(import.meta.env[`PUBLIC_${majorInitials}_TEAM_MAX_LIMIT`])
      );
    }
  }, []);

  useEffect(() => {
    if (user) {
      setDashboardLoad(false);
    } else {
      (async function () {
        await widget.alertError(
          "User not found",
          "Something went wrong while fetching user data. Returning to login page ..."
        );
        setTimeout(() => {
          window.location.href = "/sign-in";
        }, 3000);
      })();
    }
  }, [user]);

  // if all data is still fetching then show loading
  if (dashboardLoad === true) {
    return (
      <div id="container">
        <span id="first"></span>
        <span id="second"></span>
        <span id="third"></span>
      </div>
    );
  }

  return (
    <div className="w-full h-full max-lg:min-h-screen flex flex-col bg-white md:rounded-md overflow-hidden">
      {teamLoad && <p className="hidden">Loading...</p>}
      {/* Component: Topbar */}
      {isRegistration ? (
        <div
          className="w-full relative flex justify-end items-center my-0 pr-5 py-2 gap-5"
          id="countdowntopbar"
        >
          <div className="w-full h-full absolute bg-grey-1 top-0 left-0">
            <div className="bg-primary-3 h-full overflow-hidden relative w-full clip-path-polygon"></div>

            <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1 bg-black">
              <img
                className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
                src="/assets/images/logo/pixie-full.webp"
                alt="Avatar"
              />
              <div
                role="tooltip"
                className="absolute top-4 ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip"
              >
                <p className="text-[8px] lg:text-base font-semibold whitespace-nowrap">
                  I was waiting for you!
                </p>
              </div>
            </div>
          </div>

          <div className="flex flex-col mb-auto z-10">
            <p className="w-full lg:mt-2 uppercase text-md text-black font-bold tracking-wider self-start hidden md:block whitespace-nowrap">
              Time left for submission{" "}
            </p>
            <hr></hr>
          </div>

          <Countdown targetDate={targetDate} />
        </div>
      ) : (
        <div
          className="w-full relative flex justify-end items-center my-0 pr-5 py-2 gap-5"
          id="countdowntopbar"
        >
          <div className="w-full h-full absolute bg-grey-1 top-0 left-0">
            <div
              className="bg-primary-3 h-full overflow-hidden relative w-full"
              id="wavecountdown"
            ></div>
          </div>
          <div className="w-[75%] md:w-[60%] lg:ml-8 mt-1 bg-black">
            <img
              className="absolute top-4 md:top-6 w-[45px] h-[60px] lg:w-[70px] lg:h-[90px] drop-shadow-lg"
              src="/assets/images/logo/pixie-full.webp"
              alt="Avatar"
            />
            <div
              role="tooltip"
              className="relative ml-12 mb-5 lg:ml-20 lg:mb-10 z-10 inline-block px-2 lg:py-1 text-sm font-medium text-gray-900 bg-white border border-black rounded-tl-lg rounded-tr-lg rounded-br-lg shadow-sm tooltip"
            >
              <p className="text-[8px] lg:text-base font-semibold whitespace-nowrap">
                Thank you for your participation!
              </p>
            </div>
          </div>
        </div>
      )}
      <div className="flex flex-col lg:flex-row h-[640px]">
        <ParticipantContext.Provider
          value={{
            user,
            setUser,
            TEAM_MIN_LIMIT,
            TEAM_MAX_LIMIT,
            handleTeamLoad,
            isTeam,
            setIsTeam,
            team,
            setTeam,
            project,
            setProject,
          }}
        >
          {/* {window.innerWidth >= 1024 ? <Sidebar /> : <DropDown/>} */}
          <Sidebar />
          {/* {route === "jobs-applied" ? (
        <JobsAppliedSection />
      ) : (
        <MainSection />
      )} */}
          <MainSection />
        </ParticipantContext.Provider>
      </div>
    </div>
  );
};

export default Dashboard;
