---
const MAJOR_FILTER_TYPE = [
  {
    id: "all",
    name: "All",
  },
  {
    id: "se",
    name: "Software Engineering",
  },
  {
    id: "ic",
    name: "Intelligent Computing",
  },
  {
    id: "ci",
    name: "Computing Infrastructure",
  },
];

var { major, year } = Astro.props;

if (major === "") major = "All";
if (year === "") year = "All";

---

<div class="flex flex-row items-center gap-2 md:gap-5 max-md:justify-center">
  {
    MAJOR_FILTER_TYPE.map((majorFilterType, index) => (
      <>
        {index > 0 && (
          <span class="font-bold text-sm pointer-events-none">•</span>
        )}
        <a
          href={`/projects/${majorFilterType.name === "All" ? "" : majorFilterType.name}${year && year !== "All" ? `?year=${year}` : ""}`}
          class={`*:text-sm *:font-semibold ${
            major === majorFilterType.name && "bg-primary-6 *:text-white"
          } hover:bg-primary-6 *:hover:text-white px-2 py-[0.1rem] rounded-md transition`}
        >
          <span class="md:hidden">
            {majorFilterType.id == "all"
              ? "All"
              : majorFilterType.id.toUpperCase()}
          </span>
          <span class="hidden md:inline">{majorFilterType.name}</span>
        </a>
      </>
    ))
  }
</div>
