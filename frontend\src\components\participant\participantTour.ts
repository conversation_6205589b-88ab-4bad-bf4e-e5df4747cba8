const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;
const isRegistration =
  import.meta.env.PUBLIC_PIXEL_IS_REGISTRATION === "true";

function nextStep(
  tour: any,
  text: string,
  element: string,
  on: string,
  buttontxt: string
) {
  tour.addStep({
    text: text,
    attachTo: {
      element: element,
      on: on,
    },
    buttons: [
      {
        action(this): any {
          return this.back();
        },
        classes: "shepherd-button-secondary",
        text: "Back",
      },
      {
        action(this): any {
          return this.next();
        },
        text: buttontxt,
      },
    ],
  });
}

export default function startTour(project_id: string, is_team: boolean, TEAM_MAX_LIMIT: number) {
  // @ts-ignore
  const tour = new Shepherd.Tour({
    useModalOverlay: true,
    defaultStepOptions: {
      cancelIcon: {
        enabled: true,
      },
      classes: "shadow-lg shepherd-theme-arrows",
      scrollTo: { behavior: "smooth", block: "center" },
    },
    header: {
      classes: "bg-blue-800",
    },
  });

  tour.addStep({
    title: `Welcome To PIXEL ${currentYear} Dashboard!`,
    text: `This is the dashboard for PIXEL ${currentYear}. You can manage your profile${TEAM_MAX_LIMIT != 1 ? ", team," : ""} and project here. Click "Next" to continue.`,
    attachTo: {
      element: "body",
    },
    buttons: [
      {
        action(): any {
          return this.next();
        },
        text: "Next",
      },
    ],
  });

  nextStep(
    tour,
    "In desktop, hovering this sidebar will show your profile details.",
    "#sidebar",
    "left",
    "Next"
  );
  nextStep(
    tour,
    "To edit your profile, click this button.",
    "#editprofilebtn",
    "bottom",
    "Next"
  );
  nextStep(
    tour,
    "To submit your project, click this button.",
    "#submitprojectbtn",
    "bottom",
    "Next"
  );
  // nextStep(
  //   tour,
  //   "To view your job applications, click this button.",
  //   "#jobapplicationbtn",
  //   "bottom",
  //   "Next"
  // );
  nextStep(
    tour,
    "You can log out from your current session by clicking this button.",
    "#logoutbtn",
    "right",
    "Next"
  );

  nextStep(
    tour,
    "This is the countdown indicating the time left before submission closed.",
    "#countdowntopbar",
    "bottom",
    "Next"
  );

  if (
    // participant.student_major === "Software Engineering" &&
    isRegistration && is_team
  ) {
    nextStep(
      tour,
      "Your team members will be listed here.",
      "#teammembers",
      "bottom",
      "Next"
    );
    nextStep(
      tour,
      "In order to join or invite team members to your team, click here. If you not yet added anyone in the team, you will need to generate a team and share the team ID to let them join the team.",
      "#joininvitebtn",
      "bottom",
      "Next"
    );
    if (is_team) {
      nextStep(
        tour,
        "To leave the team, you can click this button.",
        "#leaveteambtn",
        "bottom",
        "Next"
      );
      if (document.getElementById("addProjectButton"))
        nextStep(
          tour,
          "Click this button to create new project",
          "#addProjectButton",
          "right",
          "Next"
        );
    } else {
      nextStep(
        tour,
        "You are able to leave the team after joining a team by clicking a red button at the right side of your member profile.",
        "body",
        "",
        "Next"
      );
    }
  }

  // nextStep(
  //   tour,
  //   "Click view project to review submitted project",
  //   "#viewProjectButton",
  //   "bottom",
  //   "Next"
  // );
  
  tour.addStep({
    text: "Click view project to review submitted project <img src=/assets/images/tour/view-project.webp alt=placeholder className=w-full h-full relative object-cover rounded/>",
    attachTo: {
      element: "body",
    },
    buttons: [
      {
        action(): any {
          return this.back();
        },
        classes: "shepherd-button-secondary",
        text: "Back",
      },
      {
        action(): any {
          return this.next();
        },
        text: "Next",
      },
    ],
  });

  // close because submission deadline
  tour.addStep({
    text: "Click update project to make changes to submitted project. <img src=/assets/images/tour/update-project.webp alt=placeholder className=w-full h-full relative object-cover rounded/>",
    attachTo: {
      element: "body",
    },
    buttons: [
      {
        action(): any {
          return this.back();
        },
        classes: "shepherd-button-secondary",
        text: "Back",
      },
      {
        action(): any {
          return this.next();
        },
        text: "Next",
      },
    ],
  });

  nextStep(
    tour,
    "Need help navigating the dashboard? Click this button anytime for a quick walkthrough.",
    "#userguidebtn",
    "bottom",
    "Next"
  );

  nextStep(
    tour,
    "End of tutorial. Good luck!",
    "body",
    "",
    "Thanks!"
  );

  tour.start();
} 