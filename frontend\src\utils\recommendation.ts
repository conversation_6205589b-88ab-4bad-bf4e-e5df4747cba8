import type { Project } from "@/modules";
import { projectFactory } from "@/modules";

// Function to fetch projects from Airtable
let cachedProjects: Project[] | null = null;

async function fetchProjects(): Promise<Project[]> {
  if (!cachedProjects) {
    try {
      cachedProjects = await projectFactory().getProjects({ isNotDraft: true });
    } catch (e) {
      console.error("Failed to fetch projects:", e);
      throw e;
    }
  }
  return cachedProjects;
}

const stopWords = [
  "the",
  "a",
  "an",
  "of",
  "is",
  "in",
  "to",
  "for",
  "on",
  "by",
  "with",
  "at",
  "like",
  "be",
  "have",
  "it",
  "that",
  "they",
  "we",
  "are",
  "this",
  "his",
  "her",
  "which",
  "who",
  "all",
  "do",
  "said",
  "if",
  "or",
  "as",
  "from",
  "has",
  "more",
  "would",
];

// Function to tokenize text
function tokenize(text: string) {
  return (text !== undefined && text.toLowerCase().match(/\b\w+\b/g)) || [];
}

// Function to remove stop words from text
function removeStopWords(text: string) {
  const words = tokenize(text);
  return words.filter((word) => !stopWords.includes(word));
}

// Function to calculate TF of a word in a document
function calculateTF(word: string, document: string[]) {
  const wordCount = document.filter((w) => w === word).length;
  return wordCount / document.length;
}

// Function to calculate IDF of a word in a set of documents
function calculateIDF(word: string, documents: string[][]) {
  const numDocsContainingWord = documents.filter((doc) =>
    doc.includes(word)
  ).length;
  return Math.log(documents.length / (1 + numDocsContainingWord));
}

// Function to calculate TF-IDF of a word in a document
function calculateTfIdf(
  word: string,
  document: string[],
  documents: string[][]
) {
  const tf = calculateTF(word, document);
  const idf = calculateIDF(word, documents);
  return tf * idf;
}

// Function to calculate cosine similarity
function cosineSimilarity(vecA: number[], vecB: number[]) {
  const dotProduct = vecA.reduce((sum, a, idx) => sum + a * vecB[idx], 0);
  const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
  return dotProduct / (magnitudeA * magnitudeB);
}

// Function to calculate Jaccard similarity for sets
function jaccardSimilarity(setA: Set<string>, setB: Set<string>) {
  const intersection = new Set([...setA].filter((x) => setB.has(x)));
  const union = new Set([...setA, ...setB]);
  return intersection.size / union.size;
}

function calculateSimilarity(
  projectA: any,
  projectB: any,
  allDocs: string[][]
) {
  const nameA = removeStopWords(projectA.project_name);
  const nameB = removeStopWords(projectB.project_name);
  const descA = removeStopWords(projectA.project_desc);
  const descB = removeStopWords(projectB.project_desc);

  const uniqueWords = Array.from(
    new Set([...nameA, ...nameB, ...descA, ...descB])
  );

  const tfidfNameA = uniqueWords.map((word) =>
    calculateTfIdf(word, nameA, allDocs)
  );
  const tfidfNameB = uniqueWords.map((word) =>
    calculateTfIdf(word, nameB, allDocs)
  );
  const tfidfDescA = uniqueWords.map((word) =>
    calculateTfIdf(word, descA, allDocs)
  );
  const tfidfDescB = uniqueWords.map((word) =>
    calculateTfIdf(word, descB, allDocs)
  );

  const nameSimilarity = cosineSimilarity(tfidfNameA, tfidfNameB);
  const descSimilarity = cosineSimilarity(tfidfDescA, tfidfDescB);

  const techStackA: any = new Set(projectA.tech_stack);
  const techStackB: any = new Set(projectB.tech_stack);
  const techStackSimilarity = jaccardSimilarity(techStackA, techStackB);

  const sdgA: any = new Set(projectA.sdg);
  const sdgB: any = new Set(projectB.sdg);
  const sdgSimilarity = jaccardSimilarity(sdgA, sdgB);

  const majorA: any = new Set(projectA.project_major);
  const majorB: any = new Set(projectB.project_major);
  const majorSimilarity = jaccardSimilarity(majorA, majorB);

  const similarity =
    0.1 * nameSimilarity +
    0.5 * descSimilarity +
    0.3 * techStackSimilarity +
    0.05 * sdgSimilarity +
    0.05 * majorSimilarity;

  return similarity;
}

async function findSimilarProjects(
  queryProject: any,
  numRecommendations: number
): Promise<Project[]> {
  let projects: Project[] = [];
  try {
    projects = await fetchProjects();
  } catch (e) {
    return [];
  }

  const allDocs = projects.map((project) =>
    removeStopWords([project.project_name, project.project_desc].join(" "))
  );

  const similarProjects: any[] = [];

  for (const project of projects) {
    if (!project) continue;
    const similarity = calculateSimilarity(queryProject, project, allDocs);
    similarProjects.push({ ...project, similarity });
  }

  similarProjects.sort((a, b) => b.similarity - a.similarity);

  return similarProjects.slice(0, numRecommendations);
}

// Export function to recommend similar projects
export async function Recommender(oldProject: any): Promise<any> {
  const numRecommendations = 7;
  const queryProject = { ...oldProject };

  if (queryProject.tech_stack) {
    queryProject.tech_stack.join();
  } else queryProject.tech_stack = [];

  const recommendations = await findSimilarProjects(
    queryProject,
    numRecommendations
  );

  if (recommendations.length > 0) {
    recommendations.shift(); // Remove the original project from recommendations
  }

  return recommendations;
}

// import { api } from "@/utils";

// // Function to calculate term frequency of a word in a document
// function calculateTermFrequency(word: string, document: string) {
// 	// //  console.log("calculateTermFrequency");
// 	const words = document.toLowerCase().split(" ");
// 	const count = words.reduce((total, w) => (w === word ? total + 1 : total), 0);
// 	return count / words.length;
// }

// // Function to calculate Jaccard similarity coefficient between two sets
// function calculateJaccardSimilarity(setA: any, setB: any) {
// 	// //  console.log("calculate jaccard similarity");
// 	const intersection = new Set([...setA].filter((x) => setB.has(x)));
// 	const union = new Set([...setA, ...setB]);
// 	return intersection.size / union.size;
// }

// // Function to calculate similarity between project names using term frequency
// function calculateSimilarity(nameA: string, nameB: string) {
// 	// //  console.log("calculate similarity");
// 	const wordsA = nameA.toLowerCase().split(" ");
// 	const wordsB = nameB.toLowerCase().split(" ");
// 	const commonWords = new Set([...wordsA].filter((w) => wordsB.includes(w)));
// 	const similarity = [...commonWords].reduce(
// 		(total, word) =>
// 			total +
// 			calculateTermFrequency(word, nameA) +
// 			calculateTermFrequency(word, nameB),
// 		0
// 	);
// 	return similarity;
// }

// // Function to find similar projects based on hashtags and names
// async function findSimilarProjects(
// 	queryProject: any,
// 	numRecommendations: number
// ) {
// 	let projects = [];
// 	try {
// 		// fetch projects data from worker api
// 		const response = await fetch(api.getBaseUrl() + "/projects", {
// 			method: "GET",
// 			headers: {
// 			},
// 		});
// 		const data = await response.json();
// 		projects = data.records;
// 		// console.log("project success fetched");
// 	} catch (e) {
// 		// console.log(e);
// 		return null;
// 	}

// 	//if query project tags is empty then return random projects
// 	if (!queryProject.tags || queryProject.tags.length === 0) {
// 		const randomProjects: any = [];
// 		for (let i = 0; i < numRecommendations; i++) {
// 			const newProject = projects[Math.floor(Math.random() * projects.length)];
// 			// check if the project is already in the array
// 			if (randomProjects.includes(newProject)) i--;
// 			else randomProjects.push(newProject);
// 		}
// 		return randomProjects;
// 	}

// 	const similarProjects = [];

// 	// split all project tags into an array of tags

// 	for (const project of projects) {
// 		if (!project.fields.tags || project.fields.tags === "") continue;
// 		project.fields.tags = project.fields.tags.split(",");
// 	}

// 	// Iterate through each project
// 	for (const project of projects) {
// 		// Calculate similarity based on hashtags
// 		const hashtagSimilarity = calculateJaccardSimilarity(
// 			new Set(queryProject.tags),
// 			new Set(project.fields.tags)
// 		);

// 		// Calculate similarity based on project names
// 		const nameSimilarity = calculateSimilarity(
// 			queryProject.name,
// 			project.fields.name
// 		);

// 		// calculate similarity based on project major
// 		const majorSimilarity = calculateSimilarity(
// 			queryProject.major,
// 			project.fields.major
// 		);

// 		// Calculate overall similarity as a weighted average
// 		const similarity = 0.7 * hashtagSimilarity + 0.3 * nameSimilarity;

// 		similarProjects.push({ ...project, similarity });
// 	}

// 	// Find the maximum similarity value
// 	const maxSimilarity = Math.max(
// 		...similarProjects.map((project) => project.similarity)
// 	);

// 	// Normalize the similarity values between 0 and 1
// 	for (const project of similarProjects) {
// 		project.similarity /= maxSimilarity;
// 	}

// 	// Sort projects based on similarity in descending order
// 	similarProjects.sort((a, b) => b.similarity - a.similarity);

// 	// Return the top N similar projects
// 	return similarProjects.slice(0, numRecommendations);
// }

// export async function Recommender(oldProject: any): Promise<any> {
// 	const numRecommendations = 7;

// 	const queryProject = { ...oldProject };

// 	// split query Project tags to array of strings using tokenizer ,
// 	// and remove empty strings
// 	if (queryProject.tags) {
// 		if (typeof queryProject.tags === "string")
// 			queryProject.tags = queryProject.tags
// 				.split(",")
// 				.filter((tag: string) => tag !== "");
// 		else queryProject.tags.join();
// 	} else queryProject.tags = [];

// 	const recommendations = await findSimilarProjects(
// 		queryProject,
// 		numRecommendations
// 	);

// 	//remove the first element which is the query project itself
// 	if (recommendations) {
// 		recommendations.shift();
// 		const formattedRecommendations = recommendations.map((response: any) => ({
// 			// id: response.id,
// 			// ...response.fields,
// 			// team: {
// 			//   members_id: response.fields.members_firebaseId.split(","),
// 			//   members_airtableId: response.fields.members_id,
// 			//   members_urls: response.fields.team_images.map((image: any) => image.url),
// 			// },
// 			id: response.id,
// 			name: response.fields.project_name,
// 			desc: response.fields.project_desc,
// 			major: response.fields.project_major,
// 			team: {
// 				members_id: response.fields.members_id,
// 				members_firebase_uid: response.fields.members_firebase_uid,
// 				members_name: response.fields.members_name,
// 				members_email: response.fields.members_email,
// 				members_photo: Array.isArray(response.fields.members_photo)
// 					? response.fields.members_photo.map((photo: any) => photo.url)
// 					: [],
// 			},
// 			supervisor: {
// 				name:
// 					response.fields.supervisor_name &&
// 					response.fields.supervisor_name.length > 0
// 						? response.fields.supervisor_name[0]
// 						: "",
// 				profile_link:
// 					response.fields.supervisor_profile_link &&
// 					response.fields.supervisor_profile_link.length > 0
// 						? response.fields.supervisor_profile_link[0]
// 						: "",
// 			},
// 			...response.fields,
// 		}));

// 		// Use the formattedRecommendations array as needed
// 		// console.log(formattedRecommendations);
// 		return formattedRecommendations;
// 	}
// 	return recommendations;
// }
