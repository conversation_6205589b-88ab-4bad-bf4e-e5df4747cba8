import type { APIRoute } from "astro";
import CryptoJS from "crypto-js";

export const GET: APIRoute = ({ request, cookies }) => {
  const csrfToken = CryptoJS.lib.WordArray.random(36).toString(
    CryptoJS.enc.Base64
  );

  cookies.set("X-CSRF-Token", csrfToken, {
    path: "/",
    httpOnly: true,
    sameSite: "strict",
    secure: import.meta.env.PROD,
    maxAge: 120, // 2 mins
  });

  return new Response(csrfToken, { status: 200 });
};
