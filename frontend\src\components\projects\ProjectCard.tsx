import React, { useContext, useEffect, useRef, useState } from "react";
import type { Project } from "@/modules";
import TeamAvatar from "./Avatar";
import ProjectSubmissionView from "@components/participant/Submission/ProjectSubmissionView";
import ProjectModal from "@components/projects/ProjectModal";
import {
  ProjectCardContext,
  ProjectContext,
} from "@components/projects/Context";
import { encryptProject } from "@utils/utils";

type Props = {
  project: any;
};

const MajorStyle: any = {
  "Software Engineering": "text-green-se-2",
  "Intelligent Computing": "text-red-ic-2",
  "Computing Infrastructure": "text-blue-ci-2",
};

const MajorInitials: any = {
  "Software Engineering": "SE",
  "Intelligent Computing": "IC",
  "Computing Infrastructure": "CI",
};

const ProjectCard: React.FC<Props> = ({ project }: { project: Project }) => {
  const { prevProjectId, setPrevProjectId } = useContext(ProjectContext);
  const [isOpen, setIsOpen] = useState(false);

  const showProject = (id: string) => {
    // set current project id as the previous project id so that we can close it later when we open another project
    setPrevProjectId(id);
    // if there is already a project modal opened, close it, and reset the isOpen property for the previous project
    if (prevProjectId) {
      const prevProjectModal = document.getElementById(
        `modal-${encryptProject(prevProjectId)}`
      );
      prevProjectModal.style.display = "none";
    }

    setIsOpen(true);

    document.body.style.overflow = "hidden";
  };

  const closeProject = (id: string) => {
    setPrevProjectId("");
    document.body.style.overflow = "auto";
    setIsOpen(false);
  };

  const handleFilterChange = (major: string) => {
    const majorId = major.replace(/\s/g, "");
    const inputElement = document.getElementById(majorId);
    if (inputElement) {
      inputElement.click();
    }
  };

  function getAwardsColor(awards: string[]) {
    for (const award of awards) {
      const lowerAward = award.toLowerCase();
      if (lowerAward === "gold") {
        return "text-[#fcbf28]"; // Rich gold color
      }
      if (lowerAward === "silver") {
        return "text-[#C0C0C0]"; // Classic silver
      }
      if (lowerAward === "bronze") {
        return "text-[#8B4513]"; // True bronze tone
      }
    }
  }

  return (
    <ProjectCardContext.Provider value={{ closeProject, showProject }}>
      {/* Project Cards */}
      {isOpen && (
        <ProjectModal id={project.id} key={project.id}>
          <ProjectSubmissionView project={project} key={project.id} />
        </ProjectModal>
      )}
      <div
        id="project-card"
        className="cursor-pointer w-full border border-black flex flex-col transition-transform transform hover:scale-[1.02]"
        onClick={() => showProject(project.id)}
      >
        {/* Thumbnail */}
        <div className="w-full aspect-video overflow-hidden cursor-pointer bg-gray-100">
          {/* {imageLoading && (
            <img
              src={project.thumbnail}
              alt={project.project_name}
              className="w-full h-full object-cover"
              width="100%"
              height="100%"
            />
          )} */}
          <img
            src={project.thumbnail}
            alt={project.project_name}
            // onLoad={() => setImageLoading(false)}
            // className={`w-full h-full object-cover ${imageLoading && "hidden"}`}
            className={`w-full h-full object-cover`}
            width="100%"
            height="100%"
          />
        </div>
        {/* Details */}
        <div className="flex flex-col justify-between grow">
          {/* Project Title */}
          <div className="px-4 pt-2 pb-4 cursor-pointer grow">
            <p className="text-mobile-16 font-medium text-left">
              {project.project_short_name ?? project.project_name}
            </p>
          </div>

          {/* Profile Avatar & Major Tag */}
          <div className="px-4">
            <div className="w-full border-t border-grey-4"></div>

            <div className="flex flex-row pt-4 pb-2 justify-between items-center">
              <div className="flex gap-2">
                <TeamAvatar team={project.team} size={"size-8"} />
              </div>

              <div className="flex">
                {project.awards && (
                  <i
                    className={`fa-solid fa-trophy ${getAwardsColor(
                      project.awards
                    )}`}
                  />
                )}

                <p
                  className={`${
                    MajorStyle[project.project_major]
                  } px-2 text-mobile-14 font-bold italic hover:cursor-pointer`}
                  onClick={() => handleFilterChange(project.project_major)}
                >
                  {MajorInitials[project.project_major]}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ProjectCardContext.Provider>
  );
};

export default ProjectCard;
