import {
  useRef,
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from "react";
import { Modal } from "flowbite";

import { Participant, participantFactory } from "@/modules";
import ReadMore from "@components/general/ReadMore";
import { createPortal } from "react-dom";
import Loading from "./Loading";
import { cache, actionSessionStorage, widget } from "@/utils";

type Props = {
  // imageURL: string;
  ref?: any;
  id?: string;
  onClose?: () => void;
};

const ProfileModal: React.FC<Partial<Participant> & Partial<Props>> =
  forwardRef(({ id, onClose, ...props }, ref: any) => {
    const [user, setUser] = useState<typeof props>(null);
    const [isLoading, setIsLoading] = useState(false);
    const mainModal = document.getElementById("main-modal");

    // Modal Logic for profile preview
    //
    const modalRef = useRef<any>(null);
    const [modal, setModal] = useState<any>();

    useEffect(() => {
      setModal(new Modal(modalRef.current));
    }, []);

    useImperativeHandle(ref, () => ({
      showModal,
      hideModal,
    }));

    function showModal(id: string) {
      setUser(props);
      modal.show();
    }

    function hideModal() {
      modal.hide();
    }
    //
    //

    // IMPROVEMENT NEEDED: Add functionality to allow closing by clicking outside the modal
    useEffect(() => {
      const handleKeyPress = (event) => {
        if (event.key === "Escape") {
          onClose ? onClose() : modal.hide();
        }
      };
  
      window.addEventListener("keydown", handleKeyPress);
  
      return () => {
        window.removeEventListener("keydown", handleKeyPress);
      };
    }, []);

    async function getParticipant(id: string) {
      let user: any;

      user = await cache.get(id);

      if (!user) {
        try {
          const data = (await participantFactory().getParticipantAirtable(
            id
          )) as Participant;
          user = data;
        } catch (error) {
          widget.alertError("Error", error);
        }
      }

      setUser(user);
      cache.set(id, user);
      setIsLoading(false);
    }

    useEffect(() => {
      if (id) {
        setIsLoading(true);
        getParticipant(id);
      }
    }, [id]);

    return (
      <>
        {createPortal(
          <>
            <div
              ref={modalRef}
              tabIndex={-1}
              aria-hidden="true"
              className={`fixed inset-0 w-full z-50 backdrop-blur before:absolute before:inset-0 before:bg-black before:bg-opacity-20 before:z-[-1] flex ${
                ref && "hidden"
              }`}
              id="profile-modal"
            >
              <div className="relative w-full flex flex-col m-auto max-w-3xl">
                <div className="relative bg-white rounded-lg shadow-lg dark:bg-gray-700">
                  <div className="px-3 py-6 md:px-6 overflow-hidden w-full flex flex-col">
                    <button
                      type="button"
                      className="border border-grey-3 hover:bg-grey-5 text-grey-3 transition duration-200 hover:text-white rounded-md cursor-pointer text-sm p-1 w-fit self-end"
                      title="Close Profile"
                      onClick={onClose ? onClose : () => modal.hide()}
                    >
                      <i className="fa-solid fa-xmark p-1"></i>
                    </button>

                    {isLoading ? (
                      <div className="w-full h-[520px] flex items-center justify-center">
                        <Loading />
                      </div>
                    ) : (
                      <div className="py-5 flex flex-col  justify-between w-full h-full">
                        <div>
                          <div className="w-32 h-32 md:w-52 md:h-52 mx-auto md:mb-5">
                            <img
                              src={
                                user?.photo ||
                                "https://www.w3schools.com/howto/img_avatar.png"
                              }
                              alt={
                                user?.display_name ||
                                user?.student_name ||
                                "Avatar"
                              }
                              // className={`rounded-full ${
                              //   user?.photo ? "relative bottom-11" : ""
                              // }`}
                              className="rounded-full object-contain size-32 md:size-52"
                            />
                          </div>

                          <h1 className="my-0 font-bold text-mobile-20 lg:text-tablet-34 w-full capitalize">
                            {user?.student_name ||
                              user?.display_name ||
                              "[Enter your name]"}
                          </h1>
                          <div className="flex gap-3 mt-3 w-full ml-0">
                            <span className="my-0 text-xs md:text-mobile-14  text-primary-6 uppercase border border-primary-6 rounded px-2 py-1 hover:-translate-y-0.5 hover:shadow duration-200 transtion">
                              {user?.student_major || "[Enter your major...]"}
                            </span>
                            <span className="my-0 text-xs md:text-mobile-14  text-grey-4 uppercase border border-grey-4 rounded px-2 py-1 hover:-translate-y-0.5 hover:shadow duration-200 transtion">
                              Matric No:{" "}
                              {user?.matric_no || "[Enter your matric no...]"}
                            </span>
                          </div>
                          <ReadMore
                            className="w-full text-justify text-gray-800 mt-5 mb-5 whitespace-pre-wrap"
                            limit={50}
                          >
                            {user?.desc || "[Tell us about yourself...]"}
                          </ReadMore>
                        </div>

                        <div className="flex flex-row gap-8 justify-center">
                          <a
                            href={"mailto:" + user?.student_email}
                            target="_blank"
                            rel="noreferrer"
                            className="bg-black/80 rounded-full h-8 w-8 flex items-center justify-center hover:bg-black"
                            title="Email"
                          >
                            <i className="fa-solid fa-envelope text-white"></i>
                          </a>
                          <a
                            href={user?.github}
                            target="_blank"
                            rel="noreferrer"
                            className="bg-black/80 rounded-full h-8 w-8 flex items-center justify-center hover:bg-black"
                            title="Github"
                          >
                            <i className="fa-brands fa-github text-white"></i>
                          </a>
                          <a
                            href={user?.linkedin}
                            target="_blank"
                            rel="noreferrer"
                            className="bg-black/80 rounded-full h-8 w-8 flex items-center justify-center hover:bg-black"
                            title="LinkedIn"
                          >
                            <i className="fa-brands fa-linkedin text-white"></i>
                          </a>
                          {user?.portfolio && (
                            <a
                              href={user?.portfolio}
                              target="_blank"
                              rel="noreferrer"
                              className="bg-black/80 rounded-full h-8 w-8 flex items-center justify-center hover:bg-black"
                              title="Portfolio"
                            >
                              <i className="fa-solid fa-globe text-white"></i>
                            </a>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>,
          // @ts-ignore
          mainModal
        )}
      </>
    );
  });

export default ProfileModal;
