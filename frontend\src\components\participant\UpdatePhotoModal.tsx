import React, { useRef, useState, useEffect } from "react";
import { compressedImage } from "@/utils";
import <PERSON>ropper from "cropperjs";
import "cropperjs/dist/cropper.min.css";

type UpdatePhotoModalProps = {
  id: string;
  currentPhoto: string;
  onPhotoUpdate: (photo: File) => void;
};

const UpdatePhotoModal: React.FC<UpdatePhotoModalProps> = ({
  id,
  currentPhoto,
  onPhotoUpdate,
}) => {
  const [previewImage, setPreviewImage] = useState<string>(currentPhoto);
  const [originalImage, setOriginalImage] = useState<string>(currentPhoto);
  const [isCropping, setIsCropping] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [cropper, setCropper] = useState<Cropper | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  useEffect(() => {
    // Clean up cropper instance when component unmounts
    return () => {
      if (cropper) {
        cropper.destroy();
        setCropper(null);
      }
    };
  }, [cropper]);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (5MB max)
      if (file.size > 5000000) {
        alert("File size is too large. Please select an image under 5MB.");
        return;
      }

      // Check file type
      if (!file.type.match("image.*")) {
        alert("Please select an image file.");
        return;
      }

      setSelectedFile(file);

      // Preview the image
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          const imageDataUrl = e.target.result as string;
          setOriginalImage(imageDataUrl); // Store the original image
          setPreviewImage(imageDataUrl);
          setIsCropping(true);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const initCropper = () => {
    if (imageRef.current && !cropper) {
      const newCropper = new Cropper(imageRef.current, {
        aspectRatio: 1,
        viewMode: 1,
        dragMode: "move",
        cropBoxMovable: true,
        cropBoxResizable: true,
        guides: true,
        background: true,
        center: true,
        highlight: false,
        autoCropArea: 0.8,
        responsive: true,
        minCropBoxWidth: 100,
        minCropBoxHeight: 100,
        toggleDragModeOnDblclick: false,
        ready: function () {
          // Get the crop box element safely
          const cropperContainer = imageRef.current?.parentElement;
          if (!cropperContainer) return;
          const cropBox = cropperContainer.querySelector(".cropper-crop-box");
          if (!cropBox) return;

          // Remove any existing overlays to avoid duplicates
          const oldOverlay = cropBox.querySelector(".cropper-circle-overlay");
          if (oldOverlay) oldOverlay.remove();

          // Add a circular overlay to the crop box
          const overlay = document.createElement("div");
          overlay.className = "cropper-circle-overlay";
          Object.assign(overlay.style, {
            position: "absolute",
            top: "0",
            left: "0",
            width: "100%",
            height: "100%",
            border: "1px solid white",
            borderRadius: "50%",
            pointerEvents: "none",
            boxSizing: "border-box",
            zIndex: "10",
          });
          cropBox.appendChild(overlay);
        },
      });
      setCropper(newCropper);
    }
  };

  useEffect(() => {
    if (isCropping) {
      // Initialize cropper after the image is loaded
      setTimeout(initCropper, 100);
    }
  }, [isCropping]);

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleCropComplete = () => {
    if (cropper && selectedFile) {
      // Get a square crop
      const canvas = cropper.getCroppedCanvas({
        width: 256,
        height: 256,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: "high",
      });

      // Create a new canvas for the circular crop
      const circularCanvas = document.createElement("canvas");
      const ctx = circularCanvas.getContext("2d");

      // Set dimensions
      circularCanvas.width = 256;
      circularCanvas.height = 256;

      if (ctx) {
        // Create circular clipping path
        ctx.beginPath();
        ctx.arc(128, 128, 128, 0, Math.PI * 2);
        ctx.closePath();
        ctx.clip();

        // Draw the image
        ctx.drawImage(canvas, 0, 0, 256, 256);

        // Optional: Add a white circle border
        ctx.strokeStyle = "white";
        ctx.lineWidth = 2;
        ctx.stroke();
      }

      // Convert to blob and create file
      circularCanvas.toBlob(
        (blob) => {
          if (blob) {
            const croppedFile = new File([blob], selectedFile.name, {
              type: "image/jpeg",
            });
            setPreviewImage(circularCanvas.toDataURL());
            setSelectedFile(croppedFile);
            onPhotoUpdate(croppedFile);
            setIsCropping(false);
            if (cropper) {
              cropper.destroy();
              setCropper(null);
            }
          }
        },
        "image/jpeg",
        0.95
      );
    }
  };

  const cancelCrop = () => {
    setIsCropping(false);
    if (cropper) {
      cropper.destroy();
      setCropper(null);
    }
  };

  const startCropping = () => {
    // Use the original image for cropping
    setPreviewImage(originalImage);
    setIsCropping(true);
  };

  // Add this function to handle Save Changes
  const handleSaveChanges = () => {
    if (selectedFile && !isCropping) {
      onPhotoUpdate(selectedFile);
      // Close the dialog programmatically
      const dialog = document.getElementById(id) as HTMLDialogElement | null;
      if (dialog) dialog.close();
    }
  };

  return (
    <dialog
      id={id}
      className="daisy-modal daisy-modal-bottom sm:daisy-modal-middle"
    >
      <div className="daisy-modal-box">
        <div className="daisy-modal-action sticky top-0 right-0 md:top-2 md:right-2 float-right">
          <form method="dialog">
            <button
              className="daisy-btn daisy-btn-sm daisy-btn-circle"
              onClick={() => setIsCropping(false)}
            >
              ✕
            </button>
          </form>
        </div>
        <h3 className="font-semibold text-mobile-18 md:text-xl mb-4">
          {isCropping ? "Crop Profile Photo" : "Update Profile Photo"}
        </h3>

        <div className="flex flex-col items-center justify-center py-4">
          {isCropping ? (
            <>
              <div className="w-full max-w-md mb-6 overflow-hidden">
                <img
                  ref={imageRef}
                  src={previewImage}
                  alt="Crop Preview"
                  className="max-w-full"
                />
              </div>
              <div className="flex gap-4">
                <button
                  onClick={cancelCrop}
                  type="button"
                  className="border border-primary-6 text-primary-6 px-4 py-2 rounded transition duration-200 hover:bg-primary-1"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCropComplete}
                  type="button"
                  className="bg-primary-6 hover:bg-primary-7 text-white px-4 py-2 rounded transition duration-200"
                >
                  Apply Crop
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="relative mb-6 group">
                <img
                  src={previewImage}
                  alt="Profile Preview"
                  className="rounded-full object-contain size-40 border-2 border-grey-2"
                />
                {selectedFile && (
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
                    <div
                      className="bg-grey-4 bg-opacity-60 text-white px-2 py-1 rounded text-sm cursor-pointer"
                      onClick={startCropping}
                    >
                      Crop Photo
                    </div>
                  </div>
                )}
              </div>

              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleFileChange}
              />

              <div className="flex gap-4">
                <button
                  onClick={triggerFileInput}
                  type="button"
                  className="bg-primary-6 hover:bg-primary-7 text-white px-4 py-2 rounded transition duration-200"
                >
                  Select New Photo
                </button>

                <button
                  type="button"
                  className="border border-primary-6 text-primary-6 px-4 py-2 rounded transition duration-200 hover:bg-primary-1"
                  onClick={handleSaveChanges}
                >
                  Save Changes
                </button>
              </div>
            </>
          )}
        </div>
      </div>
      <form method="dialog" className="daisy-modal-backdrop">
        <button type="button">close</button>
      </form>
    </dialog>
  );
};

export default UpdatePhotoModal;
