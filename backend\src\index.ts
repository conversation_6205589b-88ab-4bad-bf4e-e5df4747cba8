import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { Router } from './routes/Router';
import { MiddleWare } from './routes/Middleware';
import { cache } from 'hono/cache';
import { logger } from 'hono/logger'

const app = new Hono();
const origins = ['http://localhost:4321', 'https://pixelusm.com'];

// Function to dynamically match subdomains under pixel2024.pages.dev at https://(branch-name).pixel2024.pages.dev
const dynamicOrigin = /^https:\/\/[a-zA-Z0-9-]+\.pixel2024\.pages\.dev(\/.*)?$/;

const corsConfig = {
	origin: (origin: string) => {
		return origins.includes(origin) || dynamicOrigin.test(origin) ? origin : null;
	},
	allowHeaders: ['X-Custom-Header', 'Upgrade-Insecure-Requests', 'Content-Type', 'Origin', 'X-CSRF-Token'],
	allowMethods: ['POST', 'GET', 'PUT', 'DELETE', 'PATCH'],
	exposeHeaders: ['Content-Length', 'X-Kuma-Revision', 'Origin'],
	maxAge: 600,
	credentials: true,
};

app.use('*', cors(corsConfig));
app.use(logger())
app.get('/', (c) => c.text('Hello Welcome to the PIXEL 2024 TEST API!'));
app.get('/testbackend', (c) => c.text('Connected to PIXEL Backend'));

app.use('/dev/*', async (c, next) => await MiddleWare(c, next));
app.use('/prod/*', async (c, next) => await MiddleWare(c, next));

const cacheConfig = {
	cacheName: 'hono',
	cacheControl: 'max-age=1800', // expire in 1 hour
};

const routes = [
	{ path: 'lecturer-committees', cache: true },
	{ path: 'student-committees', cache: true },
	{ path: 'sponsors', cache: true },
	{ path: 'partners', cache: true },
	{ path: 'participants', cache: false }, //true when the submission is closed
	{ path: 'projects', cache: false }, // true when the submission is closed
	{ path: 'judges', cache: false },
	{ path: 'evaluations', cache: false },
	{ path: 'supervisors', cache: true },
];

routes.forEach((route) => {
	// cache the routes we are fetching only
	if (route.cache) {
		app.get(`/dev/${route.path}`, cache(cacheConfig));
		app.get(`/prod/${route.path}`, cache(cacheConfig));
	}
	app.route(`/dev/${route.path}`, Router);
	app.route(`/prod/${route.path}`, Router);
});

export default app;
