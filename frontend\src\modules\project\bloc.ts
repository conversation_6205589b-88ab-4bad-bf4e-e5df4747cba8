// import { ProjectApi } from "./api";
import type { Project } from "./types";
import {
  Timestamp,
  updateDoc,
  getDoc,
  addDoc,
  where,
  query,
  getDocs,
  setDoc,
} from "firebase/firestore";
import {
  widget,
  sanitizeData,
  firebaseStorage,
  actionSessionStorage,
  mapProject,
  mapSdg,
} from "@/utils";
import { proxyApi } from "@/utils/proxyApi";
import { Team, teamFactory } from "../team";
import { EvaluationData } from "../judge";
// import { mode } from "crypto-js";
import { ParticipantSession } from "..";

const currentYear = import.meta.env.PUBLIC_PIXEL_YEAR;

export function projectFactory() {
  // const projectApi = new ProjectApi();

  async function getProjects(args: {
    major?: string;
    query?: string;
    year?: string;
    isVideoQC?: boolean;
    isNotDraft?: boolean;
    filterCurrentYear?: boolean;
    signal?: AbortSignal;
  }): Promise<Project[]> {
    const { major, query, year, isVideoQC, isNotDraft, filterCurrentYear, signal } =
      args;
    const filters: string[] = [];

    if (year) {
      filters.push(`{year} = "${year}"`);
    }

    if (major && major !== "All") {
      filters.push(`{project_major} = "${major}"`);
    }

    if (query) {
      filters.push(`OR(
        FIND("${query}", {project_name}),
        FIND("${query}", {tech_stack})
      )`);
    }

    if (isVideoQC) {
      filters.push(`{video_qc} != ""`);
    }

    if (isNotDraft) {
      filters.push(`NOT({isDraft})`);
    }

    if (filterCurrentYear) {
      // if filterCurrentYear is true, we only want to fetch projects that are not from the current year
      filters.push(`NOT({year} = "${currentYear}")`);
    }

    const filterFormula =
      filters.length > 1 ? `AND(${filters.join(",")})` : filters[0];

    let offset = null;
    let allRecords: Project[] = [];

    try {
      do {
        const response = (await proxyApi.get("projects", {
          filterByFormula: filterFormula,
          offset,
        })) as { data: Project[]; offset?: string };

        // If response is an array, just concat it directly
        if (Array.isArray(response)) {
          // if result.offset has value, meaning to say there are more records to fetch
          allRecords = allRecords.concat(response);
        } else if (Array.isArray(response.data)) {
          allRecords = allRecords.concat(response.data);
          offset = response.offset || null;
        } else {
          // no more records to fetch after this
          allRecords = allRecords.concat(response.data);
          break;
        }

        offset = response.offset;
      } while (offset);
    } catch (error: any) {
      throw new Error("Error in getProjects: " + error);
    }

    return (allRecords as any).map(
      (response: any) => mapProject(response) as Project
    );
  }

  async function getProject(
    project_id: string,
    queryObject?: any
  ): Promise<Project> {
    let data = {} as Project;

    try {
      if (project_id === undefined || project_id === "") {
        return;
      }

      const projectData = await proxyApi.first("projects", project_id);
      const data = mapProject(projectData);

      // if (data.evaluation_id != null) {
      //   // using map
      //   const evaluations = await Promise.all(
      //     data.evaluation_id.map(async (id: string) => {
      //       const data = await proxyApi.first("evaluations", id); // retrieve every evaluation information
      //       return data;
      //     })
      //   );
      //   // evaluation: all evaluation details
      //   // data: project details
      //   // assign the feedbacks once again to the project data(Update)
      //   data.comments = evaluations.map(
      //     (evaluation: any) => evaluation.comments
      //   );
      // }
      // console.log("Return project info with every evaluation data: ", data);
      return data;
    } catch (error) {
      throw new Error("Error in getProject: " + error);
    }

    // if (data.airtable_id) {
    // fetch project image from airtable
    // const { name, ...rest } = await firstProjectAirtable(data.airtable_id);

    // fetch team data from firebase
    // const team = await teamFactory().getTeam("", { project_id: data.airtable_id });

    // note: pixel 2024 not using this anymore as airtable can fetch members_photo
    // set the teamDoc data to state
    // const teamData = {
    // 	...team,
    // 	members_urls: await teamFactory().fetchTeamImage(
    // 		team.members_id
    // 	),
    // };

    // data = {
    //   ...data,
    //   ...rest,
    //   team: teamData,
    // };

    // fetch evaluation data from firebase
    // await getEvaluations(data.airtable_id).then((evaluations) => {
    // 	data.feedbacks = evaluations.map(
    // 		(evaluation: any) => evaluation.comments
    // 	);
    // });

    // }

    return data;
  }

  async function updateProject(data: any, id: string): Promise<void> {
    data["updated_at"] = Timestamp.now();
    data = sanitizeData(data);

    delete data["thumbnail"];
    delete data["presentation_slide"];
    delete data["highlights"];
    delete data["tags"];

    // note: not using firebase for this
    // await updateDoc(projectApi.projectDoc(id), data);
  }

  async function submitProject(
    // team_id: string,
    userId: string, // for updated by
    data: any,
    pageMode: string, // create or update
    projectId?: string
  ) {
    // if solo, create a new row in airtable first
    if (pageMode == "create" && projectId == "") {
      projectId = await createAirtableProject({ members_id: [userId] });
    }

    let filename = "";
    if (data.project_name !== undefined && data.project_name !== "") {
      filename = data.project_name.split(" ").join("_");
    }

    // upload thumbnail to firebase storage and get public url
    if (typeof data.thumbnail !== "string") {
      data.thumbnail = await firebaseStorage.getPublicUrlFirebase(
        data.thumbnail,
        "projects/" + projectId + "/" + filename + "_thumbnail"
      );
    }

    // upload slide to firebase storage and get public url
    if (data.slide) {
      if (typeof data.slide !== "string") {
        data.slide = await firebaseStorage.getPublicUrlFirebase(
          data.slide,
          "projects/" + projectId + "/" + filename + "_slide"
        );
      }
    }

    // upload images to firebase storage and get public url
    let highlightsUrls: string[] = [];
    for (let i = 0; i < data.highlights.length; i++) {
      let url: string = "";
      if (typeof data.highlights[i] !== "string")
        url = await firebaseStorage.getPublicUrlFirebase(
          data.highlights[i],
          "projects/" + projectId + "/" + filename + "_highlight" + i
        );
      else url = data.highlights[i];
      highlightsUrls.push(url);
    }

    data.highlights = highlightsUrls;

    const sdg = mapSdg(data.sdg);

    // TODO: project code will be generated by scripting in airtable
    // const formData = {
    //   // fields: {
    //   // name: data.name,
    //   // code: data.name + "123",
    //   // thumbnail: [{ url: thumbnailUrl }],
    //   // highlights: highlightsUrls.map((url) => ({ url })),
    //   // tags: data.tags,
    //   // major: data.major,
    //   members_id: data.members_id,
    //   // members_firebaseId: data.members_firebaseId,
    //   // firebase_id: projectId ? projectId : "",

    //   project_name: data.project_name,
    //   project_desc: data.project_desc,
    //   project_major: data.project_major,
    //   sdg: sdg,

    //   supervisor_id: [data.supervisor.id],
    //   // supervisor_name: [data.supervisor.name],
    //   // profile_link: data.supervisor.profile_link,
    //   slide: slidePublicUrl ?? "",
    //   tech_stack: data.tech_stack,
    //   thumbnail: thumbnailUrl,
    //   highlights: highlightsUrls,
    //   video: data.video,
    //   year: "2024",

    //   updated_by: pageMode == "create" ? "" : userId,
    //   // team: data.team,
    //   // },
    // } as any;

    try {
      if (
        pageMode === "create" &&
        (actionSessionStorage().get("userData").project_id === "" ||
          actionSessionStorage().get("userData").project_id === undefined)
      ) {
        // data.airtable_id = await createAirtableProject();

        // delete data["tags"];
        // data.projectId = await createProject(data);

        // update team data
        // const prevTeam = (await teamFactory().getTeam(projectId)) as Team;
        // if (!prevTeam) {
        //   await widget.alertError(
        //     "Error",
        //     "Something went wrong. Please try again later"
        //   );
        //   return;
        // }

        // submit the project data to the row created in airtable
        await updateAirtableProject(data, projectId);

        // prevTeam["projectId"] = data.projectId;
        // await teamFactory().updateTeam(prevTeam, projectId);

        // insert firebase id to airtable
        // data["firebase_id"] = data.projectId;
        // console.log(projectId);

        // if (projectId == "") {
        //   console.log("run here");

        //   const id = (await createProject(data)) as string;
        //   //  console.log(id);
        //   return id;
        // } else {
        //   console.log("run here 2");
        //   //  console.log("run here");
        //   await updateAirtableProject(data, projectId);
        // }
      } else {
        //updating project
        //  console.log("run here");
        await updateAirtableProject(data, projectId);
        // await updateProject(data, project_id);
      }

      await actionSessionStorage().update("userData", {
        project_name: data.project_name,
        project_major: data.project_major,
        sdg: sdg,
        project_id: projectId,
      });
    } catch (error) {
      widget.alertError("Error", error.message);
      throw new Error("Error in submitProject: " + error);
    }

    // await new Promise((resolve) => {
    //   setTimeout(async () => {
    //     //slide
    //     if (typeof data.presentation_slide !== "string")
    //       await firebaseStorage.deleteImageFirebase(filename + "_slide");
    //     //thumbnail
    //     if (typeof data.thumbnail !== "string")
    //       await firebaseStorage.deleteImageFirebase(filename + "_thumbnail");
    //     for (let i = 0; i < data.highlights.length; i++) {
    //       if (typeof data.highlights[i] !== "string")
    //         await firebaseStorage.deleteImageFirebase(
    //           filename + "_highlight" + i
    //         );
    //     }

    //     resolve(null);
    //   }, 3000);
    // });

    // Successful
  }

  /**
   * get all evaluations of a project
   * @param id project id
   */
  // note: pixel 2024 not using anymore
  // async function getEvaluations(id: string) {
  // const querySnapshot = await getDocs(projectApi.evaluationCollection(id));
  // let data: any = [];
  // querySnapshot.forEach((doc) => {
  // 	data.push(doc.data());
  // });
  // return data;
  // }

  async function createProject(data: any): Promise<string | void> {
    try {
      const response = await proxyApi.post("projects", data, true);
      return response.id;
    } catch (error) {
      widget.alertError(
        "Error",
        "Something wrong while creating the project",
        error
      );
      throw new Error("Error in createProject: " + error);
    }
  }

  async function deleteProject(
    id: string,
    userData: ParticipantSession
  ): Promise<void> {
    try {
      await proxyApi.delete("projects", id);
      // update session storage
      actionSessionStorage().update("userData", {
        ...userData,
        project_id: "",
        project_name: "",
        project_major: "",
        sdg: "",
        isDraft: false,
      });
    } catch (error) {
      widget.alertError("Error", "Failed to delete project", error);
      throw new Error("Error in deleteProject: " + error);
    }
  }

  // create a new airtable record
  async function createAirtableProject(data: any): Promise<string> {
    try {
      const { id } = await proxyApi.post("projects", data);
      return id;
    } catch (error) {
      throw new Error("Error in createAirtableProject: " + error);
    }
  }

  async function updateAirtableProject(data: any, project_id: string) {
    //  console.log("data: ", data);

    try {
      await proxyApi.patch("projects", data, project_id, true);
    } catch (error) {
      throw new Error("Error in updateAirtableProject: " + error);
    }
  }

  async function firstProjectAirtable(id: string) {
    // return await api.first("/projects", id).then(async (response) => {
    //   return {
    //     ...response.fields,
    //     highlights:
    //       response.fields.highlights && response.fields.highlights.length > 0
    //         ? [...response.fields.highlights]
    //         : [],
    //   };
    // });
  }

  return {
    createProject,
    updateProject,
    getProjects,
    getProject,
    submitProject,
    deleteProject,
    // getEvaluations,
  };
}
