/** @type {import('tailwindcss').Config} */

import { fontFamily as _fontFamily } from "tailwindcss/defaultTheme";

export default {
  content: [
    "./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}",
    "./node_modules/flowbite/**/*.js",
  ],
  plugins: [require("daisyui"), require("flowbite/plugin"), require('@tailwindcss/typography')],
  theme: {
    fontFamily: {
      poppins: ["Poppins", ..._fontFamily.sans],
      inter: ["Inter", ..._fontFamily.sans],
    },
    fontWeight: {
      light: "300",
      normal: "400",
      medium: "500",
      semibold: "600",
      bold: "700",
    },
    screens: {
      xs: "321px",
      sm: "480px",
      md: "768px",
      lg: "992px",
      xl: "1250px",
    },
    extend: {
      screens: {
        "2xl": "1500px",
      },
      fontSize: {
        // For desktop
        "desktop-96": [
          "6rem",
          {
            lineHeight: "90%",
            fontWeight: "600",
          },
        ],
        "desktop-60": [
          "3.75rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "desktop-48": [
          "3rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "desktop-34": [
          "2.125rem",
          {
            lineHeight: "120%",
            fontWeight: "600",
          },
        ],
        "desktop-24": [
          "1.5rem",
          {
            lineHeight: "130%",
            fontWeight: "600",
          },
        ],
        "desktop-20": [
          "1.25rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
        "desktop-16": [
          "1rem",
          {
            lineHeight: "160%",
            fontWeight: "400",
          },
        ],
        "desktop-14": [
          "0.875rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
        "desktop-12": [
          "0.75rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
        // For tablet
        "tablet-72": [
          "4.5rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "tablet-48": [
          "3rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "tablet-34": [
          "2.125rem",
          {
            lineHeight: "120%",
            fontWeight: "600",
          },
        ],
        "tablet-24": [
          "1.5rem",
          {
            lineHeight: "130%",
            fontWeight: "600",
          },
        ],
        "tablet-20": [
          "1.25rem",
          {
            lineHeight: "130%",
            fontWeight: "600",
          },
        ],
        "tablet-16": [
          "1rem",
          {
            lineHeight: "150%",
            fontWeight: "400",
          },
        ],
        "tablet-14": [
          "0.875rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
        "tablet-12": [
          "0.75rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
        // For mobile:
        "mobile-48": [
          "3rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "mobile-34": [
          "2.125rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "mobile-24": [
          "1.5rem",
          {
            lineHeight: "100%",
            fontWeight: "600",
          },
        ],
        "mobile-20": [
          "1.25rem",
          {
            lineHeight: "130%",
            fontWeight: "600",
          },
        ],
        "mobile-18": [
          "1.125rem",
          {
            lineHeight: "110%",
            fontWeight: "400",
          },
        ],
        "mobile-16": [
          "1rem",
          {
            lineHeight: "150%",
            fontWeight: "400",
          },
        ],
        "mobile-14": [
          "0.875rem",
          {
            lineHeight: "130%",
            fontWeight: "400",
          },
        ],
      },
      colors: {
        bgColor: "#FFFBF8",
        textColor: "#160F08",
        muteColor: "#7B7B7B", // use when feature is disabled

        // alert color
        dangerColor: "#E85D64",
        dangerColorHover: "#D74850",
        successColor: '#42b654',
        successColorHover: '#369745',
        infoColor: '#00b5ff',

        // color groups
        black: "#160F08",
        white: "#FEFDF1",

        grey: {
          1: "#1A10070D",
          2: "#1A100733",
          3: "#1A100766",
          4: "#1A100799",
          5: "#1A1007CC",
        },
        // PIXEL 2025
        // primary: { // orange colour
        // 	1: '#FCF3EB',
        // 	2: '#F6DAC1',
        // 	3: '#F2C8A3',
        // 	4: '#ECAF7A',
        // 	5: '#E99F60',
        // 	6: '#E38738',
        // 	7: '#CF7B33',
        // 	"linear": "linear-gradient(0deg, #C75824, #E38738)"
        // },
        primary: {
          // orange colour
          1: "#F9F1C8", // very light orange
          2: "#F0DD99", // light orange
          3: "#F2D551", // light medium orange
          4: "#EFC15B", // medium orange
          5: "#BF991B", // medium dark orange
          6: "#9D7F0B", // dark orange
          7: "#6B5900", // very dark orange
          linear: "linear-gradient(0deg, #6B5900, #BF991B)", // gradient from dark orange to medium dark orange (using layout.css version)
        },

        "green-se": {
          1: "#8CFF91",
          2: "#18C121",
        },
        "red-ic": {
          1: "#FF695F",
          2: "#FF1100",
        },
        // "purple-ci": {
        //   1: "#D770FC",
        //   2: "#BD00FF",
        // },
        // "orange-ci": {
        // 	1: "#FFA770", // light orange
        // 	2: "#FF8C00", // dark orange
        // }
        "blue-ci": {
          1: "#70A1FC",
          2: "#3366FF",
        },
      },
    },
  },
  daisyui: {
    prefix: "daisy-",
    themes: ["light"]
  },
};
