// Connect to the firebase and retrieve users information
/* 
 * @prerequisites
 * - Firebase service account JSON file located at '../service-account.json'
*/

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
try {
  // Initialize the app with service account
  admin.initializeApp({
    credential: admin.credential.cert(path.join(__dirname, 'service-account.json'))
  });

  console.log('Firebase Admin SDK initialized successfully!');

  // Test function to get user info
  async function testFirebaseConnection() {
    try {
      // Get a list of users (limited to 10 for testing)
      const listUsersResult = await admin.auth().listUsers(10);
      console.log('\nSuccessfully retrieved users:');
      listUsersResult.users.forEach((userRecord) => {
        console.log('User:', userRecord.toJSON());
      });
      
      return true;
    } catch (error) {
      console.error('Error testing Firebase connection:', error);
      return false;
    }
  }

  // Run the test
  testFirebaseConnection()
    .then((success) => {
      if (success) {
        console.log('\nFirebase connection test completed successfully!');
      } else {
        console.log('\nFirebase connection test failed!');
      }
    })
    .catch((error) => {
      console.error('Unexpected error:', error);
    });

} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
} 